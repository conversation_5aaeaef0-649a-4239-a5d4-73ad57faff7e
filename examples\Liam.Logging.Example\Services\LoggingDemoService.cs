using <PERSON>.Logging.Constants;
using <PERSON>.Logging.Interfaces;

namespace Liam.Logging.Example.Services;

/// <summary>
/// 日志记录演示服务实现
/// 演示基本的日志记录功能
/// </summary>
public class LoggingDemoService : ILoggingDemoService
{
    private readonly ILiamLogger _logger;

    /// <summary>
    /// 初始化日志记录演示服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public LoggingDemoService(ILiamLogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 演示多级别日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateLogLevelsAsync()
    {
        Console.WriteLine("=== 多级别日志记录演示 ===\n");

        // 演示所有日志级别
        Console.WriteLine("1. 演示所有日志级别:");
        
        _logger.LogTrace("这是一条跟踪日志 - 用于详细的调试信息");
        await Task.Delay(100);
        
        _logger.LogDebug("这是一条调试日志 - 用于开发时的调试信息");
        await Task.Delay(100);
        
        _logger.LogInformation("这是一条信息日志 - 用于记录程序的正常运行信息");
        await Task.Delay(100);
        
        _logger.LogWarning("这是一条警告日志 - 用于记录潜在的问题");
        await Task.Delay(100);
        
        _logger.LogError("这是一条错误日志 - 用于记录错误信息");
        await Task.Delay(100);
        
        _logger.LogCritical("这是一条严重错误日志 - 用于记录严重的系统错误");
        await Task.Delay(100);

        Console.WriteLine("\n2. 演示带参数的日志记录:");
        
        var userId = 12345;
        var userName = "张三";
        var operationTime = DateTime.Now;
        
        _logger.LogInformation($"用户登录: UserId={userId}, UserName={userName}, Time={operationTime:yyyy-MM-dd HH:mm:ss}");
        
        Console.WriteLine("\n3. 演示日志级别检查:");
        
        // 检查日志级别是否启用
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug("调试级别已启用，记录详细调试信息");
            Console.WriteLine("✓ Debug级别已启用");
        }
        else
        {
            Console.WriteLine("✗ Debug级别未启用");
        }
        
        if (_logger.IsEnabled(LogLevel.Information))
        {
            _logger.LogInformation("信息级别已启用，记录常规信息");
            Console.WriteLine("✓ Information级别已启用");
        }
        else
        {
            Console.WriteLine("✗ Information级别未启用");
        }

        Console.WriteLine("\n4. 演示条件日志记录:");
        
        var items = new[] { "项目1", "项目2", "项目3", "项目4", "项目5" };
        
        foreach (var item in items)
        {
            // 只有在Debug级别启用时才记录详细信息
            if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug($"处理项目: {item}");
            }
            
            // 模拟处理时间
            await Task.Delay(50);
        }
        
        _logger.LogInformation($"批量处理完成，共处理 {items.Length} 个项目");

        Console.WriteLine("\n5. 演示不同场景的日志记录:");
        
        // 模拟业务操作
        await SimulateBusinessOperationAsync();
        
        Console.WriteLine("\n=== 多级别日志记录演示完成 ===");
    }

    /// <summary>
    /// 演示异常日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateExceptionLoggingAsync()
    {
        Console.WriteLine("=== 异常日志记录演示 ===\n");

        Console.WriteLine("1. 演示简单异常记录:");
        
        try
        {
            // 故意触发异常
            ThrowDivideByZeroException();
        }
        catch (DivideByZeroException ex)
        {
            _logger.LogError("除零异常发生", ex);
            Console.WriteLine("✓ 除零异常已记录到日志");
        }

        Console.WriteLine("\n2. 演示嵌套异常记录:");
        
        try
        {
            await SimulateNestedExceptionAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError("嵌套异常发生", ex);
            Console.WriteLine("✓ 嵌套异常已记录到日志");
        }

        Console.WriteLine("\n3. 演示自定义异常记录:");
        
        try
        {
            throw new InvalidOperationException("这是一个自定义的业务异常")
            {
                Data = { { "UserId", 12345 }, { "Operation", "UpdateProfile" } }
            };
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogError("业务操作异常", ex);
            Console.WriteLine("✓ 自定义异常已记录到日志");
        }

        Console.WriteLine("\n4. 演示异常恢复记录:");
        
        var retryCount = 0;
        const int maxRetries = 3;
        
        while (retryCount < maxRetries)
        {
            try
            {
                await SimulateUnstableOperationAsync();
                _logger.LogInformation("不稳定操作成功完成");
                Console.WriteLine("✓ 操作成功");
                break;
            }
            catch (Exception ex)
            {
                retryCount++;
                if (retryCount < maxRetries)
                {
                    _logger.LogWarning($"操作失败，正在重试 ({retryCount}/{maxRetries})", ex);
                    Console.WriteLine($"⚠ 操作失败，重试中... ({retryCount}/{maxRetries})");
                    await Task.Delay(1000);
                }
                else
                {
                    _logger.LogError("操作最终失败，已达到最大重试次数", ex);
                    Console.WriteLine("✗ 操作最终失败");
                }
            }
        }

        Console.WriteLine("\n=== 异常日志记录演示完成 ===");
    }

    /// <summary>
    /// 模拟业务操作
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task SimulateBusinessOperationAsync()
    {
        _logger.LogInformation("开始执行业务操作");
        
        // 模拟数据库查询
        _logger.LogDebug("正在查询数据库...");
        await Task.Delay(200);
        _logger.LogDebug("数据库查询完成，返回 5 条记录");
        
        // 模拟数据处理
        _logger.LogDebug("正在处理数据...");
        await Task.Delay(300);
        _logger.LogInformation("数据处理完成");
        
        // 模拟缓存更新
        _logger.LogDebug("正在更新缓存...");
        await Task.Delay(100);
        _logger.LogDebug("缓存更新完成");
        
        _logger.LogInformation("业务操作执行完成");
    }

    /// <summary>
    /// 模拟嵌套异常
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task SimulateNestedExceptionAsync()
    {
        try
        {
            await Task.Run(() =>
            {
                throw new FileNotFoundException("配置文件未找到: config.json");
            });
        }
        catch (FileNotFoundException ex)
        {
            throw new InvalidOperationException("系统初始化失败", ex);
        }
    }

    /// <summary>
    /// 模拟不稳定操作
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task SimulateUnstableOperationAsync()
    {
        await Task.Delay(100);

        // 70% 的概率失败
        if (Random.Shared.NextDouble() < 0.7)
        {
            throw new TimeoutException("网络请求超时");
        }
    }

    /// <summary>
    /// 故意抛出除零异常用于演示
    /// </summary>
    private static void ThrowDivideByZeroException()
    {
        int zero = 0;
        var result = 10 / zero;
    }
}
