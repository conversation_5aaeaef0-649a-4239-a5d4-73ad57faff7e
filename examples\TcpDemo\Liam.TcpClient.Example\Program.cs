﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Liam.TcpClient.Extensions;
using Liam.Logging.Extensions;
using Liam.TcpClient.Example.Services;

namespace Liam.TcpClient.Example;

/// <summary>
/// Liam.TcpClient 示例程序
/// 演示TCP客户端的完整功能，包括：
/// - 基本TCP客户端连接和通信
/// - SSL/TLS安全连接
/// - 连接池管理
/// - 心跳检测和自动重连
/// - 性能监控和质量监控
/// - 并发连接测试
/// - 异常处理和日志记录
/// </summary>
class Program
{
    private static IHost? _host;
    private static ILogger<Program>? _logger;
    private static ClientDemoService? _demoService;

    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Liam.TcpClient 示例程序 ===");
        Console.WriteLine("演示TCP客户端的完整功能");
        Console.WriteLine();

        try
        {
            // 构建主机和依赖注入容器
            await BuildHostAsync();

            // 获取服务实例
            _logger = _host!.Services.GetRequiredService<ILogger<Program>>();
            _demoService = _host.Services.GetRequiredService<ClientDemoService>();

            _logger.LogInformation("Liam.TcpClient 示例程序启动");

            // 显示主菜单
            await ShowMainMenuAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"程序启动失败: {ex.Message}");
            if (_logger != null)
            {
                _logger.LogError(ex, "程序启动失败");
            }
        }
        finally
        {
            // 清理资源
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }
        }
    }

    /// <summary>
    /// 构建主机和依赖注入容器
    /// </summary>
    private static async Task BuildHostAsync()
    {
        var builder = Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                // 添加Liam.Logging服务
                services.AddLiamLogging(context.Configuration);

                // 添加Liam.TcpClient服务
                services.AddTcpClient(config =>
                {
                    config.ConnectionTimeoutSeconds = context.Configuration.GetValue<int>("TcpClient:ConnectTimeout", 30);
                    config.ReceiveBufferSize = context.Configuration.GetValue<int>("TcpClient:BufferSize", 4096);
                    config.SendBufferSize = context.Configuration.GetValue<int>("TcpClient:BufferSize", 4096);
                    config.EnableHeartbeat = context.Configuration.GetValue<bool>("TcpClient:EnableHeartbeat", true);
                    config.HeartbeatIntervalSeconds = context.Configuration.GetValue<int>("TcpClient:HeartbeatInterval", 30);
                    config.EnableAutoReconnect = context.Configuration.GetValue<bool>("TcpClient:EnableAutoReconnect", true);
                    config.ReconnectIntervalSeconds = context.Configuration.GetValue<int>("TcpClient:ReconnectInterval", 5);
                    config.MaxReconnectAttempts = context.Configuration.GetValue<int>("TcpClient:MaxReconnectAttempts", 10);
                });

                // 添加示例服务
                services.AddSingleton<ClientDemoService>();
            })
            .ConfigureLogging((context, logging) =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.AddDebug();
            });

        _host = builder.Build();
        await _host.StartAsync();
    }

    /// <summary>
    /// 显示主菜单
    /// </summary>
    private static async Task ShowMainMenuAsync()
    {
        while (true)
        {
            Console.WriteLine();
            Console.WriteLine("=== TCP客户端示例菜单 ===");
            Console.WriteLine("1. 基本连接测试");
            Console.WriteLine("2. SSL/TLS连接测试");
            Console.WriteLine("3. 连接池演示");
            Console.WriteLine("4. 心跳检测演示");
            Console.WriteLine("5. 自动重连演示");
            Console.WriteLine("6. 性能监控演示");
            Console.WriteLine("7. 并发连接测试");
            Console.WriteLine("8. 消息收发测试");
            Console.WriteLine("9. 查看客户端状态");
            Console.WriteLine("10. 断开所有连接");
            Console.WriteLine("0. 退出程序");
            Console.WriteLine();
            Console.Write("请选择功能 (0-10): ");

            var input = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(input))
                continue;

            try
            {
                switch (input.Trim())
                {
                    case "1":
                        await _demoService!.BasicConnectionTestAsync();
                        break;
                    case "2":
                        await _demoService!.SslConnectionTestAsync();
                        break;
                    case "3":
                        await _demoService!.ConnectionPoolDemoAsync();
                        break;
                    case "4":
                        await _demoService!.HeartbeatDemoAsync();
                        break;
                    case "5":
                        await _demoService!.AutoReconnectDemoAsync();
                        break;
                    case "6":
                        await _demoService!.PerformanceMonitoringDemoAsync();
                        break;
                    case "7":
                        await _demoService!.ConcurrentConnectionTestAsync();
                        break;
                    case "8":
                        await _demoService!.MessageTransferTestAsync();
                        break;
                    case "9":
                        await _demoService!.ShowClientStatusAsync();
                        break;
                    case "10":
                        await _demoService!.DisconnectAllAsync();
                        break;
                    case "0":
                        Console.WriteLine("正在退出程序...");
                        await _demoService!.DisconnectAllAsync();
                        return;
                    default:
                        Console.WriteLine("无效选择，请重新输入。");
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"操作失败: {ex.Message}");
                _logger?.LogError(ex, "菜单操作失败");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键继续...");
            Console.ReadKey();
        }
    }
}
