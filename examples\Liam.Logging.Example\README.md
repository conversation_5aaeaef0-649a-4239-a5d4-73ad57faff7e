# Liam.Logging.Example

## 项目概述

Liam.Logging.Example 是 Liam.Logging 库的完整示例程序，演示了日志记录库的所有核心功能和最佳实践。

## 功能特性

### 🎯 核心功能演示
- **多级别日志记录** - Trace, Debug, Information, Warning, Error, Critical
- **多种输出目标** - 控制台、文件、结构化输出
- **结构化日志记录** - 包含上下文信息和自定义属性
- **异步日志记录** - 高性能异步处理和批量操作
- **日志作用域** - 上下文信息管理和嵌套作用域
- **日志过滤和格式化** - 灵活的过滤条件和自定义格式
- **配置管理** - 从 appsettings.json 读取配置，支持环境特定配置
- **异常处理** - 完整的异常处理和错误恢复机制
- **性能监控** - 性能测试和吞吐量分析

### 🛠️ 技术特性
- **.NET 8.0** 目标框架
- **依赖注入** 完整集成
- **配置系统** 多环境支持
- **异步编程** 最佳实践
- **资源管理** 正确的生命周期管理
- **错误处理** 健壮的异常处理机制

## 快速开始

### 环境要求
- .NET 8.0 SDK
- Visual Studio 2022 或 VS Code

### 运行示例

1. **克隆项目并导航到示例目录**
   ```bash
   cd examples/Liam.Logging.Example
   ```

2. **还原依赖包**
   ```bash
   dotnet restore
   ```

3. **运行示例程序**

   **简单演示模式（推荐）：**
   ```bash
   dotnet run -- --simple
   ```

   **完整交互式演示：**
   ```bash
   dotnet run
   ```

   **基本功能测试：**
   ```bash
   dotnet run -- --test
   ```

   **查看帮助信息：**
   ```bash
   dotnet run -- --help
   ```

4. **选择演示功能**

   **简单演示模式**：自动运行所有核心功能的演示，无需用户交互

   **完整交互式演示**：程序启动后会显示交互式菜单，选择要演示的功能：
   ```
   ┌─────────────────────────────────────────────────────────────┐
   │                        主菜单                                │
   ├─────────────────────────────────────────────────────────────┤
   │  1. 多级别日志记录演示                                       │
   │  2. 结构化日志记录演示                                       │
   │  3. 异步日志记录演示                                         │
   │  4. 日志作用域演示                                           │
   │  5. 日志过滤和格式化演示                                     │
   │  6. 配置管理演示                                             │
   │  7. 异常处理演示                                             │
   │  8. 性能测试                                                 │
   │  9. 运行所有演示                                             │
   │  0. 退出程序                                                 │
   └─────────────────────────────────────────────────────────────┘
   ```

## 配置说明

### 配置文件结构

项目包含三个配置文件：

#### appsettings.json (基础配置)
```json
{
  "Logging": {
    "MinimumLevel": "Information",
    "EnableAsync": true,
    "AsyncQueueSize": 10000,
    "BatchSize": 100,
    "ApplicationName": "Liam.Logging.Example"
  }
}
```

#### appsettings.Development.json (开发环境)
```json
{
  "Logging": {
    "MinimumLevel": "Trace",
    "IncludeSourceInfo": true,
    "Environment": "Development"
  }
}
```

#### appsettings.Production.json (生产环境)
```json
{
  "Logging": {
    "MinimumLevel": "Warning",
    "IncludeSourceInfo": false,
    "Environment": "Production"
  }
}
```

### 环境切换

通过设置环境变量来切换配置：

**Windows (PowerShell)**
```powershell
$env:ASPNETCORE_ENVIRONMENT="Development"
dotnet run
```

**Linux/macOS**
```bash
export ASPNETCORE_ENVIRONMENT=Development
dotnet run
```

## 运行模式

### 1. 简单演示模式 (--simple)
**推荐新用户使用**

自动运行所有核心功能的演示，包括：
- 基本日志级别演示（Trace, Debug, Information, Warning, Error, Critical）
- 结构化日志记录演示（参数化消息、复杂对象）
- 异步日志记录演示（并发异步操作）
- 日志作用域演示（嵌套作用域、上下文信息）
- 异常日志记录演示（单一异常、嵌套异常）

**运行方式：**
```bash
dotnet run -- --simple
```

### 2. 基本功能测试 (--test)
**用于验证库的基本功能**

运行基本的功能测试，验证：
- 服务注册和依赖注入
- 日志提供程序配置
- 基本日志记录功能
- 异步日志记录
- 异常处理

**运行方式：**
```bash
dotnet run -- --test
```

### 3. 完整交互式演示（默认）
**适合深入了解各项功能**

提供完整的交互式菜单，可以选择性地演示各项功能：

**运行方式：**
```bash
dotnet run
```

## 演示功能详解

### 1. 多级别日志记录演示
- 展示所有日志级别的使用
- 演示日志级别过滤
- 条件日志记录
- 异常日志记录

### 2. 结构化日志记录演示
- 消息模板和参数
- 复杂对象序列化
- 自定义属性
- LogEvent 使用

### 3. 异步日志记录演示
- 基本异步日志
- 高并发场景
- 批处理机制
- 性能比较

### 4. 日志作用域演示
- 基本作用域使用
- 嵌套作用域
- 并发作用域
- 异常处理中的作用域

### 5. 日志过滤和格式化演示
- 级别过滤
- 类别过滤
- 消息内容过滤
- 自定义格式化

### 6. 配置管理演示
- 配置读取和验证
- 环境特定配置
- 配置优先级
- 动态配置监控

### 7. 异常处理演示
- 基本异常处理
- 嵌套异常
- 异步异常
- 错误恢复机制

### 8. 性能测试
- 同步 vs 异步性能比较
- 并发性能测试
- 内存使用分析
- 吞吐量测试

## 日志输出

### 控制台输出
程序运行时会在控制台显示彩色格式化的日志信息。

### 文件输出
日志文件保存在 `logs/` 目录下：
- `logs/app.log` - 主日志文件
- `logs/dev-app.log` - 开发环境日志
- `logs/prod-app.log` - 生产环境日志

### 日志轮转
文件日志支持自动轮转：
- 最大文件大小：10MB (开发) / 50MB (生产)
- 保留文件数：5 (开发) / 30 (生产)
- 轮转间隔：每天

## 性能指标

### 典型性能数据
- **同步日志**: ~50,000 条/秒
- **异步日志**: ~150,000 条/秒
- **批量处理**: ~200,000 条/秒
- **内存使用**: ~100 bytes/条消息

### 性能优化建议
1. 在生产环境使用异步日志记录
2. 适当设置批处理大小 (100-1000)
3. 根据需要调整日志级别
4. 启用日志过滤减少不必要的输出

## 最佳实践

### 1. 日志级别使用
- **Trace**: 非常详细的调试信息
- **Debug**: 开发时的调试信息
- **Information**: 常规运行信息
- **Warning**: 潜在问题提醒
- **Error**: 错误信息
- **Critical**: 严重系统错误

### 2. 结构化日志
```csharp
// 推荐：使用结构化日志
logger.LogInformation("用户 {UserId} 登录成功，IP: {IpAddress}", userId, ipAddress);

// 不推荐：字符串拼接
logger.LogInformation($"用户 {userId} 登录成功，IP: {ipAddress}");
```

### 3. 异常处理
```csharp
try
{
    // 业务逻辑
}
catch (Exception ex)
{
    logger.LogError("操作失败", ex);
    // 错误处理逻辑
}
```

### 4. 作用域使用
```csharp
using (logger.BeginScope(new { UserId = userId, Operation = "UpdateProfile" }))
{
    logger.LogInformation("开始更新用户资料");
    // 业务逻辑
    logger.LogInformation("用户资料更新完成");
}
```

## 故障排除

### 常见问题

1. **日志文件未创建**
   - 检查 `logs/` 目录权限
   - 确认配置文件中的路径设置

2. **性能问题**
   - 检查日志级别设置
   - 考虑启用异步日志记录
   - 调整批处理参数

3. **配置不生效**
   - 确认环境变量设置
   - 检查配置文件格式
   - 验证配置文件复制到输出目录

### 调试技巧

1. **启用详细日志**
   ```json
   {
     "Logging": {
       "MinimumLevel": "Trace",
       "IncludeSourceInfo": true
     }
   }
   ```

2. **检查配置加载**
   运行配置管理演示查看当前配置状态

3. **性能分析**
   运行性能测试获取详细的性能指标

## 扩展示例

### 自定义日志提供程序
参考项目中的提供程序实现，可以创建自定义的日志输出目标。

### 自定义过滤器
实现 `ILogFilter` 接口创建自定义的日志过滤逻辑。

### 集成第三方库
示例展示了如何与 Microsoft.Extensions.Hosting 和依赖注入容器集成。

## 相关资源

- [Liam.Logging 主项目](../../src/Liam.Logging/)
- [Liam.Logging API 文档](../../src/Liam.Logging/README.md)
- [NuGet 包](https://www.nuget.org/packages/Liam.Logging/)

## 许可证

本示例项目遵循 MIT 许可证。
