using Microsoft.Extensions.Logging;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;
using Liam.SerialPort.Example.Services;
using System.Text;

namespace Liam.SerialPort.Example.Demos;

/// <summary>
/// 数据处理器演示
/// 展示ISerialPortDataHandler接口的直接使用
/// </summary>
public class DataHandlerDemo
{
    private readonly ILogger<DataHandlerDemo> _logger;
    private readonly ISerialPortService _serialPortService;
    private readonly ISerialPortDataHandler _dataHandler;
    private readonly IMenuService _menuService;
    private readonly IPerformanceMonitor _performanceMonitor;

    /// <summary>
    /// 构造函数
    /// </summary>
    public DataHandlerDemo(
        ILogger<DataHandlerDemo> logger,
        ISerialPortService serialPortService,
        ISerialPortDataHandler dataHandler,
        IMenuService menuService,
        IPerformanceMonitor performanceMonitor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serialPortService = serialPortService ?? throw new ArgumentNullException(nameof(serialPortService));
        _dataHandler = dataHandler ?? throw new ArgumentNullException(nameof(dataHandler));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
    }

    /// <summary>
    /// 运行数据处理器演示
    /// </summary>
    public async Task<bool> RunAsync()
    {
        _menuService.ClearScreen();
        
        Console.ForegroundColor = ConsoleColor.DarkMagenta;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    数据处理器演示                            ║");
        Console.WriteLine("║  演示内容：直接读取、字符串读取、行读取、监听控制            ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();

        try
        {
            // 确保连接
            if (!await EnsureConnectionAsync())
            {
                return true;
            }

            // 1. 演示直接数据读取
            await DemonstrateDirectReadAsync();

            // 2. 演示字符串读取
            await DemonstrateStringReadAsync();

            // 3. 演示行读取
            await DemonstrateLineReadAsync();

            // 4. 演示十六进制发送
            await DemonstrateHexSendAsync();

            // 5. 演示监听控制
            await DemonstrateListeningControlAsync();

            // 6. 演示流式数据处理
            await DemonstrateStreamProcessingAsync();

            _menuService.ShowStatus("数据处理器演示完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据处理器演示过程中发生错误");
            _menuService.ShowStatus($"演示失败: {ex.Message}", StatusType.Error);
        }
        finally
        {
            // 停止监听
            _dataHandler.StopListening();
            
            if (_serialPortService.IsConnected)
            {
                await _serialPortService.DisconnectAsync();
            }
        }

        return true;
    }

    /// <summary>
    /// 确保串口连接
    /// </summary>
    private async Task<bool> EnsureConnectionAsync()
    {
        if (_serialPortService.IsConnected)
        {
            return true;
        }

        var ports = await _serialPortService.GetAvailablePortsAsync();
        var portList = ports.ToList();

        if (!portList.Any())
        {
            _menuService.ShowStatus("未发现可用串口", StatusType.Warning);
            return false;
        }

        var selectedPort = portList.First();
        var settings = SerialPortSettings.Default;

        var connected = await _serialPortService.ConnectAsync(selectedPort, settings);
        if (connected)
        {
            _menuService.ShowStatus($"已连接到 {selectedPort.PortName}", StatusType.Success);
        }

        return connected;
    }

    /// <summary>
    /// 演示直接数据读取
    /// </summary>
    private async Task DemonstrateDirectReadAsync()
    {
        Console.WriteLine("\n--- 直接数据读取演示 ---");
        
        // 先发送一些测试数据
        var testData = Encoding.UTF8.GetBytes("Hello DataHandler!");
        Console.WriteLine($"发送测试数据: {Encoding.UTF8.GetString(testData)}");
        await _serialPortService.SendAsync(testData);
        
        // 等待数据到达
        await Task.Delay(1000);
        
        try
        {
            // 读取指定字节数
            Console.WriteLine("1. 读取指定字节数 (10字节):");
            var operationId = _performanceMonitor.StartOperation("直接读取数据");
            var data = await _dataHandler.ReadAsync(10, TimeSpan.FromSeconds(5));
            _performanceMonitor.EndOperation(operationId, data.Length > 0);
            
            if (data.Length > 0)
            {
                Console.WriteLine($"读取到 {data.Length} 字节: {Encoding.UTF8.GetString(data)}");
                Console.WriteLine($"十六进制: {Convert.ToHexString(data)}");
                _performanceMonitor.RecordDataTransfer(data.Length, TransferDirection.Receive);
            }
            else
            {
                Console.WriteLine("未读取到数据");
            }
        }
        catch (TimeoutException)
        {
            Console.WriteLine("读取超时");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "直接读取数据失败");
            Console.WriteLine($"读取失败: {ex.Message}");
        }

        await Task.Delay(1000);

        try
        {
            // 读取所有可用数据
            Console.WriteLine("\n2. 读取所有可用数据:");
            var operationId = _performanceMonitor.StartOperation("读取所有数据");
            var allData = await _dataHandler.ReadAsync(-1, TimeSpan.FromSeconds(2)); // -1表示读取所有
            _performanceMonitor.EndOperation(operationId, allData.Length > 0);
            
            if (allData.Length > 0)
            {
                Console.WriteLine($"读取到 {allData.Length} 字节: {Encoding.UTF8.GetString(allData)}");
                _performanceMonitor.RecordDataTransfer(allData.Length, TransferDirection.Receive);
            }
            else
            {
                Console.WriteLine("缓冲区中无数据");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "读取所有数据失败");
            Console.WriteLine($"读取失败: {ex.Message}");
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示字符串读取
    /// </summary>
    private async Task DemonstrateStringReadAsync()
    {
        Console.WriteLine("\n--- 字符串读取演示 ---");
        
        // 发送测试字符串
        var testStrings = new[]
        {
            "String1",
            "测试中文",
            "Special@#$%"
        };

        foreach (var testString in testStrings)
        {
            Console.WriteLine($"发送测试字符串: {testString}");
            await _serialPortService.SendAsync(testString);
            
            await Task.Delay(500);
            
            try
            {
                var operationId = _performanceMonitor.StartOperation("字符串读取");
                var receivedString = await _dataHandler.ReadStringAsync(null, TimeSpan.FromSeconds(3));
                _performanceMonitor.EndOperation(operationId, !string.IsNullOrEmpty(receivedString));
                
                if (!string.IsNullOrEmpty(receivedString))
                {
                    Console.WriteLine($"读取到字符串: {receivedString}");
                    Console.WriteLine($"匹配: {receivedString == testString}");
                    _performanceMonitor.RecordDataTransfer(Encoding.UTF8.GetByteCount(receivedString), TransferDirection.Receive);
                }
                else
                {
                    Console.WriteLine("未读取到字符串");
                }
            }
            catch (TimeoutException)
            {
                Console.WriteLine("字符串读取超时");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "字符串读取失败");
                Console.WriteLine($"读取失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示行读取
    /// </summary>
    private async Task DemonstrateLineReadAsync()
    {
        Console.WriteLine("\n--- 行读取演示 ---");
        
        // 发送带不同换行符的测试行
        var testLines = new[]
        {
            ("Line1\r\n", "CRLF结尾"),
            ("Line2\n", "LF结尾"),
            ("Line3\r", "CR结尾"),
            ("Line4", "无换行符")
        };

        foreach (var (line, description) in testLines)
        {
            Console.WriteLine($"发送测试行: {description} - {line.Replace("\r", "\\r").Replace("\n", "\\n")}");
            await _serialPortService.SendAsync(line);
            
            await Task.Delay(500);
            
            try
            {
                var operationId = _performanceMonitor.StartOperation("行读取");
                var receivedLine = await _dataHandler.ReadLineAsync(null, TimeSpan.FromSeconds(3));
                _performanceMonitor.EndOperation(operationId, !string.IsNullOrEmpty(receivedLine));
                
                if (!string.IsNullOrEmpty(receivedLine))
                {
                    Console.WriteLine($"读取到行: {receivedLine}");
                    _performanceMonitor.RecordDataTransfer(Encoding.UTF8.GetByteCount(receivedLine), TransferDirection.Receive);
                }
                else
                {
                    Console.WriteLine("未读取到完整行");
                }
            }
            catch (TimeoutException)
            {
                Console.WriteLine("行读取超时");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "行读取失败");
                Console.WriteLine($"读取失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示十六进制发送
    /// </summary>
    private async Task DemonstrateHexSendAsync()
    {
        Console.WriteLine("\n--- 数据处理器十六进制发送演示 ---");
        
        var hexStrings = new[]
        {
            "48656C6C6F",      // "Hello"
            "41 54 0D 0A",     // "AT\r\n"
            "FF:FE:FD:FC",     // 用冒号分隔
            "01-02-03-04"      // 用短横线分隔
        };

        foreach (var hexString in hexStrings)
        {
            try
            {
                Console.WriteLine($"通过数据处理器发送十六进制: {hexString}");
                
                var operationId = _performanceMonitor.StartOperation("数据处理器十六进制发送");
                await _dataHandler.SendHexAsync(hexString);
                _performanceMonitor.EndOperation(operationId, true);
                
                Console.WriteLine("发送成功");
                
                // 尝试读取回显（如果有的话）
                await Task.Delay(500);
                try
                {
                    var echo = await _dataHandler.ReadAsync(-1, TimeSpan.FromSeconds(1));
                    if (echo.Length > 0)
                    {
                        Console.WriteLine($"收到回显: {Convert.ToHexString(echo)}");
                    }
                }
                catch (TimeoutException)
                {
                    // 没有回显是正常的
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据处理器十六进制发送失败");
                Console.WriteLine($"发送失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示监听控制
    /// </summary>
    private async Task DemonstrateListeningControlAsync()
    {
        Console.WriteLine("\n--- 监听控制演示 ---");
        
        // 注册数据接收事件
        var receivedData = new List<byte[]>();
        _dataHandler.DataReceived += (sender, e) =>
        {
            receivedData.Add(e.Data);
            Console.WriteLine($"[监听] 收到数据: {Encoding.UTF8.GetString(e.Data)} ({e.Data.Length} 字节)");
        };

        try
        {
            Console.WriteLine("1. 启动监听...");
            _dataHandler.StartListening();
            Console.WriteLine("监听已启动");
            
            // 发送一些测试数据
            var testMessages = new[] { "Message1", "Message2", "Message3" };
            
            foreach (var message in testMessages)
            {
                Console.WriteLine($"发送: {message}");
                await _serialPortService.SendAsync(message);
                await Task.Delay(1000); // 等待接收
            }
            
            Console.WriteLine($"通过监听接收到 {receivedData.Count} 条数据");
            
            await Task.Delay(2000);
            
            Console.WriteLine("\n2. 停止监听...");
            _dataHandler.StopListening();
            Console.WriteLine("监听已停止");
            
            // 停止监听后发送数据
            Console.WriteLine("发送数据 (监听已停止): StoppedMessage");
            await _serialPortService.SendAsync("StoppedMessage");
            await Task.Delay(1000);
            
            Console.WriteLine("监听停止后不应收到数据事件");
            
            Console.WriteLine("\n3. 重新启动监听...");
            _dataHandler.StartListening();
            Console.WriteLine("监听已重新启动");
            
            Console.WriteLine("发送数据 (监听已重启): RestartedMessage");
            await _serialPortService.SendAsync("RestartedMessage");
            await Task.Delay(1000);
        }
        finally
        {
            _dataHandler.StopListening();
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示流式数据处理
    /// </summary>
    private async Task DemonstrateStreamProcessingAsync()
    {
        Console.WriteLine("\n--- 流式数据处理演示 ---");
        
        Console.WriteLine("模拟连续数据流处理...");
        
        // 启动监听
        var processedPackets = 0;
        var totalBytes = 0;
        
        _dataHandler.DataReceived += (sender, e) =>
        {
            processedPackets++;
            totalBytes += e.Data.Length;
            
            if (processedPackets % 5 == 0)
            {
                Console.WriteLine($"已处理 {processedPackets} 个数据包，总计 {totalBytes} 字节");
            }
        };

        _dataHandler.StartListening();
        
        try
        {
            // 模拟连续发送数据流
            var random = new Random();
            var streamDuration = TimeSpan.FromSeconds(10);
            var startTime = DateTime.UtcNow;
            
            Console.WriteLine($"开始 {streamDuration.TotalSeconds} 秒的数据流处理...");
            
            while (DateTime.UtcNow - startTime < streamDuration)
            {
                // 生成随机大小的数据包
                var packetSize = random.Next(10, 100);
                var packet = new byte[packetSize];
                random.NextBytes(packet);
                
                // 添加包头和包尾标识
                var fullPacket = new List<byte> { 0xAA, 0xBB }; // 包头
                fullPacket.AddRange(packet);
                fullPacket.AddRange(new byte[] { 0xCC, 0xDD }); // 包尾
                
                await _serialPortService.SendAsync(fullPacket.ToArray());
                
                // 随机间隔
                await Task.Delay(random.Next(100, 500));
            }
            
            // 等待最后的数据处理
            await Task.Delay(2000);
            
            Console.WriteLine($"\n流式处理完成:");
            Console.WriteLine($"  处理数据包: {processedPackets} 个");
            Console.WriteLine($"  处理字节数: {totalBytes} 字节");
            Console.WriteLine($"  平均包大小: {(processedPackets > 0 ? totalBytes / processedPackets : 0)} 字节");
            Console.WriteLine($"  处理速率: {totalBytes / streamDuration.TotalSeconds:F2} 字节/秒");
        }
        finally
        {
            _dataHandler.StopListening();
        }
        
        await _menuService.WaitForKeyAsync();
    }
}
