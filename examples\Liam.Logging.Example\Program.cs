﻿using Liam.Logging.Example.Services;
using Liam.Logging.Extensions;
using Liam.Logging.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Liam.Logging.Example;

/// <summary>
/// Liam.Logging 示例程序主入口
/// 演示日志记录库的各种功能特性
/// </summary>
class Program
{
    /// <summary>
    /// 程序主入口点
    /// </summary>
    /// <param name="args">命令行参数</param>
    /// <returns>异步任务</returns>
    static async Task Main(string[] args)
    {
        // 检查命令行参数
        if (args.Length > 0)
        {
            switch (args[0])
            {
                case "--test":
                    await TestProgram.TestMain(args);
                    return;
                case "--simple":
                    await SimpleDemo.RunAsync();
                    return;
                case "--help":
                case "-h":
                    ShowHelp();
                    return;
            }
        }

        try
        {
            // 创建主机构建器
            var hostBuilder = CreateHostBuilder(args);
            using var host = hostBuilder.Build();

            // 启动主机服务
            await host.StartAsync();

            // 获取示例服务并运行
            var exampleService = host.Services.GetRequiredService<IExampleService>();
            await exampleService.RunAsync();

            // 停止主机服务
            await host.StopAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"程序运行出现异常: {ex.Message}");
            Console.WriteLine($"异常详情: {ex}");
            Environment.Exit(1);
        }
    }

    /// <summary>
    /// 创建主机构建器
    /// </summary>
    /// <param name="args">命令行参数</param>
    /// <returns>主机构建器</returns>
    static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                // 配置文件加载顺序：基础配置 -> 环境特定配置
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                      .AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json",
                                   optional: true, reloadOnChange: true)
                      .AddEnvironmentVariables()
                      .AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                // 注册Liam日志记录服务
                services.AddLiamLogging(context.Configuration, "Logging");

                // 添加控制台日志提供程序
                services.AddConsoleLogging(config =>
                {
                    config.EnableColors = true;
                    config.TimestampFormat = "yyyy-MM-dd HH:mm:ss.fff";
                });

                // 添加文件日志提供程序
                services.AddFileLogging(config =>
                {
                    config.FilePath = "logs/example.log";
                    config.EnableRotation = true;
                    config.MaxFileSize = 10 * 1024 * 1024; // 10MB
                    config.RetainedFileCount = 5;
                });

                // 注册示例服务
                services.AddTransient<IExampleService, ExampleService>();
                services.AddTransient<ILoggingDemoService, LoggingDemoService>();
                services.AddTransient<IPerformanceTestService, PerformanceTestService>();
                services.AddTransient<IConfigurationDemoService, ConfigurationDemoService>();
                services.AddTransient<IAsyncLoggingDemoService, AsyncLoggingDemoService>();
                services.AddTransient<IStructuredLoggingDemoService, StructuredLoggingDemoService>();
                services.AddTransient<IScopeLoggingDemoService, ScopeLoggingDemoService>();
                services.AddTransient<IFilteringDemoService, FilteringDemoService>();
                services.AddTransient<IExceptionHandlingDemoService, ExceptionHandlingDemoService>();
            });

    /// <summary>
    /// 显示帮助信息
    /// </summary>
    static void ShowHelp()
    {
        Console.WriteLine("Liam.Logging.Example - Liam.Logging 功能库示例程序");
        Console.WriteLine();
        Console.WriteLine("用法:");
        Console.WriteLine("  Liam.Logging.Example [选项]");
        Console.WriteLine();
        Console.WriteLine("选项:");
        Console.WriteLine("  --simple    运行简单演示模式");
        Console.WriteLine("  --test      运行基本功能测试");
        Console.WriteLine("  --help, -h  显示此帮助信息");
        Console.WriteLine();
        Console.WriteLine("默认行为:");
        Console.WriteLine("  不带参数运行时，启动完整的交互式演示程序");
        Console.WriteLine();
        Console.WriteLine("示例:");
        Console.WriteLine("  Liam.Logging.Example --simple");
        Console.WriteLine("  Liam.Logging.Example --test");
        Console.WriteLine();
    }
}
