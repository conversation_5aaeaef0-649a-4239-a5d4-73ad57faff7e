{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Liam.TcpServer": "Debug", "Liam.Logging": "Debug"}}, "TcpServer": {"Port": 8080, "MaxConnections": 100, "BufferSize": 4096, "KeepAlive": true, "KeepAliveInterval": 30, "KeepAliveRetryCount": 3, "ConnectionTimeout": 30, "EnableSsl": false, "SslCertificatePath": "", "SslCertificatePassword": "", "EnableHeartbeat": true, "HeartbeatInterval": 30, "HeartbeatTimeout": 10, "EnableConnectionPool": true, "ConnectionPoolSize": 50, "EnablePerformanceMonitoring": true, "LogLevel": "Debug"}, "Performance": {"EnableMetrics": true, "MetricsInterval": 5, "EnableMemoryOptimization": true, "MaxConcurrentConnections": 1000, "ThreadPoolMinWorkerThreads": 10, "ThreadPoolMinCompletionPortThreads": 10}}