namespace Liam.Logging.Example.Services;

/// <summary>
/// 示例服务接口
/// 提供交互式菜单和功能演示的主要入口
/// </summary>
public interface IExampleService
{
    /// <summary>
    /// 运行示例程序
    /// </summary>
    /// <returns>异步任务</returns>
    Task RunAsync();
}

/// <summary>
/// 日志记录演示服务接口
/// 演示基本的日志记录功能
/// </summary>
public interface ILoggingDemoService
{
    /// <summary>
    /// 演示多级别日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateLogLevelsAsync();

    /// <summary>
    /// 演示异常日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateExceptionLoggingAsync();
}

/// <summary>
/// 性能测试服务接口
/// 演示异步日志记录的性能优势
/// </summary>
public interface IPerformanceTestService
{
    /// <summary>
    /// 运行性能测试
    /// </summary>
    /// <param name="iterations">测试迭代次数</param>
    /// <param name="concurrentThreads">并发线程数</param>
    /// <returns>异步任务</returns>
    Task RunPerformanceTestAsync(int iterations = 10000, int concurrentThreads = 10);

    /// <summary>
    /// 比较同步和异步日志记录性能
    /// </summary>
    /// <param name="iterations">测试迭代次数</param>
    /// <returns>异步任务</returns>
    Task ComparePerformanceAsync(int iterations = 5000);
}

/// <summary>
/// 配置演示服务接口
/// 演示配置管理和动态配置功能
/// </summary>
public interface IConfigurationDemoService
{
    /// <summary>
    /// 演示配置管理
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateConfigurationAsync();

    /// <summary>
    /// 演示不同环境配置
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateEnvironmentConfigAsync();
}

/// <summary>
/// 异步日志记录演示服务接口
/// 演示异步日志记录和缓冲机制
/// </summary>
public interface IAsyncLoggingDemoService
{
    /// <summary>
    /// 演示异步日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateAsyncLoggingAsync();

    /// <summary>
    /// 演示批处理日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateBatchLoggingAsync();
}

/// <summary>
/// 结构化日志记录演示服务接口
/// 演示结构化日志和自定义属性
/// </summary>
public interface IStructuredLoggingDemoService
{
    /// <summary>
    /// 演示结构化日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateStructuredLoggingAsync();

    /// <summary>
    /// 演示自定义属性日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateCustomPropertiesAsync();
}

/// <summary>
/// 作用域日志记录演示服务接口
/// 演示日志作用域和上下文信息
/// </summary>
public interface IScopeLoggingDemoService
{
    /// <summary>
    /// 演示日志作用域
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateScopeLoggingAsync();

    /// <summary>
    /// 演示嵌套作用域
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateNestedScopesAsync();
}

/// <summary>
/// 过滤演示服务接口
/// 演示日志过滤和格式化功能
/// </summary>
public interface IFilteringDemoService
{
    /// <summary>
    /// 演示日志过滤
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateFilteringAsync();

    /// <summary>
    /// 演示日志格式化
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateFormattingAsync();
}

/// <summary>
/// 异常处理演示服务接口
/// 演示异常处理和错误日志记录最佳实践
/// </summary>
public interface IExceptionHandlingDemoService
{
    /// <summary>
    /// 演示异常处理
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateExceptionHandlingAsync();

    /// <summary>
    /// 演示错误恢复
    /// </summary>
    /// <returns>异步任务</returns>
    Task DemonstrateErrorRecoveryAsync();
}
