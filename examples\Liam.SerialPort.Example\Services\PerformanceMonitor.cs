using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace Liam.SerialPort.Example.Services;

/// <summary>
/// 性能监控实现
/// 提供详细的性能监控和统计功能
/// </summary>
public class PerformanceMonitor : IPerformanceMonitor, IDisposable
{
    private readonly ILogger<PerformanceMonitor> _logger;
    private readonly ConcurrentDictionary<string, OperationInfo> _activeOperations = new();
    private readonly object _statsLock = new();
    private readonly Timer _metricsTimer;
    private readonly Stopwatch _totalRunTime = new();

    private PerformanceStatistics _statistics = new();
    private RealTimeMetrics _realTimeMetrics = new();
    private DateTime _lastMetricsUpdate = DateTime.UtcNow;
    private long _lastOperationCount;
    private long _lastBytesSent;
    private long _lastBytesReceived;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public PerformanceMonitor(ILogger<PerformanceMonitor> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 每秒更新一次实时指标
        _metricsTimer = new Timer(UpdateRealTimeMetrics, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
    }

    /// <summary>
    /// 开始监控
    /// </summary>
    public void Start()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(PerformanceMonitor));

        _totalRunTime.Start();
        _logger.LogInformation("性能监控已启动");
    }

    /// <summary>
    /// 停止监控
    /// </summary>
    public void Stop()
    {
        if (_disposed)
            return;

        _totalRunTime.Stop();
        _logger.LogInformation("性能监控已停止，总运行时间: {TotalTime}", _totalRunTime.Elapsed);
    }

    /// <summary>
    /// 记录操作开始
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <returns>操作ID</returns>
    public string StartOperation(string operationName)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(PerformanceMonitor));

        var operationId = Guid.NewGuid().ToString("N")[..8];
        var operationInfo = new OperationInfo
        {
            Id = operationId,
            Name = operationName,
            StartTime = DateTime.UtcNow,
            Stopwatch = Stopwatch.StartNew()
        };

        _activeOperations[operationId] = operationInfo;
        _logger.LogDebug("操作开始: {OperationName} (ID: {OperationId})", operationName, operationId);

        return operationId;
    }

    /// <summary>
    /// 记录操作完成
    /// </summary>
    /// <param name="operationId">操作ID</param>
    /// <param name="success">是否成功</param>
    public void EndOperation(string operationId, bool success = true)
    {
        if (_disposed || !_activeOperations.TryRemove(operationId, out var operationInfo))
            return;

        operationInfo.Stopwatch.Stop();
        var responseTime = operationInfo.Stopwatch.Elapsed.TotalMilliseconds;

        lock (_statsLock)
        {
            _statistics.TotalOperations++;
            
            if (success)
            {
                _statistics.SuccessfulOperations++;
            }
            else
            {
                _statistics.FailedOperations++;
            }

            // 更新响应时间统计
            if (_statistics.TotalOperations == 1)
            {
                _statistics.MinResponseTime = responseTime;
                _statistics.MaxResponseTime = responseTime;
                _statistics.AverageResponseTime = responseTime;
            }
            else
            {
                _statistics.MinResponseTime = Math.Min(_statistics.MinResponseTime, responseTime);
                _statistics.MaxResponseTime = Math.Max(_statistics.MaxResponseTime, responseTime);
                
                // 计算移动平均值
                _statistics.AverageResponseTime = 
                    (_statistics.AverageResponseTime * (_statistics.TotalOperations - 1) + responseTime) / _statistics.TotalOperations;
            }
        }

        _logger.LogDebug("操作完成: {OperationName} (ID: {OperationId}), 耗时: {ResponseTime:F2}ms, 成功: {Success}", 
            operationInfo.Name, operationId, responseTime, success);
    }

    /// <summary>
    /// 记录数据传输
    /// </summary>
    /// <param name="bytesTransferred">传输的字节数</param>
    /// <param name="direction">传输方向</param>
    public void RecordDataTransfer(long bytesTransferred, TransferDirection direction)
    {
        if (_disposed)
            return;

        lock (_statsLock)
        {
            switch (direction)
            {
                case TransferDirection.Send:
                    _statistics.TotalBytesSent += bytesTransferred;
                    break;
                case TransferDirection.Receive:
                    _statistics.TotalBytesReceived += bytesTransferred;
                    break;
            }
        }

        _logger.LogTrace("数据传输记录: {Direction} {Bytes} 字节", direction, bytesTransferred);
    }

    /// <summary>
    /// 记录连接事件
    /// </summary>
    /// <param name="eventType">事件类型</param>
    /// <param name="portName">串口名称</param>
    public void RecordConnectionEvent(ConnectionEventType eventType, string portName)
    {
        if (_disposed)
            return;

        lock (_statsLock)
        {
            switch (eventType)
            {
                case ConnectionEventType.Connected:
                    _statistics.TotalConnections++;
                    _realTimeMetrics.ActiveConnections++;
                    break;
                case ConnectionEventType.Disconnected:
                    _realTimeMetrics.ActiveConnections = Math.Max(0, _realTimeMetrics.ActiveConnections - 1);
                    break;
                case ConnectionEventType.ConnectionFailed:
                    _statistics.ConnectionFailures++;
                    break;
                case ConnectionEventType.AutoReconnect:
                    _statistics.AutoReconnectCount++;
                    break;
            }
        }

        _logger.LogInformation("连接事件: {EventType} - {PortName}", eventType, portName);
    }

    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    /// <returns>性能统计</returns>
    public PerformanceStatistics GetStatistics()
    {
        lock (_statsLock)
        {
            var stats = new PerformanceStatistics
            {
                TotalRunTime = _totalRunTime.Elapsed,
                TotalOperations = _statistics.TotalOperations,
                SuccessfulOperations = _statistics.SuccessfulOperations,
                FailedOperations = _statistics.FailedOperations,
                AverageResponseTime = _statistics.AverageResponseTime,
                MinResponseTime = _statistics.MinResponseTime,
                MaxResponseTime = _statistics.MaxResponseTime,
                TotalBytesSent = _statistics.TotalBytesSent,
                TotalBytesReceived = _statistics.TotalBytesReceived,
                TotalConnections = _statistics.TotalConnections,
                ConnectionFailures = _statistics.ConnectionFailures,
                AutoReconnectCount = _statistics.AutoReconnectCount,
                MemoryUsage = GC.GetTotalMemory(false),
                CpuUsage = GetCpuUsage()
            };

            return stats;
        }
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public void Reset()
    {
        if (_disposed)
            return;

        lock (_statsLock)
        {
            _statistics = new PerformanceStatistics();
            _realTimeMetrics = new RealTimeMetrics();
            _lastOperationCount = 0;
            _lastBytesSent = 0;
            _lastBytesReceived = 0;
            _lastMetricsUpdate = DateTime.UtcNow;
        }

        _totalRunTime.Restart();
        _logger.LogInformation("性能统计信息已重置");
    }

    /// <summary>
    /// 获取实时性能指标
    /// </summary>
    /// <returns>实时性能指标</returns>
    public RealTimeMetrics GetRealTimeMetrics()
    {
        lock (_statsLock)
        {
            return new RealTimeMetrics
            {
                ActiveConnections = _realTimeMetrics.ActiveConnections,
                OperationsPerSecond = _realTimeMetrics.OperationsPerSecond,
                BytesPerSecondSent = _realTimeMetrics.BytesPerSecondSent,
                BytesPerSecondReceived = _realTimeMetrics.BytesPerSecondReceived,
                CurrentResponseTime = _realTimeMetrics.CurrentResponseTime,
                ErrorRate = _realTimeMetrics.ErrorRate,
                MemoryGrowthRate = _realTimeMetrics.MemoryGrowthRate
            };
        }
    }

    /// <summary>
    /// 更新实时指标
    /// </summary>
    /// <param name="state">定时器状态</param>
    private void UpdateRealTimeMetrics(object? state)
    {
        if (_disposed)
            return;

        try
        {
            var now = DateTime.UtcNow;
            var elapsed = (now - _lastMetricsUpdate).TotalSeconds;

            if (elapsed < 0.5) // 避免除零错误
                return;

            lock (_statsLock)
            {
                // 计算每秒操作数
                var operationDelta = _statistics.TotalOperations - _lastOperationCount;
                _realTimeMetrics.OperationsPerSecond = operationDelta / elapsed;

                // 计算每秒传输字节数
                var sentDelta = _statistics.TotalBytesSent - _lastBytesSent;
                var receivedDelta = _statistics.TotalBytesReceived - _lastBytesReceived;
                _realTimeMetrics.BytesPerSecondSent = sentDelta / elapsed;
                _realTimeMetrics.BytesPerSecondReceived = receivedDelta / elapsed;

                // 计算错误率
                _realTimeMetrics.ErrorRate = _statistics.TotalOperations > 0 
                    ? (_statistics.FailedOperations / (double)_statistics.TotalOperations) * 100 
                    : 0;

                // 更新记录
                _lastOperationCount = _statistics.TotalOperations;
                _lastBytesSent = _statistics.TotalBytesSent;
                _lastBytesReceived = _statistics.TotalBytesReceived;
                _lastMetricsUpdate = now;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "更新实时指标时发生错误");
        }
    }

    /// <summary>
    /// 获取CPU使用率
    /// </summary>
    /// <returns>CPU使用率百分比</returns>
    private static double GetCpuUsage()
    {
        try
        {
            using var process = Process.GetCurrentProcess();
            return process.TotalProcessorTime.TotalMilliseconds / Environment.ProcessorCount / Environment.TickCount * 100;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _metricsTimer?.Dispose();
        _totalRunTime?.Stop();
        
        _logger.LogInformation("性能监控器已释放");
    }

    /// <summary>
    /// 操作信息
    /// </summary>
    private class OperationInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public Stopwatch Stopwatch { get; set; } = new();
    }
}
