# Liam.TcpServer 示例程序

## 概述

这是一个完整的Liam.TcpServer功能演示程序，展示了TCP服务器的各种功能和最佳实践。

## 功能特性

### 🚀 核心功能演示
- **基本TCP服务器**: 标准TCP服务器启动和客户端连接处理
- **SSL/TLS安全连接**: 加密通信和证书验证
- **连接管理**: 连接池、连接监控和统计信息
- **心跳检测**: 自动检测客户端连接状态
- **性能监控**: 实时性能指标和资源使用情况
- **压力测试**: 多客户端并发连接测试

### 🛠️ 技术特性
- **.NET 8.0**: 基于最新.NET平台
- **依赖注入**: 使用Microsoft.Extensions.DependencyInjection
- **配置管理**: 支持appsettings.json配置
- **日志记录**: 集成Liam.Logging进行详细日志记录
- **异常处理**: 完善的错误处理和恢复机制
- **资源管理**: 自动资源清理和内存优化

## 快速开始

### 1. 运行程序
```bash
cd examples/TcpDemo/Liam.TcpServer.Example
dotnet run
```

### 2. 选择功能
程序启动后会显示交互式菜单：
```
=== TCP服务器示例菜单 ===
1. 启动基本TCP服务器
2. 启动SSL/TLS服务器
3. 连接管理演示
4. 心跳检测演示
5. 性能监控演示
6. 压力测试
7. 查看服务器状态
8. 停止所有服务器
0. 退出程序
```

### 3. 测试连接
启动服务器后，可以使用以下方式测试：

**使用telnet测试**:
```bash
telnet localhost 8080
```

**使用PowerShell测试**:
```powershell
$client = New-Object System.Net.Sockets.TcpClient
$client.Connect("localhost", 8080)
$stream = $client.GetStream()
$data = [System.Text.Encoding]::UTF8.GetBytes("Hello Server`n")
$stream.Write($data, 0, $data.Length)
```

## 配置说明

### appsettings.json 配置项

```json
{
  "TcpServer": {
    "Port": 8080,                    // 默认监听端口
    "MaxConnections": 100,           // 最大连接数
    "BufferSize": 4096,              // 缓冲区大小
    "KeepAlive": true,               // 启用Keep-Alive
    "KeepAliveInterval": 30,         // Keep-Alive间隔(秒)
    "ConnectionTimeout": 30,         // 连接超时(秒)
    "EnableSsl": false,              // 启用SSL/TLS
    "SslCertificatePath": "",        // SSL证书路径
    "SslCertificatePassword": "",    // SSL证书密码
    "EnableHeartbeat": true,         // 启用心跳检测
    "HeartbeatInterval": 30,         // 心跳间隔(秒)
    "HeartbeatTimeout": 10           // 心跳超时(秒)
  }
}
```

## 功能详解

### 1. 基本TCP服务器
- 启动标准TCP服务器监听指定端口
- 处理客户端连接和断开事件
- 实现消息回显功能
- 显示连接统计信息

### 2. SSL/TLS服务器
- 支持加密通信
- 证书验证和管理
- 安全连接建立
- 加密数据传输

### 3. 连接管理
- 实时连接统计
- 活跃连接列表
- 连接质量监控
- 连接池管理

### 4. 心跳检测
- 自动发送心跳包
- 检测客户端连接状态
- 自动清理无响应连接
- 可配置心跳间隔和超时

### 5. 性能监控
- CPU和内存使用率
- 网络吞吐量统计
- 消息处理速度
- 响应时间分析
- 错误率统计

### 6. 压力测试
- 模拟大量并发连接
- 测试服务器承载能力
- 性能基准测试
- 资源使用分析

## 最佳实践

### 1. 错误处理
```csharp
try
{
    await server.StartAsync(config);
}
catch (Exception ex)
{
    _logger.LogError(ex, "服务器启动失败");
    // 处理错误...
}
```

### 2. 资源管理
```csharp
// 使用using语句确保资源释放
using var client = new TcpClient();
await client.ConnectAsync(endpoint);
```

### 3. 异步编程
```csharp
// 使用ConfigureAwait(false)避免死锁
await server.StartAsync(config).ConfigureAwait(false);
```

### 4. 日志记录
```csharp
_logger.LogInformation("服务器启动成功，端口: {Port}", config.Port);
_logger.LogError(ex, "处理客户端消息失败");
```

## 故障排除

### 常见问题

**1. 端口被占用**
```
错误: Address already in use
解决: 更改端口号或停止占用端口的程序
```

**2. SSL证书问题**
```
错误: SSL证书无效
解决: 检查证书路径和密码配置
```

**3. 连接超时**
```
错误: Connection timeout
解决: 检查网络连接和防火墙设置
```

**4. 内存不足**
```
错误: OutOfMemoryException
解决: 减少并发连接数或增加系统内存
```

### 调试技巧

1. **启用详细日志**:
   ```json
   "Logging": {
     "LogLevel": {
       "Liam.TcpServer": "Debug"
     }
   }
   ```

2. **监控资源使用**:
   - 使用任务管理器监控CPU和内存
   - 使用netstat查看网络连接状态

3. **网络工具**:
   - 使用Wireshark分析网络包
   - 使用telnet测试连接

## 扩展开发

### 自定义消息处理器
```csharp
server.DataReceived += async (sender, e) =>
{
    // 自定义消息处理逻辑
    var message = ProcessMessage(e.Data);
    await server.SendToClientAsync(e.ClientId, message);
};
```

### 添加中间件
```csharp
services.AddLiamTcpServer(configuration)
    .AddMiddleware<AuthenticationMiddleware>()
    .AddMiddleware<LoggingMiddleware>();
```

## 相关资源

- [Liam.TcpServer 文档](../../../src/Liam.TcpServer/README.md)
- [Liam.TcpClient 示例](../Liam.TcpClient.Example/README.md)
- [Liam.Logging 文档](../../../src/Liam.Logging/README.md)
- [.NET 8 网络编程指南](https://docs.microsoft.com/dotnet/core/extensions/networking)
