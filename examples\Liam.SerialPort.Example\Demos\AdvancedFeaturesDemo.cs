using Microsoft.Extensions.Logging;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;
using Liam.SerialPort.Events;
using Liam.SerialPort.Example.Services;
using System.Text;

namespace Liam.SerialPort.Example.Demos;

/// <summary>
/// 高级功能演示
/// 展示热插拔检测、自动重连、事件处理、连接质量监控等高级功能
/// </summary>
public class AdvancedFeaturesDemo
{
    private readonly ILogger<AdvancedFeaturesDemo> _logger;
    private readonly ISerialPortService _serialPortService;
    private readonly IMenuService _menuService;
    private readonly IPerformanceMonitor _performanceMonitor;
    private readonly CancellationTokenSource _cancellationTokenSource = new();

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="serialPortService">串口服务</param>
    /// <param name="menuService">菜单服务</param>
    /// <param name="performanceMonitor">性能监控器</param>
    public AdvancedFeaturesDemo(
        ILogger<AdvancedFeaturesDemo> logger,
        ISerialPortService serialPortService,
        IMenuService menuService,
        IPerformanceMonitor performanceMonitor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serialPortService = serialPortService ?? throw new ArgumentNullException(nameof(serialPortService));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
    }

    /// <summary>
    /// 运行高级功能演示
    /// </summary>
    /// <returns>是否继续运行程序</returns>
    public async Task<bool> RunAsync()
    {
        _menuService.ClearScreen();
        
        Console.ForegroundColor = ConsoleColor.Magenta;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    高级功能演示                              ║");
        Console.WriteLine("║  演示内容：热插拔检测、自动重连、事件处理、连接质量监控      ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();

        try
        {
            // 注册所有事件处理器
            RegisterEventHandlers();

            // 1. 演示事件处理机制
            await DemonstrateEventHandlingAsync();

            // 2. 演示自动重连功能
            await DemonstrateAutoReconnectAsync();

            // 3. 演示热插拔检测
            await DemonstrateHotplugDetectionAsync();

            // 4. 演示连接质量监控
            await DemonstrateConnectionQualityMonitoringAsync();

            // 5. 演示异步数据处理
            await DemonstrateAsyncDataProcessingAsync();

            _menuService.ShowStatus("高级功能演示完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "高级功能演示过程中发生错误");
            _menuService.ShowStatus($"演示失败: {ex.Message}", StatusType.Error);
        }
        finally
        {
            // 取消注册事件处理器
            UnregisterEventHandlers();
            
            // 确保断开连接
            if (_serialPortService.IsConnected)
            {
                await _serialPortService.DisconnectAsync();
                _menuService.ShowStatus("已断开串口连接", StatusType.Info);
            }
            
            _cancellationTokenSource.Cancel();
        }

        return true;
    }

    /// <summary>
    /// 注册事件处理器
    /// </summary>
    private void RegisterEventHandlers()
    {
        _serialPortService.StatusChanged += OnStatusChanged;
        _serialPortService.DataReceived += OnDataReceived;
        _serialPortService.ErrorOccurred += OnErrorOccurred;
        _serialPortService.DeviceChanged += OnDeviceChanged;
        
        _logger.LogDebug("事件处理器已注册");
    }

    /// <summary>
    /// 取消注册事件处理器
    /// </summary>
    private void UnregisterEventHandlers()
    {
        _serialPortService.StatusChanged -= OnStatusChanged;
        _serialPortService.DataReceived -= OnDataReceived;
        _serialPortService.ErrorOccurred -= OnErrorOccurred;
        _serialPortService.DeviceChanged -= OnDeviceChanged;
        
        _logger.LogDebug("事件处理器已取消注册");
    }

    /// <summary>
    /// 演示事件处理机制
    /// </summary>
    private async Task DemonstrateEventHandlingAsync()
    {
        _menuService.ShowStatus("正在演示事件处理机制...", StatusType.Info);
        
        Console.WriteLine("\n--- 事件处理机制演示 ---");
        Console.WriteLine("已注册以下事件处理器:");
        Console.WriteLine("  • StatusChanged - 连接状态变化事件");
        Console.WriteLine("  • DataReceived - 数据接收事件");
        Console.WriteLine("  • ErrorOccurred - 错误发生事件");
        Console.WriteLine("  • DeviceChanged - 设备热插拔事件");
        
        // 尝试建立连接以触发状态变化事件
        var ports = await _serialPortService.GetAvailablePortsAsync();
        var portList = ports.ToList();
        
        if (portList.Any())
        {
            var selectedPort = portList.First();
            var settings = SerialPortSettings.Default;
            
            Console.WriteLine($"\n尝试连接到 {selectedPort.PortName} 以演示状态变化事件...");
            
            try
            {
                await _serialPortService.ConnectAsync(selectedPort, settings);
                await Task.Delay(2000); // 等待事件处理
                
                // 发送一些数据以触发数据接收事件
                Console.WriteLine("发送测试数据以触发数据接收事件...");
                await _serialPortService.SendAsync("TEST EVENT HANDLING");
                await Task.Delay(1000);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"连接失败: {ex.Message}");
            }
        }
        else
        {
            Console.WriteLine("未发现可用串口，无法演示连接相关事件");
        }
        
        _menuService.ShowStatus("事件处理机制演示完成", StatusType.Success);
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示自动重连功能
    /// </summary>
    private async Task DemonstrateAutoReconnectAsync()
    {
        _menuService.ShowStatus("正在演示自动重连功能...", StatusType.Info);
        
        Console.WriteLine("\n--- 自动重连功能演示 ---");
        
        if (!_serialPortService.IsConnected)
        {
            Console.WriteLine("当前未连接，先建立连接...");
            
            var ports = await _serialPortService.GetAvailablePortsAsync();
            var portList = ports.ToList();
            
            if (!portList.Any())
            {
                _menuService.ShowStatus("未发现可用串口，无法演示自动重连", StatusType.Warning);
                return;
            }
            
            var selectedPort = portList.First();
            var settings = SerialPortSettings.Default;
            settings.AutoReconnect = true;
            settings.AutoReconnectInterval = 2000; // 2秒重连间隔
            settings.MaxAutoReconnectAttempts = 5; // 最多重连5次
            
            await _serialPortService.ConnectAsync(selectedPort, settings);
        }
        
        if (_serialPortService.IsConnected)
        {
            Console.WriteLine($"当前自动重连状态: {(_serialPortService.AutoReconnectEnabled ? "启用" : "禁用")}");
            
            if (!_serialPortService.AutoReconnectEnabled)
            {
                _serialPortService.AutoReconnectEnabled = true;
                Console.WriteLine("已启用自动重连功能");
            }
            
            Console.WriteLine("\n自动重连演示说明:");
            Console.WriteLine("1. 当前连接正常");
            Console.WriteLine("2. 如果连接意外断开，系统将自动尝试重连");
            Console.WriteLine("3. 重连间隔: 2秒");
            Console.WriteLine("4. 最大重连次数: 5次");
            Console.WriteLine("\n提示: 您可以拔掉串口设备来测试自动重连功能");
            
            // 监控连接状态30秒
            var startTime = DateTime.UtcNow;
            var monitorDuration = TimeSpan.FromSeconds(30);
            
            Console.WriteLine($"\n开始监控连接状态 ({monitorDuration.TotalSeconds} 秒)...");
            
            while (DateTime.UtcNow - startTime < monitorDuration && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                Console.Write($"\r状态: {_serialPortService.Status} | 已连接: {_serialPortService.IsConnected} | 监控时间: {(DateTime.UtcNow - startTime).TotalSeconds:F0}s");
                await Task.Delay(1000, _cancellationTokenSource.Token);
            }
            
            Console.WriteLine();
        }
        else
        {
            _menuService.ShowStatus("无法建立连接，跳过自动重连演示", StatusType.Warning);
        }
        
        _menuService.ShowStatus("自动重连功能演示完成", StatusType.Success);
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示热插拔检测
    /// </summary>
    private async Task DemonstrateHotplugDetectionAsync()
    {
        _menuService.ShowStatus("正在演示热插拔检测功能...", StatusType.Info);
        
        Console.WriteLine("\n--- 热插拔检测功能演示 ---");
        Console.WriteLine("系统正在监控串口设备的热插拔事件...");
        Console.WriteLine("\n当前可用串口:");
        
        var initialPorts = await _serialPortService.GetAvailablePortsAsync();
        var initialPortList = initialPorts.ToList();
        
        foreach (var port in initialPortList)
        {
            Console.WriteLine($"  • {port.PortName} - {port.Description}");
        }
        
        Console.WriteLine($"\n检测到 {initialPortList.Count} 个串口设备");
        Console.WriteLine("\n热插拔检测说明:");
        Console.WriteLine("1. 插入新的串口设备将触发设备添加事件");
        Console.WriteLine("2. 拔出串口设备将触发设备移除事件");
        Console.WriteLine("3. 系统会自动更新可用设备列表");
        Console.WriteLine("\n提示: 请尝试插拔串口设备来测试热插拔检测");
        
        // 监控设备变化30秒
        var startTime = DateTime.UtcNow;
        var monitorDuration = TimeSpan.FromSeconds(30);
        var lastPortCount = initialPortList.Count;
        
        Console.WriteLine($"\n开始监控设备变化 ({monitorDuration.TotalSeconds} 秒)...");
        
        while (DateTime.UtcNow - startTime < monitorDuration && !_cancellationTokenSource.Token.IsCancellationRequested)
        {
            try
            {
                var currentPorts = await _serialPortService.GetAvailablePortsAsync();
                var currentPortList = currentPorts.ToList();
                
                if (currentPortList.Count != lastPortCount)
                {
                    Console.WriteLine($"\n[{DateTime.Now:HH:mm:ss}] 设备数量变化: {lastPortCount} -> {currentPortList.Count}");
                    
                    if (currentPortList.Count > lastPortCount)
                    {
                        Console.WriteLine("检测到新设备插入:");
                        var newPorts = currentPortList.Where(p => !initialPortList.Any(ip => ip.PortName == p.PortName));
                        foreach (var port in newPorts)
                        {
                            Console.WriteLine($"  + {port.PortName} - {port.Description}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("检测到设备移除:");
                        var removedPorts = initialPortList.Where(p => !currentPortList.Any(cp => cp.PortName == p.PortName));
                        foreach (var port in removedPorts)
                        {
                            Console.WriteLine($"  - {port.PortName} - {port.Description}");
                        }
                    }
                    
                    lastPortCount = currentPortList.Count;
                    initialPortList = currentPortList; // 更新基准列表
                }
                
                Console.Write($"\r监控中... 当前设备数: {currentPortList.Count} | 监控时间: {(DateTime.UtcNow - startTime).TotalSeconds:F0}s");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "监控设备变化时发生错误");
            }
            
            await Task.Delay(2000, _cancellationTokenSource.Token); // 每2秒检查一次
        }
        
        Console.WriteLine();
        _menuService.ShowStatus("热插拔检测功能演示完成", StatusType.Success);
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示连接质量监控
    /// </summary>
    private async Task DemonstrateConnectionQualityMonitoringAsync()
    {
        _menuService.ShowStatus("正在演示连接质量监控...", StatusType.Info);
        
        Console.WriteLine("\n--- 连接质量监控演示 ---");
        
        if (!_serialPortService.IsConnected)
        {
            Console.WriteLine("当前未连接，跳过连接质量监控演示");
            return;
        }
        
        Console.WriteLine("开始连接质量监控...");
        Console.WriteLine("监控指标:");
        Console.WriteLine("  • 数据传输成功率");
        Console.WriteLine("  • 平均响应时间");
        Console.WriteLine("  • 连接稳定性");
        Console.WriteLine("  • 错误率统计");
        
        var testDuration = TimeSpan.FromSeconds(20);
        var testInterval = TimeSpan.FromSeconds(2);
        var startTime = DateTime.UtcNow;
        var testCount = 0;
        var successCount = 0;
        var totalResponseTime = 0.0;
        
        Console.WriteLine($"\n开始质量测试 ({testDuration.TotalSeconds} 秒)...");
        
        while (DateTime.UtcNow - startTime < testDuration && !_cancellationTokenSource.Token.IsCancellationRequested)
        {
            testCount++;
            var testStartTime = DateTime.UtcNow;
            
            try
            {
                // 发送测试数据
                var testData = $"QUALITY_TEST_{testCount:D3}";
                await _serialPortService.SendAsync(testData);
                
                // 模拟等待响应
                await Task.Delay(100, _cancellationTokenSource.Token);
                
                var responseTime = (DateTime.UtcNow - testStartTime).TotalMilliseconds;
                totalResponseTime += responseTime;
                successCount++;
                
                Console.WriteLine($"测试 {testCount}: 成功 (响应时间: {responseTime:F1}ms)");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试 {testCount}: 失败 ({ex.Message})");
            }
            
            // 显示实时统计
            var successRate = testCount > 0 ? (double)successCount / testCount * 100 : 0;
            var avgResponseTime = successCount > 0 ? totalResponseTime / successCount : 0;
            
            Console.WriteLine($"实时统计 - 成功率: {successRate:F1}% | 平均响应时间: {avgResponseTime:F1}ms | 测试次数: {testCount}");
            
            await Task.Delay(testInterval, _cancellationTokenSource.Token);
        }
        
        // 显示最终统计
        Console.WriteLine("\n=== 连接质量报告 ===");
        Console.WriteLine($"总测试次数: {testCount}");
        Console.WriteLine($"成功次数: {successCount}");
        Console.WriteLine($"失败次数: {testCount - successCount}");
        Console.WriteLine($"成功率: {(testCount > 0 ? (double)successCount / testCount * 100 : 0):F2}%");
        Console.WriteLine($"平均响应时间: {(successCount > 0 ? totalResponseTime / successCount : 0):F2}ms");
        
        var qualityLevel = successCount == testCount ? "优秀" : 
                          (double)successCount / testCount >= 0.9 ? "良好" : 
                          (double)successCount / testCount >= 0.7 ? "一般" : "较差";
        Console.WriteLine($"连接质量评级: {qualityLevel}");
        
        _menuService.ShowStatus("连接质量监控演示完成", StatusType.Success);
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示异步数据处理
    /// </summary>
    private async Task DemonstrateAsyncDataProcessingAsync()
    {
        _menuService.ShowStatus("正在演示异步数据处理...", StatusType.Info);
        
        Console.WriteLine("\n--- 异步数据处理演示 ---");
        
        if (!_serialPortService.IsConnected)
        {
            Console.WriteLine("当前未连接，跳过异步数据处理演示");
            return;
        }
        
        Console.WriteLine("开始异步数据处理演示...");
        Console.WriteLine("将并发发送多个数据包，演示异步处理能力");
        
        var tasks = new List<Task>();
        var packetCount = 10;
        
        for (int i = 1; i <= packetCount; i++)
        {
            var packetId = i;
            var task = Task.Run(async () =>
            {
                try
                {
                    var data = $"ASYNC_PACKET_{packetId:D2}_{DateTime.UtcNow.Ticks}";
                    var operationId = _performanceMonitor.StartOperation($"异步发送包{packetId}");
                    
                    Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 发送包 {packetId}: {data}");
                    await _serialPortService.SendAsync(data);
                    
                    _performanceMonitor.RecordDataTransfer(Encoding.UTF8.GetByteCount(data), TransferDirection.Send);
                    _performanceMonitor.EndOperation(operationId, true);
                    
                    Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 包 {packetId} 发送完成");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 包 {packetId} 发送失败: {ex.Message}");
                }
            });
            
            tasks.Add(task);
            
            // 稍微错开发送时间
            await Task.Delay(100, _cancellationTokenSource.Token);
        }
        
        Console.WriteLine($"\n等待所有 {packetCount} 个数据包发送完成...");
        await Task.WhenAll(tasks);
        
        Console.WriteLine("所有数据包发送完成");
        
        _menuService.ShowStatus("异步数据处理演示完成", StatusType.Success);
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 连接状态变化事件处理
    /// </summary>
    private void OnStatusChanged(object? sender, ConnectionStatusChangedEventArgs e)
    {
        Console.WriteLine($"\n[事件] 连接状态变化: {e.OldStatus} -> {e.NewStatus}");
        _performanceMonitor.RecordConnectionEvent(
            e.NewStatus == ConnectionStatus.Connected ? ConnectionEventType.Connected : ConnectionEventType.Disconnected,
            _serialPortService.CurrentPort?.PortName ?? "Unknown");
    }

    /// <summary>
    /// 数据接收事件处理
    /// </summary>
    private void OnDataReceived(object? sender, DataReceivedEventArgs e)
    {
        Console.WriteLine($"\n[事件] 数据接收: {Encoding.UTF8.GetString(e.Data)} ({e.Data.Length} 字节)");
        _performanceMonitor.RecordDataTransfer(e.Data.Length, TransferDirection.Receive);
    }

    /// <summary>
    /// 错误发生事件处理
    /// </summary>
    private void OnErrorOccurred(object? sender, SerialPortErrorEventArgs e)
    {
        Console.WriteLine($"\n[事件] 错误发生: {e.ErrorType} - {e.Message}");
        if (e.Exception != null)
        {
            _logger.LogError(e.Exception, "串口错误: {ErrorType}", e.ErrorType);
        }
    }

    /// <summary>
    /// 设备变化事件处理
    /// </summary>
    private void OnDeviceChanged(object? sender, DeviceChangedEventArgs e)
    {
        Console.WriteLine($"\n[事件] 设备变化: {e.ChangeType} - {e.DeviceInfo.PortName}");
        _performanceMonitor.RecordConnectionEvent(ConnectionEventType.DeviceHotplug, e.DeviceInfo.PortName);
    }
}
