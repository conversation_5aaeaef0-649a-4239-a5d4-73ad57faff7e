using Liam.Logging.Interfaces;
using System.Diagnostics;

namespace Liam.Logging.Example.Services;

/// <summary>
/// 性能测试服务实现
/// 演示异步日志记录的性能优势
/// </summary>
public class PerformanceTestService : IPerformanceTestService
{
    private readonly ILiamLogger _logger;

    /// <summary>
    /// 初始化性能测试服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public PerformanceTestService(ILiamLogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 运行性能测试
    /// </summary>
    /// <param name="iterations">测试迭代次数</param>
    /// <param name="concurrentThreads">并发线程数</param>
    /// <returns>异步任务</returns>
    public async Task RunPerformanceTestAsync(int iterations = 10000, int concurrentThreads = 10)
    {
        Console.WriteLine("=== 性能测试演示 ===\n");
        
        _logger.LogInformation($"开始性能测试: 迭代次数={iterations}, 并发线程数={concurrentThreads}");

        Console.WriteLine($"测试参数:");
        Console.WriteLine($"  迭代次数: {iterations:N0}");
        Console.WriteLine($"  并发线程数: {concurrentThreads}");
        Console.WriteLine($"  每线程迭代: {iterations / concurrentThreads:N0}");
        Console.WriteLine();

        // 1. 单线程同步日志测试
        await RunSingleThreadSyncTest(iterations / 4);

        // 2. 单线程异步日志测试
        await RunSingleThreadAsyncTest(iterations / 4);

        // 3. 多线程并发测试
        await RunConcurrentTest(iterations, concurrentThreads);

        // 4. 批量日志测试
        await RunBatchLoggingTest(iterations / 2);

        // 5. 内存使用测试
        await RunMemoryUsageTest(iterations / 2);

        Console.WriteLine("\n=== 性能测试完成 ===");
        _logger.LogInformation("性能测试完成");
    }

    /// <summary>
    /// 比较同步和异步日志记录性能
    /// </summary>
    /// <param name="iterations">测试迭代次数</param>
    /// <returns>异步任务</returns>
    public async Task ComparePerformanceAsync(int iterations = 5000)
    {
        Console.WriteLine("=== 同步 vs 异步性能比较 ===\n");
        
        _logger.LogInformation($"开始性能比较测试: 迭代次数={iterations}");

        // 预热
        Console.WriteLine("正在预热...");
        for (int i = 0; i < 100; i++)
        {
            _logger.LogInformation($"预热日志 {i}");
        }
        await Task.Delay(1000);

        // 同步日志测试
        Console.WriteLine("\n1. 同步日志记录测试:");
        var syncStopwatch = Stopwatch.StartNew();
        
        for (int i = 0; i < iterations; i++)
        {
            _logger.LogInformation($"同步日志消息 {i}: 当前时间 {DateTime.Now:HH:mm:ss.fff}");
        }
        
        syncStopwatch.Stop();
        var syncTime = syncStopwatch.ElapsedMilliseconds;
        var syncThroughput = iterations * 1000.0 / syncTime;

        Console.WriteLine($"  耗时: {syncTime:N0} ms");
        Console.WriteLine($"  吞吐量: {syncThroughput:N0} 条/秒");

        // 等待一段时间确保同步日志完成
        await Task.Delay(2000);

        // 异步日志测试
        Console.WriteLine("\n2. 异步日志记录测试:");
        var asyncStopwatch = Stopwatch.StartNew();
        
        var tasks = new List<Task>();
        for (int i = 0; i < iterations; i++)
        {
            var task = _logger.LogAsync(Liam.Logging.Constants.LogLevel.Information, 
                $"异步日志消息 {i}: 当前时间 {DateTime.Now:HH:mm:ss.fff}");
            tasks.Add(task);
        }
        
        await Task.WhenAll(tasks);
        asyncStopwatch.Stop();
        
        var asyncTime = asyncStopwatch.ElapsedMilliseconds;
        var asyncThroughput = iterations * 1000.0 / asyncTime;

        Console.WriteLine($"  耗时: {asyncTime:N0} ms");
        Console.WriteLine($"  吞吐量: {asyncThroughput:N0} 条/秒");

        // 性能比较结果
        Console.WriteLine("\n3. 性能比较结果:");
        var improvement = (syncTime - asyncTime) * 100.0 / syncTime;
        var throughputImprovement = (asyncThroughput - syncThroughput) * 100.0 / syncThroughput;

        Console.WriteLine($"  时间改进: {improvement:F1}% (异步更快)");
        Console.WriteLine($"  吞吐量改进: {throughputImprovement:F1}% (异步更高)");

        if (improvement > 0)
        {
            Console.WriteLine($"  ✓ 异步日志比同步日志快 {improvement:F1}%");
        }
        else
        {
            Console.WriteLine($"  ⚠ 同步日志比异步日志快 {Math.Abs(improvement):F1}%");
        }

        _logger.LogInformation($"性能比较完成: 同步={syncTime}ms, 异步={asyncTime}ms, 改进={improvement:F1}%");
    }

    /// <summary>
    /// 运行单线程同步测试
    /// </summary>
    /// <param name="iterations">迭代次数</param>
    /// <returns>异步任务</returns>
    private async Task RunSingleThreadSyncTest(int iterations)
    {
        Console.WriteLine("1. 单线程同步日志测试:");
        
        var stopwatch = Stopwatch.StartNew();
        
        for (int i = 0; i < iterations; i++)
        {
            _logger.LogInformation($"单线程同步日志 {i}");
        }
        
        stopwatch.Stop();
        var throughput = iterations * 1000.0 / stopwatch.ElapsedMilliseconds;
        
        Console.WriteLine($"   耗时: {stopwatch.ElapsedMilliseconds:N0} ms");
        Console.WriteLine($"   吞吐量: {throughput:N0} 条/秒");
        
        await Task.Delay(500); // 等待日志写入完成
    }

    /// <summary>
    /// 运行单线程异步测试
    /// </summary>
    /// <param name="iterations">迭代次数</param>
    /// <returns>异步任务</returns>
    private async Task RunSingleThreadAsyncTest(int iterations)
    {
        Console.WriteLine("\n2. 单线程异步日志测试:");
        
        var stopwatch = Stopwatch.StartNew();
        var tasks = new List<Task>();
        
        for (int i = 0; i < iterations; i++)
        {
            var task = _logger.LogAsync(Liam.Logging.Constants.LogLevel.Information, $"单线程异步日志 {i}");
            tasks.Add(task);
        }
        
        await Task.WhenAll(tasks);
        stopwatch.Stop();
        
        var throughput = iterations * 1000.0 / stopwatch.ElapsedMilliseconds;
        
        Console.WriteLine($"   耗时: {stopwatch.ElapsedMilliseconds:N0} ms");
        Console.WriteLine($"   吞吐量: {throughput:N0} 条/秒");
    }

    /// <summary>
    /// 运行并发测试
    /// </summary>
    /// <param name="totalIterations">总迭代次数</param>
    /// <param name="threadCount">线程数</param>
    /// <returns>异步任务</returns>
    private async Task RunConcurrentTest(int totalIterations, int threadCount)
    {
        Console.WriteLine($"\n3. 多线程并发测试 ({threadCount} 线程):");
        
        var iterationsPerThread = totalIterations / threadCount;
        var stopwatch = Stopwatch.StartNew();
        
        var tasks = Enumerable.Range(0, threadCount).Select(async threadId =>
        {
            for (int i = 0; i < iterationsPerThread; i++)
            {
                await _logger.LogAsync(Liam.Logging.Constants.LogLevel.Information, 
                    $"并发日志 Thread-{threadId} Message-{i}");
            }
        });
        
        await Task.WhenAll(tasks);
        stopwatch.Stop();
        
        var actualIterations = iterationsPerThread * threadCount;
        var throughput = actualIterations * 1000.0 / stopwatch.ElapsedMilliseconds;
        
        Console.WriteLine($"   总迭代: {actualIterations:N0}");
        Console.WriteLine($"   耗时: {stopwatch.ElapsedMilliseconds:N0} ms");
        Console.WriteLine($"   吞吐量: {throughput:N0} 条/秒");
    }

    /// <summary>
    /// 运行批量日志测试
    /// </summary>
    /// <param name="iterations">迭代次数</param>
    /// <returns>异步任务</returns>
    private async Task RunBatchLoggingTest(int iterations)
    {
        Console.WriteLine($"\n4. 批量日志测试:");
        
        var stopwatch = Stopwatch.StartNew();
        var batchSize = 100;
        var batches = iterations / batchSize;
        
        for (int batch = 0; batch < batches; batch++)
        {
            var batchTasks = new List<Task>();
            
            for (int i = 0; i < batchSize; i++)
            {
                var messageIndex = batch * batchSize + i;
                var task = _logger.LogAsync(Liam.Logging.Constants.LogLevel.Information, 
                    $"批量日志 Batch-{batch} Message-{i} Index-{messageIndex}");
                batchTasks.Add(task);
            }
            
            await Task.WhenAll(batchTasks);
        }
        
        stopwatch.Stop();
        var actualIterations = batches * batchSize;
        var throughput = actualIterations * 1000.0 / stopwatch.ElapsedMilliseconds;
        
        Console.WriteLine($"   批次数: {batches:N0}");
        Console.WriteLine($"   批次大小: {batchSize}");
        Console.WriteLine($"   总消息: {actualIterations:N0}");
        Console.WriteLine($"   耗时: {stopwatch.ElapsedMilliseconds:N0} ms");
        Console.WriteLine($"   吞吐量: {throughput:N0} 条/秒");
    }

    /// <summary>
    /// 运行内存使用测试
    /// </summary>
    /// <param name="iterations">迭代次数</param>
    /// <returns>异步任务</returns>
    private async Task RunMemoryUsageTest(int iterations)
    {
        Console.WriteLine($"\n5. 内存使用测试:");
        
        // 强制垃圾回收以获得准确的基线
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        
        var initialMemory = GC.GetTotalMemory(false);
        
        var stopwatch = Stopwatch.StartNew();
        
        for (int i = 0; i < iterations; i++)
        {
            await _logger.LogAsync(Liam.Logging.Constants.LogLevel.Information, 
                $"内存测试日志 {i}: 包含一些额外的数据来测试内存使用情况 - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
        }
        
        stopwatch.Stop();
        
        // 再次强制垃圾回收
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        
        var finalMemory = GC.GetTotalMemory(false);
        var memoryUsed = finalMemory - initialMemory;
        var memoryPerMessage = memoryUsed / (double)iterations;
        
        Console.WriteLine($"   消息数: {iterations:N0}");
        Console.WriteLine($"   耗时: {stopwatch.ElapsedMilliseconds:N0} ms");
        Console.WriteLine($"   初始内存: {initialMemory / 1024.0 / 1024.0:F2} MB");
        Console.WriteLine($"   最终内存: {finalMemory / 1024.0 / 1024.0:F2} MB");
        Console.WriteLine($"   内存增长: {memoryUsed / 1024.0 / 1024.0:F2} MB");
        Console.WriteLine($"   每条消息: {memoryPerMessage:F0} bytes");
    }
}
