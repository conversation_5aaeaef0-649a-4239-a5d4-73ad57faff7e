using Liam.Logging.Interfaces;

namespace Liam.Logging.Example.Services;

/// <summary>
/// 作用域日志记录演示服务实现
/// 演示日志作用域和上下文信息
/// </summary>
public class ScopeLoggingDemoService : IScopeLoggingDemoService
{
    private readonly ILiamLogger _logger;

    /// <summary>
    /// 初始化作用域日志记录演示服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ScopeLoggingDemoService(ILiamLogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 演示日志作用域
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateScopeLoggingAsync()
    {
        Console.WriteLine("=== 日志作用域演示 ===\n");

        Console.WriteLine("1. 基本作用域使用:");
        
        // 基本作用域
        using (var scope = _logger.BeginScope("用户操作"))
        {
            _logger.LogInformation("开始处理用户请求");
            await Task.Delay(100);
            
            _logger.LogInformation("验证用户权限");
            await Task.Delay(50);
            
            _logger.LogInformation("执行业务逻辑");
            await Task.Delay(150);
            
            _logger.LogInformation("用户请求处理完成");
        }
        
        Console.WriteLine("✓ 基本作用域演示完成");

        Console.WriteLine("\n2. 带参数的作用域:");
        
        await DemonstrateParameterizedScopeAsync();

        Console.WriteLine("\n3. 复杂对象作用域:");
        
        await DemonstrateComplexObjectScopeAsync();

        Console.WriteLine("\n4. 异步操作中的作用域:");
        
        await DemonstrateAsyncScopeAsync();

        Console.WriteLine("\n=== 日志作用域演示完成 ===");
    }

    /// <summary>
    /// 演示嵌套作用域
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateNestedScopesAsync()
    {
        Console.WriteLine("=== 嵌套作用域演示 ===\n");

        Console.WriteLine("1. 简单嵌套作用域:");
        
        using (var outerScope = _logger.BeginScope("订单处理"))
        {
            _logger.LogInformation("开始处理订单");
            
            using (var innerScope = _logger.BeginScope("库存检查"))
            {
                _logger.LogInformation("检查商品库存");
                await Task.Delay(100);
                
                _logger.LogInformation("库存充足，可以下单");
            }
            
            using (var innerScope = _logger.BeginScope("价格计算"))
            {
                _logger.LogInformation("计算订单总价");
                await Task.Delay(80);
                
                _logger.LogInformation("应用优惠券折扣");
                await Task.Delay(50);
                
                _logger.LogInformation("价格计算完成");
            }
            
            using (var innerScope = _logger.BeginScope("支付处理"))
            {
                _logger.LogInformation("开始支付处理");
                await Task.Delay(200);
                
                _logger.LogInformation("支付成功");
            }
            
            _logger.LogInformation("订单处理完成");
        }
        
        Console.WriteLine("✓ 简单嵌套作用域演示完成");

        Console.WriteLine("\n2. 复杂嵌套作用域:");
        
        await DemonstrateComplexNestedScopesAsync();

        Console.WriteLine("\n3. 并发作用域:");
        
        await DemonstrateConcurrentScopesAsync();

        Console.WriteLine("\n4. 异常处理中的作用域:");
        
        await DemonstrateExceptionScopesAsync();

        Console.WriteLine("\n=== 嵌套作用域演示完成 ===");
    }

    /// <summary>
    /// 演示带参数的作用域
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateParameterizedScopeAsync()
    {
        var userId = 12345;
        var sessionId = Guid.NewGuid().ToString("N")[..8];
        var requestId = Guid.NewGuid().ToString("N")[..8];
        
        using (var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            { "UserId", userId },
            { "SessionId", sessionId },
            { "RequestId", requestId },
            { "Operation", "UpdateProfile" }
        }))
        {
            _logger.LogInformation("开始更新用户资料");
            await Task.Delay(100);
            
            _logger.LogInformation("验证输入数据");
            await Task.Delay(50);
            
            _logger.LogInformation("更新数据库记录");
            await Task.Delay(120);
            
            _logger.LogInformation("清除相关缓存");
            await Task.Delay(30);
            
            _logger.LogInformation("用户资料更新完成");
        }
        
        Console.WriteLine("✓ 带参数的作用域演示完成");
    }

    /// <summary>
    /// 演示复杂对象作用域
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateComplexObjectScopeAsync()
    {
        var orderContext = new
        {
            OrderId = "ORD-2024-001",
            CustomerId = 12345,
            CustomerName = "张三",
            OrderDate = DateTime.Now,
            TotalAmount = 599.99m,
            Items = new[]
            {
                new { ProductId = "P001", Name = "笔记本电脑", Quantity = 1, Price = 499.99m },
                new { ProductId = "P002", Name = "鼠标", Quantity = 1, Price = 100.00m }
            },
            ShippingAddress = new
            {
                Street = "中关村大街1号",
                City = "北京",
                Province = "北京市",
                PostalCode = "100000"
            }
        };
        
        using (var scope = _logger.BeginScope(orderContext))
        {
            _logger.LogInformation("开始处理复杂订单");
            
            foreach (var item in orderContext.Items)
            {
                using (var itemScope = _logger.BeginScope(new { ProductId = item.ProductId, ProductName = item.Name }))
                {
                    _logger.LogInformation($"处理商品: {item.Name}");
                    await Task.Delay(50);
                    
                    _logger.LogInformation($"商品处理完成，数量: {item.Quantity}，价格: {item.Price:C}");
                }
            }
            
            _logger.LogInformation($"订单总金额: {orderContext.TotalAmount:C}");
            _logger.LogInformation("复杂订单处理完成");
        }
        
        Console.WriteLine("✓ 复杂对象作用域演示完成");
    }

    /// <summary>
    /// 演示异步操作中的作用域
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateAsyncScopeAsync()
    {
        var batchId = Guid.NewGuid().ToString("N")[..8];
        
        using (var scope = _logger.BeginScope(new { BatchId = batchId, Operation = "BatchProcessing" }))
        {
            _logger.LogInformation("开始批量异步处理");
            
            var tasks = Enumerable.Range(1, 5).Select(async i =>
            {
                using (var taskScope = _logger.BeginScope(new { TaskId = i, BatchId = batchId }))
                {
                    _logger.LogInformation($"异步任务 {i} 开始");
                    
                    // 模拟异步工作
                    await Task.Delay(Random.Shared.Next(100, 300));
                    
                    _logger.LogInformation($"异步任务 {i} 处理中...");
                    
                    await Task.Delay(Random.Shared.Next(50, 150));
                    
                    _logger.LogInformation($"异步任务 {i} 完成");
                }
            });
            
            await Task.WhenAll(tasks);
            
            _logger.LogInformation("批量异步处理完成");
        }
        
        Console.WriteLine("✓ 异步操作中的作用域演示完成");
    }

    /// <summary>
    /// 演示复杂嵌套作用域
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateComplexNestedScopesAsync()
    {
        var transactionId = Guid.NewGuid().ToString("N")[..8];
        
        using (var transactionScope = _logger.BeginScope(new { TransactionId = transactionId, Type = "DatabaseTransaction" }))
        {
            _logger.LogInformation("开始数据库事务");
            
            using (var operationScope = _logger.BeginScope("用户数据更新"))
            {
                _logger.LogInformation("更新用户基本信息");
                await Task.Delay(100);
                
                using (var validationScope = _logger.BeginScope("数据验证"))
                {
                    _logger.LogInformation("验证邮箱格式");
                    await Task.Delay(30);
                    
                    _logger.LogInformation("验证手机号格式");
                    await Task.Delay(25);
                    
                    _logger.LogInformation("数据验证通过");
                }
                
                using (var updateScope = _logger.BeginScope("数据库更新"))
                {
                    _logger.LogInformation("执行UPDATE语句");
                    await Task.Delay(80);
                    
                    _logger.LogInformation("更新影响行数: 1");
                }
                
                _logger.LogInformation("用户数据更新完成");
            }
            
            using (var operationScope = _logger.BeginScope("日志记录更新"))
            {
                _logger.LogInformation("记录操作日志");
                await Task.Delay(50);
                
                using (var auditScope = _logger.BeginScope("审计信息"))
                {
                    _logger.LogInformation("记录操作时间");
                    _logger.LogInformation("记录操作用户");
                    _logger.LogInformation("记录操作类型");
                    await Task.Delay(40);
                }
                
                _logger.LogInformation("日志记录完成");
            }
            
            _logger.LogInformation("提交数据库事务");
            await Task.Delay(60);
            
            _logger.LogInformation("数据库事务完成");
        }
        
        Console.WriteLine("✓ 复杂嵌套作用域演示完成");
    }

    /// <summary>
    /// 演示并发作用域
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateConcurrentScopesAsync()
    {
        var tasks = Enumerable.Range(1, 3).Select(async workerId =>
        {
            using (var workerScope = _logger.BeginScope(new { WorkerId = workerId, WorkerType = "ConcurrentWorker" }))
            {
                _logger.LogInformation($"并发工作者 {workerId} 开始工作");
                
                for (int step = 1; step <= 3; step++)
                {
                    using (var stepScope = _logger.BeginScope(new { Step = step, WorkerId = workerId }))
                    {
                        _logger.LogInformation($"工作者 {workerId} 执行步骤 {step}");
                        await Task.Delay(Random.Shared.Next(100, 200));
                        
                        _logger.LogInformation($"工作者 {workerId} 步骤 {step} 完成");
                    }
                }
                
                _logger.LogInformation($"并发工作者 {workerId} 工作完成");
            }
        });
        
        await Task.WhenAll(tasks);
        Console.WriteLine("✓ 并发作用域演示完成");
    }

    /// <summary>
    /// 演示异常处理中的作用域
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateExceptionScopesAsync()
    {
        var operationId = Guid.NewGuid().ToString("N")[..8];
        
        using (var operationScope = _logger.BeginScope(new { OperationId = operationId, Type = "RiskyOperation" }))
        {
            _logger.LogInformation("开始执行可能失败的操作");
            
            try
            {
                using (var riskScope = _logger.BeginScope("高风险步骤"))
                {
                    _logger.LogInformation("执行高风险操作");
                    await Task.Delay(100);
                    
                    // 模拟可能的异常
                    if (Random.Shared.NextDouble() < 0.7) // 70% 概率失败
                    {
                        throw new InvalidOperationException("模拟操作失败");
                    }
                    
                    _logger.LogInformation("高风险操作成功完成");
                }
                
                _logger.LogInformation("操作成功完成");
            }
            catch (Exception ex)
            {
                using (var errorScope = _logger.BeginScope(new { ErrorType = ex.GetType().Name, ErrorMessage = ex.Message }))
                {
                    _logger.LogError("操作执行失败", ex);
                    
                    using (var recoveryScope = _logger.BeginScope("错误恢复"))
                    {
                        _logger.LogInformation("开始错误恢复流程");
                        await Task.Delay(50);
                        
                        _logger.LogInformation("清理资源");
                        await Task.Delay(30);
                        
                        _logger.LogInformation("错误恢复完成");
                    }
                }
            }
            finally
            {
                using (var cleanupScope = _logger.BeginScope("清理操作"))
                {
                    _logger.LogInformation("执行最终清理");
                    await Task.Delay(40);
                    
                    _logger.LogInformation("清理操作完成");
                }
            }
            
            _logger.LogInformation("风险操作流程结束");
        }
        
        Console.WriteLine("✓ 异常处理中的作用域演示完成");
    }
}
