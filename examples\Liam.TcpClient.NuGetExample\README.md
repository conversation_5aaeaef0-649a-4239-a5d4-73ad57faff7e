# Liam.TcpClient NuGet包使用示例

## 概述

这是一个演示如何使用已发布的Liam.TcpClient NuGet包的简单示例程序。

## 功能演示

- ✅ **NuGet包集成**: 演示如何在项目中引用和使用Liam.TcpClient NuGet包
- ✅ **依赖注入配置**: 展示如何配置服务容器和TCP客户端
- ✅ **基本连接**: 演示TCP客户端连接和断开
- ✅ **消息收发**: 展示文本消息的发送和接收
- ✅ **事件处理**: 演示连接、断开、数据接收和错误事件
- ✅ **统计信息**: 显示连接统计和性能指标

## 快速开始

### 1. 安装NuGet包

在您的项目中安装Liam.TcpClient NuGet包：

```bash
dotnet add package Liam.TcpClient
```

### 2. 配置依赖注入

```csharp
var services = new ServiceCollection();

// 添加日志服务
services.AddLogging(builder =>
{
    builder.AddConsole();
    builder.SetMinimumLevel(LogLevel.Information);
});

// 添加Liam.TcpClient服务
services.AddTcpClient(config =>
{
    config.ConnectionTimeoutSeconds = 30;
    config.EnableHeartbeat = true;
    config.EnableAutoReconnect = true;
});

var serviceProvider = services.BuildServiceProvider();
var tcpClient = serviceProvider.GetRequiredService<ITcpClient>();
```

### 3. 连接和使用

```csharp
// 注册事件处理器
tcpClient.Connected += (sender, e) =>
{
    Console.WriteLine($"已连接到: {e.ConnectionInfo.RemoteEndPoint}");
};

tcpClient.DataReceived += (sender, e) =>
{
    var message = Encoding.UTF8.GetString(e.Data);
    Console.WriteLine($"收到消息: {message}");
};

// 连接到服务器
var connected = await tcpClient.ConnectAsync("localhost", 8080);

if (connected)
{
    // 发送消息
    await tcpClient.SendTextAsync("Hello Server!");
    
    // 获取统计信息
    var stats = tcpClient.GetStatistics();
    Console.WriteLine($"发送消息数: {stats.TotalMessagesSent}");
    
    // 断开连接
    await tcpClient.DisconnectAsync();
}
```

## 运行示例

### 1. 启动TCP服务器

首先需要启动一个TCP服务器来测试连接。您可以：

**选项A**: 使用TcpDemo中的服务器示例
```bash
cd examples/TcpDemo/Liam.TcpServer.Example
dotnet run
# 选择 "1. 启动基本TCP服务器"
```

**选项B**: 使用简单的telnet服务器或其他TCP服务器

### 2. 运行客户端示例

```bash
cd examples/Liam.TcpClient.NuGetExample
dotnet run
```

## 示例输出

```
=== Liam.TcpClient NuGet包使用示例 ===
演示如何使用已发布的Liam.TcpClient NuGet包

正在尝试连接到 localhost:8080...
请确保有TCP服务器在端口8080上运行
✅ 已连接到服务器: 127.0.0.1:8080
🎉 连接成功！
📨 收到服务器消息: Echo: Hello from Liam.TcpClient NuGet package!

📊 连接统计:
  - 连接时长: 00:00:02.1234567
  - 发送消息数: 1
  - 接收消息数: 1
  - 发送字节数: 45
  - 接收字节数: 52
❌ 与服务器断开连接: 127.0.0.1:8080
🔌 已断开连接

示例程序执行完成，按任意键退出...
```

## 配置选项

Liam.TcpClient支持丰富的配置选项：

```csharp
services.AddTcpClient(config =>
{
    // 连接配置
    config.ConnectionTimeoutSeconds = 30;        // 连接超时
    config.ReceiveBufferSize = 4096;             // 接收缓冲区大小
    config.SendBufferSize = 4096;                // 发送缓冲区大小
    
    // 心跳检测
    config.EnableHeartbeat = true;               // 启用心跳
    config.HeartbeatIntervalSeconds = 30;        // 心跳间隔
    
    // 自动重连
    config.EnableAutoReconnect = true;           // 启用自动重连
    config.ReconnectIntervalSeconds = 5;         // 重连间隔
    config.MaxReconnectAttempts = 3;             // 最大重连次数
    
    // SSL/TLS (可选)
    config.EnableSsl = false;                    // 启用SSL
    // config.SslConfig = new SslConfig { ... };
});
```

## 高级功能

### 异步消息处理

```csharp
tcpClient.DataReceived += async (sender, e) =>
{
    var message = Encoding.UTF8.GetString(e.Data);
    
    // 异步处理消息
    await ProcessMessageAsync(message);
};
```

### 错误处理

```csharp
tcpClient.Error += (sender, e) =>
{
    logger.LogError(e.Exception, "TCP客户端错误");
    
    // 根据错误类型进行处理
    if (e.Exception is TcpConnectionException)
    {
        // 处理连接错误
    }
};
```

### 连接质量监控

```csharp
// 获取连接统计
var stats = tcpClient.GetStatistics();
Console.WriteLine($"错误次数: {stats.TotalErrors}");
Console.WriteLine($"重连次数: {stats.TotalReconnections}");

// 检查连接状态
if (tcpClient.IsConnected)
{
    Console.WriteLine($"连接时长: {tcpClient.ConnectionDuration}");
}
```

## 故障排除

### 连接失败
- 确保目标服务器正在运行并监听指定端口
- 检查防火墙设置
- 验证网络连接

### 消息发送失败
- 检查连接状态
- 验证消息格式
- 查看错误日志

### 性能问题
- 调整缓冲区大小
- 优化消息处理逻辑
- 启用性能监控

## 相关资源

- [Liam.TcpClient 完整文档](../../src/Liam.TcpClient/README.md)
- [TcpDemo 完整示例](../TcpDemo/README.md)
- [NuGet包页面](https://www.nuget.org/packages/Liam.TcpClient/)
- [源代码仓库](https://gitee.com/liam-gitee/liam)

## 技术支持

如果您在使用过程中遇到问题，请：
1. 查看完整文档和API参考
2. 检查示例代码和最佳实践
3. 在GitHub/Gitee上提交Issue
4. 参考故障排除指南
