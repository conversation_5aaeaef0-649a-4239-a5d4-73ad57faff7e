﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Liam.SerialPort.Extensions;
using Liam.SerialPort.Example.Services;
using Liam.SerialPort.Example.Demos;

namespace Liam.SerialPort.Example;

/// <summary>
/// Liam.SerialPort 示例程序主入口
/// 演示串口通讯库的各种功能和最佳实践
/// </summary>
class Program
{
    /// <summary>
    /// 程序主入口点
    /// </summary>
    /// <param name="args">命令行参数</param>
    /// <returns>异步任务</returns>
    static async Task Main(string[] args)
    {
        // 显示欢迎信息
        DisplayWelcomeMessage();

        try
        {
            // 创建主机构建器并配置服务
            var host = CreateHostBuilder(args).Build();

            // 启动示例应用程序
            var app = host.Services.GetRequiredService<ExampleApplication>();
            await app.RunAsync();
        }
        catch (Exception ex)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"程序运行时发生错误: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
            Console.ResetColor();
        }
        finally
        {
            Console.WriteLine("\n按任意键退出程序...");
            Console.ReadKey();
        }
    }

    /// <summary>
    /// 创建主机构建器
    /// </summary>
    /// <param name="args">命令行参数</param>
    /// <returns>主机构建器</returns>
    static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                // 配置应用程序配置
                config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
                config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true);
                config.AddCommandLine(args);
            })
            .ConfigureLogging((context, logging) =>
            {
                // 配置日志记录
                logging.ClearProviders();
                logging.AddConsole();
                logging.AddDebug();
                logging.SetMinimumLevel(LogLevel.Information);
            })
            .ConfigureServices((context, services) =>
            {
                // 注册串口服务
                services.AddSerialPort();

                // 注册示例应用程序服务
                services.AddSingleton<ExampleApplication>();
                services.AddTransient<IMenuService, MenuService>();
                services.AddTransient<IPerformanceMonitor, PerformanceMonitor>();

                // 注册演示服务
                services.AddTransient<BasicOperationsDemo>();
                services.AddTransient<AdvancedFeaturesDemo>();
                services.AddTransient<PerformanceTestDemo>();
                services.AddTransient<ConnectionPoolDemo>();
                services.AddTransient<DataFormatDemo>();
                services.AddTransient<ErrorHandlingDemo>();
                services.AddTransient<ConfigurationDemo>();
                services.AddTransient<ExtensionMethodsDemo>();
                services.AddTransient<DataHandlerDemo>();
                services.AddTransient<ConnectionManagementDemo>();
                services.AddTransient<DeviceDiscoveryDemo>();
            });

    /// <summary>
    /// 显示欢迎信息
    /// </summary>
    static void DisplayWelcomeMessage()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.Cyan;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    Liam.SerialPort 示例程序                    ║");
        Console.WriteLine("║                                                              ║");
        Console.WriteLine("║  这是一个完整的串口通讯库演示程序，展示了以下功能：              ║");
        Console.WriteLine("║  • 串口设备发现和枚举                                        ║");
        Console.WriteLine("║  • 连接管理和参数配置                                        ║");
        Console.WriteLine("║  • 同步/异步数据传输                                         ║");
        Console.WriteLine("║  • 热插拔检测和自动重连                                      ║");
        Console.WriteLine("║  • 连接状态监控和事件处理                                    ║");
        Console.WriteLine("║  • 数据缓冲区管理和超时处理                                  ║");
        Console.WriteLine("║  • 多种数据格式支持                                          ║");
        Console.WriteLine("║  • 错误处理和异常管理                                        ║");
        Console.WriteLine("║  • 性能监控和连接质量统计                                    ║");
        Console.WriteLine("║  • 依赖注入和配置管理                                        ║");
        Console.WriteLine("║                                                              ║");
        Console.WriteLine("║  基于 .NET 8.0 和 Liam.SerialPort v1.1.0                   ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();
    }
}
