# Liam.TcpClient 示例程序

## 概述

这是一个完整的Liam.TcpClient功能演示程序，展示了TCP客户端的各种功能和最佳实践。

## 功能特性

### 🚀 核心功能演示
- **基本TCP连接**: 标准TCP客户端连接和通信
- **SSL/TLS安全连接**: 加密通信和证书验证
- **连接池管理**: 多连接复用和负载均衡
- **心跳检测**: 自动检测连接状态和质量
- **自动重连**: 智能重连机制和故障恢复
- **性能监控**: 实时性能指标和连接质量监控
- **并发连接**: 多客户端并发连接测试
- **消息收发**: 高效的消息传输和处理

### 🛠️ 技术特性
- **.NET 8.0**: 基于最新.NET平台
- **依赖注入**: 使用Microsoft.Extensions.DependencyInjection
- **配置管理**: 支持appsettings.json配置
- **日志记录**: 集成Liam.Logging进行详细日志记录
- **异常处理**: 完善的错误处理和恢复机制
- **资源管理**: 自动资源清理和内存优化

## 快速开始

### 1. 启动服务器
首先需要启动TCP服务器（可以使用Liam.TcpServer.Example）：
```bash
cd examples/TcpDemo/Liam.TcpServer.Example
dotnet run
# 选择 "1. 启动基本TCP服务器"
```

### 2. 运行客户端
```bash
cd examples/TcpDemo/Liam.TcpClient.Example
dotnet run
```

### 3. 选择功能
程序启动后会显示交互式菜单：
```
=== TCP客户端示例菜单 ===
1. 基本连接测试
2. SSL/TLS连接测试
3. 连接池演示
4. 心跳检测演示
5. 自动重连演示
6. 性能监控演示
7. 并发连接测试
8. 消息收发测试
9. 查看客户端状态
10. 断开所有连接
0. 退出程序
```

## 配置说明

### appsettings.json 配置项

```json
{
  "TcpClient": {
    "ServerHost": "localhost",           // 服务器地址
    "ServerPort": 8080,                  // 服务器端口
    "ConnectTimeout": 30,                // 连接超时(秒)
    "ReceiveTimeout": 30,                // 接收超时(秒)
    "SendTimeout": 30,                   // 发送超时(秒)
    "BufferSize": 4096,                  // 缓冲区大小
    "KeepAlive": true,                   // 启用Keep-Alive
    "EnableSsl": false,                  // 启用SSL/TLS
    "SslServerName": "",                 // SSL服务器名称
    "ValidateServerCertificate": true,   // 验证服务器证书
    "EnableHeartbeat": true,             // 启用心跳检测
    "HeartbeatInterval": 30,             // 心跳间隔(秒)
    "HeartbeatTimeout": 10,              // 心跳超时(秒)
    "EnableAutoReconnect": true,         // 启用自动重连
    "ReconnectInterval": 5,              // 重连间隔(秒)
    "MaxReconnectAttempts": 10,          // 最大重连次数
    "EnableConnectionPool": true,        // 启用连接池
    "ConnectionPoolSize": 10             // 连接池大小
  }
}
```

## 功能详解

### 1. 基本连接测试
- 建立标准TCP连接
- 发送和接收消息
- 连接状态监控
- 基本错误处理

### 2. SSL/TLS连接测试
- 建立加密连接
- 证书验证
- 安全数据传输
- SSL配置管理

### 3. 连接池演示
- 多连接管理
- 连接复用
- 负载均衡
- 并发消息发送

### 4. 心跳检测演示
- 自动心跳包发送
- 连接状态检测
- 网络质量监控
- 连接质量评估

### 5. 自动重连演示
- 智能重连机制
- 连接故障检测
- 重连策略配置
- 故障恢复

### 6. 性能监控演示
- 实时性能指标
- 网络延迟测量
- 吞吐量统计
- 资源使用监控

### 7. 并发连接测试
- 多客户端并发
- 压力测试
- 性能基准
- 资源消耗分析

### 8. 消息收发测试
- 批量消息发送
- 消息传输性能
- 吞吐量测试
- 延迟分析

## 最佳实践

### 1. 连接管理
```csharp
// 使用using语句确保连接释放
using var client = serviceProvider.GetRequiredService<ITcpClient>();
await client.ConnectAsync(config);
```

### 2. 异常处理
```csharp
try
{
    await client.SendAsync(data);
}
catch (TcpClientException ex)
{
    _logger.LogError(ex, "发送数据失败");
    // 处理特定的TCP客户端异常
}
```

### 3. 异步编程
```csharp
// 使用ConfigureAwait(false)避免死锁
await client.ConnectAsync(config).ConfigureAwait(false);
```

### 4. 事件处理
```csharp
client.DataReceived += async (sender, e) =>
{
    // 异步处理接收到的数据
    await ProcessDataAsync(e.Data);
};
```

### 5. 资源清理
```csharp
// 确保正确释放资源
await client.DisconnectAsync();
client.Dispose();
```

## 故障排除

### 常见问题

**1. 连接被拒绝**
```
错误: Connection refused
解决: 确保服务器已启动并监听正确端口
```

**2. 连接超时**
```
错误: Connection timeout
解决: 检查网络连接和服务器可达性
```

**3. SSL握手失败**
```
错误: SSL handshake failed
解决: 检查SSL配置和证书有效性
```

**4. 心跳超时**
```
错误: Heartbeat timeout
解决: 调整心跳间隔和超时设置
```

### 调试技巧

1. **启用详细日志**:
   ```json
   "Logging": {
     "LogLevel": {
       "Liam.TcpClient": "Debug"
     }
   }
   ```

2. **监控网络连接**:
   ```bash
   netstat -an | findstr :8080
   ```

3. **检查防火墙设置**:
   - 确保防火墙允许TCP连接
   - 检查端口是否被阻止

4. **使用网络工具**:
   - telnet测试基本连接
   - Wireshark分析网络包

## 性能优化

### 1. 连接池配置
```json
{
  "ConnectionPoolSize": 20,        // 根据并发需求调整
  "EnableConnectionPool": true
}
```

### 2. 缓冲区优化
```json
{
  "BufferSize": 8192,             // 根据消息大小调整
  "EnableMemoryOptimization": true
}
```

### 3. 超时设置
```json
{
  "ConnectTimeout": 10,           // 快速失败
  "ReceiveTimeout": 30,           // 根据业务需求
  "SendTimeout": 30
}
```

## 扩展开发

### 自定义消息处理器
```csharp
client.DataReceived += async (sender, e) =>
{
    var message = await DeserializeMessageAsync(e.Data);
    await ProcessBusinessLogicAsync(message);
};
```

### 添加中间件
```csharp
services.AddLiamTcpClient(configuration)
    .AddMiddleware<CompressionMiddleware>()
    .AddMiddleware<EncryptionMiddleware>();
```

### 自定义重连策略
```csharp
services.Configure<TcpClientConfig>(config =>
{
    config.ReconnectStrategy = new ExponentialBackoffStrategy();
});
```

## 相关资源

- [Liam.TcpClient 文档](../../../src/Liam.TcpClient/README.md)
- [Liam.TcpServer 示例](../Liam.TcpServer.Example/README.md)
- [Liam.Logging 文档](../../../src/Liam.Logging/README.md)
- [.NET 8 网络编程指南](https://docs.microsoft.com/dotnet/core/extensions/networking)
