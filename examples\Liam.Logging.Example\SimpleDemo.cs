using Liam.Logging.Extensions;
using Liam.Logging.Interfaces;
using Liam.Logging.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Liam.Logging.Example;

/// <summary>
/// 简单的Liam.Logging功能演示
/// </summary>
public class SimpleDemo
{
    /// <summary>
    /// 运行简单演示
    /// </summary>
    /// <returns>异步任务</returns>
    public static async Task RunAsync()
    {
        Console.WriteLine("=== Liam.Logging 功能演示 ===\n");

        try
        {
            // 1. 创建服务容器和配置
            var services = new ServiceCollection();
            var configuration = CreateConfiguration();
            services.AddSingleton<IConfiguration>(configuration);

            // 2. 注册Liam.Logging服务
            services.AddLiamLogging(configuration, "Logging");

            // 3. 添加控制台日志提供程序
            services.AddConsoleLogging(config =>
            {
                config.EnableColors = true;
                config.TimestampFormat = "HH:mm:ss.fff";
            });

            // 4. 构建服务提供程序
            using var serviceProvider = services.BuildServiceProvider();
            var logger = serviceProvider.GetRequiredService<ILiamLogger>();

            Console.WriteLine("1. 基本日志级别演示:");
            await DemonstrateBasicLogging(logger);

            Console.WriteLine("\n2. 结构化日志演示:");
            await DemonstrateStructuredLogging(logger);

            Console.WriteLine("\n3. 异步日志演示:");
            await DemonstrateAsyncLogging(logger);

            Console.WriteLine("\n4. 日志作用域演示:");
            await DemonstrateScopeLogging(logger);

            Console.WriteLine("\n5. 异常日志演示:");
            await DemonstrateExceptionLogging(logger);

            Console.WriteLine("\n6. 等待异步日志完成...");
            await Task.Delay(1000);

            Console.WriteLine("\n=== 演示完成 ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"演示程序出现异常: {ex.Message}");
            Console.WriteLine($"异常详情: {ex}");
        }
    }

    /// <summary>
    /// 创建配置
    /// </summary>
    /// <returns>配置对象</returns>
    private static IConfiguration CreateConfiguration()
    {
        var configData = new Dictionary<string, string?>
        {
            { "Logging:MinimumLevel", "Debug" },
            { "Logging:EnableAsync", "true" },
            { "Logging:ApplicationName", "Liam.Logging.SimpleDemo" },
            { "Logging:AsyncQueueSize", "1000" },
            { "Logging:BatchSize", "10" },
            { "Logging:BatchTimeoutMs", "500" }
        };

        return new ConfigurationBuilder()
            .AddInMemoryCollection(configData)
            .Build();
    }

    /// <summary>
    /// 演示基本日志记录
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <returns>异步任务</returns>
    private static async Task DemonstrateBasicLogging(ILiamLogger logger)
    {
        logger.LogTrace("这是一条跟踪日志 - 用于详细的调试信息");
        await Task.Delay(50);

        logger.LogDebug("这是一条调试日志 - 用于开发时的调试信息");
        await Task.Delay(50);

        logger.LogInformation("这是一条信息日志 - 用于记录程序的正常运行信息");
        await Task.Delay(50);

        logger.LogWarning("这是一条警告日志 - 用于记录潜在的问题");
        await Task.Delay(50);

        logger.LogError("这是一条错误日志 - 用于记录错误信息");
        await Task.Delay(50);

        logger.LogCritical("这是一条严重错误日志 - 用于记录严重的系统错误");
        await Task.Delay(50);

        Console.WriteLine("   ✓ 基本日志级别演示完成");
    }

    /// <summary>
    /// 演示结构化日志记录
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <returns>异步任务</returns>
    private static async Task DemonstrateStructuredLogging(ILiamLogger logger)
    {
        // 使用LogStructured方法记录结构化日志
        logger.LogStructured(
            Liam.Logging.Constants.LogLevel.Information,
            "用户 {UserId} 在 {LoginTime} 登录系统，来源IP: {IpAddress}",
            12345, DateTime.Now, "*************");

        await Task.Delay(100);

        // 记录复杂对象
        var order = new
        {
            OrderId = "ORD-2024-001",
            CustomerId = 12345,
            Amount = 299.99m,
            Items = new[] { "笔记本电脑", "无线鼠标" }
        };

        logger.LogStructured(
            Liam.Logging.Constants.LogLevel.Information,
            "订单创建: OrderId={OrderId}, CustomerId={CustomerId}, Amount={Amount}",
            order.OrderId, order.CustomerId, order.Amount);

        await Task.Delay(100);

        Console.WriteLine("   ✓ 结构化日志演示完成");
    }

    /// <summary>
    /// 演示异步日志记录
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <returns>异步任务</returns>
    private static async Task DemonstrateAsyncLogging(ILiamLogger logger)
    {
        var tasks = new List<Task>();

        // 创建多个异步日志任务
        for (int i = 0; i < 5; i++)
        {
            var taskId = i;
            var task = logger.LogAsync(
                Liam.Logging.Constants.LogLevel.Information,
                $"异步日志消息 {taskId}: 时间戳 {DateTime.Now:HH:mm:ss.fff}");
            tasks.Add(task);
        }

        // 等待所有异步日志完成
        await Task.WhenAll(tasks);

        Console.WriteLine("   ✓ 异步日志演示完成");
    }

    /// <summary>
    /// 演示日志作用域
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <returns>异步任务</returns>
    private static async Task DemonstrateScopeLogging(ILiamLogger logger)
    {
        // 创建日志作用域
        using (logger.BeginScope(new { UserId = 12345, Operation = "UpdateProfile" }))
        {
            logger.LogInformation("开始更新用户资料");
            await Task.Delay(100);

            logger.LogInformation("验证用户权限");
            await Task.Delay(50);

            // 嵌套作用域
            using (logger.BeginScope("数据验证"))
            {
                logger.LogInformation("验证输入数据");
                await Task.Delay(50);

                logger.LogInformation("数据验证通过");
            }

            logger.LogInformation("用户资料更新完成");
        }

        Console.WriteLine("   ✓ 日志作用域演示完成");
    }

    /// <summary>
    /// 演示异常日志记录
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <returns>异步任务</returns>
    private static async Task DemonstrateExceptionLogging(ILiamLogger logger)
    {
        try
        {
            // 故意触发异常
            await SimulateFailingOperationAsync();
        }
        catch (Exception ex)
        {
            logger.LogError("操作失败", ex);
        }

        // 演示嵌套异常
        try
        {
            await SimulateNestedExceptionAsync();
        }
        catch (Exception ex)
        {
            logger.LogError("嵌套异常发生", ex);
        }

        Console.WriteLine("   ✓ 异常日志演示完成");
    }

    /// <summary>
    /// 模拟失败的操作
    /// </summary>
    /// <returns>异步任务</returns>
    private static async Task SimulateFailingOperationAsync()
    {
        await Task.Delay(50);
        throw new InvalidOperationException("这是一个模拟的操作失败");
    }

    /// <summary>
    /// 模拟嵌套异常
    /// </summary>
    /// <returns>异步任务</returns>
    private static async Task SimulateNestedExceptionAsync()
    {
        try
        {
            await Task.Delay(50);
            throw new FileNotFoundException("配置文件未找到: config.json");
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("系统初始化失败", ex);
        }
    }
}
