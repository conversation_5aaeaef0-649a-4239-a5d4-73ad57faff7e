# Liam.SerialPort 示例程序

这是一个完整的 Liam.SerialPort 库演示程序，展示了串口通讯库的各种功能和最佳实践。

## 功能特性

### 🔍 基础操作演示
- **设备发现和枚举** - 自动发现系统中的串口设备
- **连接建立和参数配置** - 支持各种串口参数配置
- **同步/异步数据传输** - 演示不同的数据传输方式
- **连接状态监控** - 实时监控连接状态变化
- **缓冲区管理** - 演示接收和发送缓冲区的管理

### ⚡ 高级功能演示
- **热插拔检测** - 自动检测设备的插入和拔出
- **自动重连机制** - 连接断开时自动尝试重连
- **事件驱动架构** - 完整的事件处理机制
- **连接质量监控** - 实时监控连接质量和性能指标
- **异步数据处理** - 演示高并发数据处理能力

### 📊 性能测试演示
- **吞吐量测试** - 测试数据传输的最大吞吐量
- **延迟测试** - 测量数据传输的延迟和抖动
- **压力测试** - 在高负载下测试系统稳定性
- **并发测试** - 测试多线程并发访问的安全性

### 🔗 连接池演示
- **多连接管理** - 同时管理多个串口连接
- **资源池化** - 有效复用连接资源
- **连接复用** - 提高资源利用率

### 📝 数据格式演示
- **字符串格式** - 支持各种字符编码
- **字节数组格式** - 原始二进制数据处理
- **十六进制格式** - 十六进制数据的转换和处理
- **编码转换** - 不同字符编码之间的转换

### 🛠️ 错误处理演示
- **异常处理** - 完整的异常捕获和处理机制
- **错误恢复** - 自动错误恢复策略
- **故障转移** - 连接失败时的故障转移
- **重试机制** - 智能重试和指数退避算法

### ⚙️ 配置管理演示
- **参数配置** - 灵活的配置管理系统
- **依赖注入** - 基于 Microsoft.Extensions.DependencyInjection
- **日志集成** - 集成 Microsoft.Extensions.Logging
- **配置文件管理** - 支持 JSON 配置文件和环境变量

### 🔧 扩展方法演示
- **十六进制发送** - 支持多种分隔符格式的十六进制数据发送
- **批量发送** - 字节数组和字符串的批量发送功能
- **等待数据** - 等待特定数据或字符串的到达
- **校验和计算** - 支持Sum、XOR、二进制补码等校验算法
- **数据模式查找** - 在数据流中查找特定的字节模式
- **串口设置创建** - 预定义的高速、低速等配置模板

### 📊 数据处理器演示
- **直接数据读取** - 通过ISerialPortDataHandler直接读取指定字节数
- **字符串读取** - 读取并转换为字符串格式
- **行读取** - 按行读取数据，支持多种换行符
- **监听控制** - 启动/停止数据接收监听
- **流式数据处理** - 连续数据流的实时处理演示

### 🔗 连接管理演示
- **手动重连** - 通过ISerialPortConnection手动触发重连
- **连接测试** - 测试连接的可用性和稳定性
- **连接统计** - 详细的连接统计信息和性能指标
- **状态扩展方法** - 连接状态的各种判断和操作方法
- **生命周期管理** - 完整的连接生命周期监控

### 🔍 设备发现演示
- **设备监控** - 通过ISerialPortDiscovery实时监控设备变化
- **设备刷新** - 手动刷新设备列表
- **端口可用性检查** - 批量检查端口的可用状态
- **设备信息分析** - 按制造商、类型等维度分析设备信息
- **热插拔检测** - 实时检测设备的插入和拔出

## 技术规范

- **.NET 版本**: .NET 8.0
- **依赖库**: Liam.SerialPort v1.1.0
- **日志框架**: Microsoft.Extensions.Logging
- **配置系统**: Microsoft.Extensions.Configuration
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **主机服务**: Microsoft.Extensions.Hosting

## 快速开始

### 前置要求

1. **.NET 8.0 SDK** 或更高版本
2. **串口设备** - 用于测试（可选，程序会模拟设备不存在的情况）
3. **Windows/Linux/macOS** - 跨平台支持

### 运行程序

1. **克隆或下载项目**
   ```bash
   git clone https://gitee.com/liam-gitee/liam.git
   cd liam/examples/Liam.SerialPort.Example
   ```

2. **还原依赖包**
   ```bash
   dotnet restore
   ```

3. **运行程序**

   **方式一：使用启动脚本（推荐）**
   ```bash
   # Windows
   run-example.bat

   # Linux/macOS
   chmod +x run-example.sh
   ./run-example.sh
   ```

   **方式二：直接运行**
   ```bash
   dotnet run
   ```

4. **按照菜单提示操作**
   - 程序启动后会显示主菜单
   - 选择相应的数字来运行不同的演示
   - 按照屏幕提示进行操作

### 配置选项

程序支持通过 `appsettings.json` 文件进行配置：

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Liam.SerialPort": "Debug"
    }
  },
  "SerialPort": {
    "DefaultBaudRate": 9600,
    "DefaultTimeout": 5000,
    "AutoReconnect": true
  }
}
```

## 演示内容详解

### 1. 基础操作演示

演示串口通讯的基本功能：

- **设备发现**: 扫描并列出所有可用的串口设备
- **连接建立**: 选择设备并配置连接参数
- **数据传输**: 发送和接收数据的基本操作
- **状态监控**: 查看连接状态和缓冲区信息

**适用场景**: 初学者了解串口通讯基础

### 2. 高级功能演示

展示串口库的高级特性：

- **事件处理**: 响应连接状态变化、数据接收等事件
- **自动重连**: 连接断开时自动尝试重新连接
- **热插拔**: 检测设备的动态插拔
- **质量监控**: 实时监控连接质量和性能

**适用场景**: 需要稳定可靠串口通讯的生产环境

### 3. 性能测试演示

全面测试串口通讯的性能：

- **吞吐量测试**: 测量最大数据传输速率
- **延迟测试**: 测量数据传输的延迟时间
- **压力测试**: 高负载下的稳定性测试
- **并发测试**: 多线程并发访问测试

**适用场景**: 性能敏感的应用场景

### 4. 数据格式演示

展示不同数据格式的处理：

- **文本数据**: 字符串的发送和接收
- **二进制数据**: 字节数组的处理
- **十六进制**: 十六进制格式的转换
- **编码转换**: 不同字符编码的处理

**适用场景**: 需要处理多种数据格式的应用

### 5. 错误处理演示

演示完整的错误处理机制：

- **异常捕获**: 各种异常情况的处理
- **超时处理**: 操作超时的处理策略
- **重试机制**: 失败操作的自动重试
- **错误恢复**: 错误后的自动恢复

**适用场景**: 需要高可靠性的关键应用

### 6. 配置管理演示

展示灵活的配置管理：

- **配置文件**: JSON 配置文件的使用
- **环境变量**: 环境特定的配置
- **依赖注入**: 服务的注册和解析
- **日志配置**: 日志级别和输出的配置

**适用场景**: 企业级应用的配置管理

### 7. 扩展方法演示

展示Liam.SerialPort库提供的扩展方法：

- **十六进制发送**: 支持多种分隔符的十六进制数据发送
- **批量发送**: 高效的批量数据传输
- **等待数据**: 等待特定数据模式的到达
- **校验和功能**: 多种校验算法的计算和验证
- **数据模式查找**: 协议解析中的模式匹配

**适用场景**: 需要高级数据处理功能的应用

### 8. 数据处理器演示

展示ISerialPortDataHandler的直接使用：

- **精确读取**: 读取指定字节数的数据
- **格式化读取**: 字符串和行数据的读取
- **监听控制**: 数据接收监听的启停控制
- **流式处理**: 连续数据流的实时处理

**适用场景**: 需要精确控制数据读取的应用

### 9. 连接管理演示

展示ISerialPortConnection的高级功能：

- **连接测试**: 验证连接的可用性
- **手动重连**: 程序控制的重连操作
- **统计信息**: 详细的连接性能统计
- **状态管理**: 连接状态的扩展操作

**适用场景**: 需要精细连接控制的关键应用

### 10. 设备发现演示

展示ISerialPortDiscovery的设备管理功能：

- **实时监控**: 设备变化的实时检测
- **批量检查**: 多端口可用性的并发检查
- **设备分析**: 设备信息的统计分析
- **热插拔处理**: 设备动态变化的响应

**适用场景**: 需要动态设备管理的应用

## 常见问题

### Q: 程序提示"未发现串口设备"怎么办？

A: 这是正常情况，程序会演示如何处理设备不存在的情况。如果需要测试实际设备：
1. 连接串口设备到计算机
2. 确保设备驱动已正确安装
3. 检查设备管理器中是否显示串口设备

### Q: 如何修改默认的串口参数？

A: 可以通过以下方式修改：
1. 编辑 `appsettings.json` 文件中的 `SerialPort` 配置节
2. 在程序运行时通过菜单选择自定义参数
3. 通过环境变量覆盖配置值

### Q: 程序运行时出现权限错误怎么办？

A: 在某些系统上访问串口需要管理员权限：
1. Windows: 以管理员身份运行命令提示符
2. Linux: 将用户添加到 `dialout` 组
3. macOS: 确保用户有设备访问权限

### Q: 如何启用详细的调试日志？

A: 修改 `appsettings.json` 中的日志配置：
```json
{
  "Logging": {
    "LogLevel": {
      "Liam.SerialPort": "Trace",
      "Liam.SerialPort.Example": "Debug"
    }
  }
}
```

### Q: 可以同时连接多个串口设备吗？

A: 可以，程序支持多连接管理。在连接池演示中可以看到相关功能。

## 故障排除

### 连接问题
- 检查串口设备是否正确连接
- 验证设备驱动是否已安装
- 确认串口参数配置正确
- 检查是否有其他程序占用串口

### 性能问题
- 调整缓冲区大小
- 优化波特率设置
- 检查系统资源使用情况
- 启用性能监控查看详细指标

### 数据传输问题
- 验证数据格式是否正确
- 检查字符编码设置
- 确认流控制配置
- 查看错误日志获取详细信息

## 扩展开发

### 添加自定义演示

1. 在 `Demos` 目录下创建新的演示类
2. 实现演示逻辑
3. 在 `Program.cs` 中注册服务
4. 在 `ExampleApplication.cs` 中添加菜单项

### 集成到现有项目

```csharp
// 添加 NuGet 包引用
dotnet add package Liam.SerialPort

// 注册服务
services.AddSerialPort();

// 使用服务
public class MyService
{
    private readonly ISerialPortService _serialPortService;
    
    public MyService(ISerialPortService serialPortService)
    {
        _serialPortService = serialPortService;
    }
}
```

## 技术支持

- **文档**: [Liam.SerialPort 完整文档](../../src/Liam.SerialPort/README.md)
- **源码**: [Gitee 仓库](https://gitee.com/liam-gitee/liam)
- **问题反馈**: 通过 Gitee Issues 提交问题
- **NuGet 包**: [NuGet.org](https://www.nuget.org/packages/Liam.SerialPort/)

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](../../LICENSE) 文件。
