using Liam.Logging.Interfaces;
using Microsoft.Extensions.Configuration;

namespace Liam.Logging.Example.Services;

/// <summary>
/// 配置演示服务实现
/// 演示配置管理和动态配置功能
/// </summary>
public class ConfigurationDemoService : IConfigurationDemoService
{
    private readonly ILiamLogger _logger;
    private readonly IConfiguration _configuration;

    /// <summary>
    /// 初始化配置演示服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">配置服务</param>
    public ConfigurationDemoService(ILiamLogger logger, IConfiguration configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }

    /// <summary>
    /// 演示配置管理
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateConfigurationAsync()
    {
        Console.WriteLine("=== 配置管理演示 ===\n");

        Console.WriteLine("1. 基本配置读取:");
        
        await DemonstrateBasicConfigurationAsync();

        Console.WriteLine("\n2. 日志配置详情:");
        
        await DemonstrateLoggingConfigurationAsync();

        Console.WriteLine("\n3. 示例程序配置:");
        
        await DemonstrateExampleSettingsAsync();

        Console.WriteLine("\n4. 配置验证:");
        
        await DemonstrateConfigurationValidationAsync();

        Console.WriteLine("\n5. 配置变更监控:");
        
        await DemonstrateConfigurationMonitoringAsync();

        Console.WriteLine("\n=== 配置管理演示完成 ===");
    }

    /// <summary>
    /// 演示不同环境配置
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateEnvironmentConfigAsync()
    {
        Console.WriteLine("=== 环境配置演示 ===\n");

        Console.WriteLine("1. 当前环境信息:");
        
        await ShowCurrentEnvironmentAsync();

        Console.WriteLine("\n2. 环境特定配置:");
        
        await ShowEnvironmentSpecificConfigAsync();

        Console.WriteLine("\n3. 配置优先级演示:");
        
        await DemonstrateConfigurationPriorityAsync();

        Console.WriteLine("\n4. 环境变量配置:");
        
        await DemonstrateEnvironmentVariablesAsync();

        Console.WriteLine("\n=== 环境配置演示完成 ===");
    }

    /// <summary>
    /// 演示基本配置读取
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateBasicConfigurationAsync()
    {
        var basicConfigs = new Dictionary<string, string>
        {
            { "应用程序名称", _configuration["Logging:ApplicationName"] ?? "未配置" },
            { "环境名称", _configuration["Logging:Environment"] ?? "未配置" },
            { "最小日志级别", _configuration["Logging:MinimumLevel"] ?? "未配置" },
            { "启用异步", _configuration["Logging:EnableAsync"] ?? "未配置" },
            { "异步队列大小", _configuration["Logging:AsyncQueueSize"] ?? "未配置" },
            { "批处理大小", _configuration["Logging:BatchSize"] ?? "未配置" }
        };

        foreach (var config in basicConfigs)
        {
            Console.WriteLine($"   {config.Key}: {config.Value}");
            _logger.LogInformation($"配置项 {config.Key}: {config.Value}");
            await Task.Delay(50);
        }

        Console.WriteLine("✓ 基本配置读取完成");
    }

    /// <summary>
    /// 演示日志配置详情
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateLoggingConfigurationAsync()
    {
        var loggingSection = _configuration.GetSection("Logging");
        
        Console.WriteLine("   日志配置详情:");
        
        // 全局属性
        var globalPropsSection = loggingSection.GetSection("GlobalProperties");
        if (globalPropsSection.Exists())
        {
            Console.WriteLine("   全局属性:");
            foreach (var prop in globalPropsSection.GetChildren())
            {
                Console.WriteLine($"     {prop.Key}: {prop.Value}");
                _logger.LogDebug($"全局属性 {prop.Key}: {prop.Value}");
            }
        }

        // 提供程序配置
        var providersSection = loggingSection.GetSection("Providers");
        if (providersSection.Exists())
        {
            Console.WriteLine("   日志提供程序:");
            var providers = providersSection.GetChildren().ToArray();
            
            for (int i = 0; i < providers.Length; i++)
            {
                var provider = providers[i];
                var typeName = provider["TypeName"] ?? "未知";
                var enabled = provider["Enabled"] ?? "未配置";
                var minLevel = provider["MinimumLevel"] ?? "未配置";
                
                Console.WriteLine($"     提供程序 {i + 1}:");
                Console.WriteLine($"       类型: {typeName}");
                Console.WriteLine($"       启用: {enabled}");
                Console.WriteLine($"       最小级别: {minLevel}");
                
                _logger.LogDebug($"提供程序配置: {typeName}, 启用={enabled}, 级别={minLevel}");
                
                // 显示提供程序设置
                var settingsSection = provider.GetSection("Settings");
                if (settingsSection.Exists())
                {
                    Console.WriteLine($"       设置:");
                    foreach (var setting in settingsSection.GetChildren())
                    {
                        Console.WriteLine($"         {setting.Key}: {setting.Value}");
                    }
                }
                
                await Task.Delay(100);
            }
        }

        // 过滤器配置
        var filtersSection = loggingSection.GetSection("Filters");
        if (filtersSection.Exists())
        {
            Console.WriteLine("   日志过滤器:");
            var filters = filtersSection.GetChildren().ToArray();
            
            for (int i = 0; i < filters.Length; i++)
            {
                var filter = filters[i];
                var type = filter["Type"] ?? "未知";
                var condition = filter["Condition"] ?? "未配置";
                var action = filter["Action"] ?? "未配置";
                
                Console.WriteLine($"     过滤器 {i + 1}:");
                Console.WriteLine($"       类型: {type}");
                Console.WriteLine($"       条件: {condition}");
                Console.WriteLine($"       动作: {action}");
                
                _logger.LogDebug($"过滤器配置: {type}, 条件={condition}, 动作={action}");
                await Task.Delay(80);
            }
        }

        Console.WriteLine("✓ 日志配置详情显示完成");
    }

    /// <summary>
    /// 演示示例程序配置
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateExampleSettingsAsync()
    {
        var exampleSection = _configuration.GetSection("ExampleSettings");
        
        if (exampleSection.Exists())
        {
            Console.WriteLine("   示例程序配置:");
            
            var settings = new Dictionary<string, string>
            {
                { "性能测试迭代次数", exampleSection["PerformanceTestIterations"] ?? "未配置" },
                { "并发线程数", exampleSection["ConcurrentThreads"] ?? "未配置" },
                { "测试数据大小", exampleSection["TestDataSize"] ?? "未配置" },
                { "启用详细输出", exampleSection["EnableDetailedOutput"] ?? "未配置" },
                { "模拟错误", exampleSection["SimulateErrors"] ?? "未配置" },
                { "错误率", exampleSection["ErrorRate"] ?? "未配置" }
            };

            foreach (var setting in settings)
            {
                Console.WriteLine($"     {setting.Key}: {setting.Value}");
                _logger.LogInformation($"示例配置 {setting.Key}: {setting.Value}");
                await Task.Delay(60);
            }
        }
        else
        {
            Console.WriteLine("   示例程序配置节未找到");
            _logger.LogWarning("ExampleSettings配置节未找到");
        }

        Console.WriteLine("✓ 示例程序配置显示完成");
    }

    /// <summary>
    /// 演示配置验证
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateConfigurationValidationAsync()
    {
        Console.WriteLine("   配置验证:");
        
        var validationResults = new List<(string Item, bool IsValid, string Message)>();

        // 验证必需的配置项
        var requiredConfigs = new[]
        {
            ("Logging:ApplicationName", "应用程序名称"),
            ("Logging:MinimumLevel", "最小日志级别"),
            ("Logging:EnableAsync", "异步启用标志"),
            ("Logging:Providers", "日志提供程序")
        };

        foreach (var (configKey, displayName) in requiredConfigs)
        {
            var value = _configuration[configKey];
            var isValid = !string.IsNullOrEmpty(value);
            var message = isValid ? "配置正确" : "配置缺失";
            
            validationResults.Add((displayName, isValid, message));
            
            Console.WriteLine($"     {displayName}: {(isValid ? "✓" : "✗")} {message}");
            _logger.LogInformation($"配置验证 {displayName}: {message}");
            
            await Task.Delay(50);
        }

        // 验证数值配置的有效性
        var numericConfigs = new[]
        {
            ("Logging:AsyncQueueSize", "异步队列大小", 1, 100000),
            ("Logging:BatchSize", "批处理大小", 1, 10000),
            ("Logging:BatchTimeoutMs", "批处理超时", 100, 60000),
            ("ExampleSettings:PerformanceTestIterations", "性能测试迭代", 1, 1000000)
        };

        foreach (var (configKey, displayName, min, max) in numericConfigs)
        {
            var valueStr = _configuration[configKey];
            bool isValid = false;
            string message = "配置缺失";

            if (!string.IsNullOrEmpty(valueStr) && int.TryParse(valueStr, out int value))
            {
                isValid = value >= min && value <= max;
                message = isValid ? $"值有效 ({value})" : $"值超出范围 ({value}, 应在 {min}-{max} 之间)";
            }
            else if (!string.IsNullOrEmpty(valueStr))
            {
                message = "值格式无效";
            }

            validationResults.Add((displayName, isValid, message));
            
            Console.WriteLine($"     {displayName}: {(isValid ? "✓" : "✗")} {message}");
            _logger.LogInformation($"数值配置验证 {displayName}: {message}");
            
            await Task.Delay(50);
        }

        // 汇总验证结果
        var validCount = validationResults.Count(r => r.IsValid);
        var totalCount = validationResults.Count;
        
        Console.WriteLine($"   验证汇总: {validCount}/{totalCount} 项配置有效");
        _logger.LogInformation($"配置验证完成: {validCount}/{totalCount} 项有效");

        Console.WriteLine("✓ 配置验证完成");
    }

    /// <summary>
    /// 演示配置变更监控
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateConfigurationMonitoringAsync()
    {
        Console.WriteLine("   配置变更监控演示:");
        
        // 注册配置变更回调
        var changeToken = _configuration.GetReloadToken();
        
        Console.WriteLine("     配置变更监控已启用");
        _logger.LogInformation("配置变更监控已启用");
        
        // 显示当前配置的哈希值（用于检测变更）
        var currentConfig = GetConfigurationSnapshot();
        var configHash = currentConfig.GetHashCode();
        
        Console.WriteLine($"     当前配置快照哈希: {configHash}");
        _logger.LogDebug($"配置快照哈希: {configHash}");
        
        // 模拟配置变更检测
        Console.WriteLine("     (在实际应用中，配置文件变更会触发重新加载)");
        _logger.LogInformation("配置变更监控演示: 文件变更将触发自动重新加载");
        
        await Task.Delay(200);
        Console.WriteLine("✓ 配置变更监控演示完成");
    }

    /// <summary>
    /// 显示当前环境信息
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task ShowCurrentEnvironmentAsync()
    {
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";
        var machineName = Environment.MachineName;
        var userName = Environment.UserName;
        var osVersion = Environment.OSVersion.ToString();
        var dotnetVersion = Environment.Version.ToString();
        
        var envInfo = new Dictionary<string, string>
        {
            { "运行环境", environment },
            { "机器名称", machineName },
            { "用户名称", userName },
            { "操作系统", osVersion },
            { ".NET版本", dotnetVersion },
            { "工作目录", Environment.CurrentDirectory },
            { "进程ID", Environment.ProcessId.ToString() }
        };

        foreach (var info in envInfo)
        {
            Console.WriteLine($"   {info.Key}: {info.Value}");
            _logger.LogInformation($"环境信息 {info.Key}: {info.Value}");
            await Task.Delay(40);
        }

        Console.WriteLine("✓ 当前环境信息显示完成");
    }

    /// <summary>
    /// 显示环境特定配置
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task ShowEnvironmentSpecificConfigAsync()
    {
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";
        
        Console.WriteLine($"   当前环境: {environment}");
        Console.WriteLine($"   对应配置文件: appsettings.{environment}.json");
        
        // 显示环境特定的配置差异
        var envSpecificConfigs = new[]
        {
            "Logging:MinimumLevel",
            "Logging:IncludeSourceInfo",
            "ExampleSettings:PerformanceTestIterations",
            "ExampleSettings:EnableDetailedOutput"
        };

        foreach (var configKey in envSpecificConfigs)
        {
            var value = _configuration[configKey];
            Console.WriteLine($"   {configKey}: {value ?? "未配置"}");
            _logger.LogDebug($"环境配置 {configKey}: {value}");
            await Task.Delay(60);
        }

        Console.WriteLine("✓ 环境特定配置显示完成");
    }

    /// <summary>
    /// 演示配置优先级
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateConfigurationPriorityAsync()
    {
        Console.WriteLine("   配置优先级演示 (从高到低):");
        Console.WriteLine("     1. 命令行参数");
        Console.WriteLine("     2. 环境变量");
        Console.WriteLine("     3. appsettings.{Environment}.json");
        Console.WriteLine("     4. appsettings.json");
        
        // 检查是否有环境变量覆盖
        var envVarExample = Environment.GetEnvironmentVariable("Logging__MinimumLevel");
        if (!string.IsNullOrEmpty(envVarExample))
        {
            Console.WriteLine($"   检测到环境变量覆盖: Logging__MinimumLevel = {envVarExample}");
            _logger.LogInformation($"环境变量覆盖检测: Logging__MinimumLevel = {envVarExample}");
        }
        else
        {
            Console.WriteLine("   未检测到环境变量覆盖");
            _logger.LogDebug("未检测到环境变量覆盖");
        }

        await Task.Delay(100);
        Console.WriteLine("✓ 配置优先级演示完成");
    }

    /// <summary>
    /// 演示环境变量配置
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateEnvironmentVariablesAsync()
    {
        Console.WriteLine("   环境变量配置演示:");
        
        var relevantEnvVars = new[]
        {
            "ASPNETCORE_ENVIRONMENT",
            "DOTNET_ENVIRONMENT",
            "Logging__MinimumLevel",
            "Logging__EnableAsync",
            "ExampleSettings__PerformanceTestIterations"
        };

        foreach (var envVar in relevantEnvVars)
        {
            var value = Environment.GetEnvironmentVariable(envVar);
            var status = string.IsNullOrEmpty(value) ? "未设置" : value;
            
            Console.WriteLine($"   {envVar}: {status}");
            _logger.LogDebug($"环境变量 {envVar}: {status}");
            await Task.Delay(50);
        }

        Console.WriteLine("   (可以通过设置环境变量来覆盖配置文件中的值)");
        Console.WriteLine("✓ 环境变量配置演示完成");
    }

    /// <summary>
    /// 获取配置快照
    /// </summary>
    /// <returns>配置快照字符串</returns>
    private string GetConfigurationSnapshot()
    {
        var snapshot = new List<string>();
        
        // 收集主要配置项
        var keyConfigs = new[]
        {
            "Logging:MinimumLevel",
            "Logging:EnableAsync",
            "Logging:ApplicationName",
            "ExampleSettings:PerformanceTestIterations"
        };

        foreach (var key in keyConfigs)
        {
            var value = _configuration[key] ?? "null";
            snapshot.Add($"{key}={value}");
        }

        return string.Join(";", snapshot);
    }
}
