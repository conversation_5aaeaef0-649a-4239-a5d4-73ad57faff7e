using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;
using Liam.SerialPort.Example.Services;
using System.IO.Ports;

namespace Liam.SerialPort.Example.Demos;

/// <summary>
/// 配置管理演示
/// 展示参数配置、依赖注入、日志集成等配置管理功能
/// </summary>
public class ConfigurationDemo
{
    private readonly ILogger<ConfigurationDemo> _logger;
    private readonly ISerialPortService _serialPortService;
    private readonly IMenuService _menuService;
    private readonly IPerformanceMonitor _performanceMonitor;
    private readonly IConfiguration _configuration;

    public ConfigurationDemo(
        ILogger<ConfigurationDemo> logger,
        ISerialPortService serialPortService,
        IMenuService menuService,
        IPerformanceMonitor performanceMonitor,
        IConfiguration configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serialPortService = serialPortService ?? throw new ArgumentNullException(nameof(serialPortService));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }

    public async Task<bool> RunAsync()
    {
        _menuService.ClearScreen();
        
        Console.ForegroundColor = ConsoleColor.DarkYellow;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    配置管理演示                              ║");
        Console.WriteLine("║  演示内容：参数配置、依赖注入、日志集成、配置文件管理        ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();

        try
        {
            // 演示配置管理功能
            await DemonstrateConfigurationSourcesAsync();
            await DemonstrateSerialPortConfigurationAsync();
            await DemonstrateLoggingConfigurationAsync();
            await DemonstrateDependencyInjectionAsync();
            await DemonstrateRuntimeConfigurationAsync();

            _menuService.ShowStatus("配置管理演示完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "配置管理演示过程中发生错误");
            _menuService.ShowStatus($"演示失败: {ex.Message}", StatusType.Error);
        }

        return true;
    }

    private async Task DemonstrateConfigurationSourcesAsync()
    {
        Console.WriteLine("\n--- 配置源演示 ---");
        
        Console.WriteLine("1. 应用程序配置源:");
        Console.WriteLine("   • appsettings.json - 基础配置文件");
        Console.WriteLine("   • appsettings.{Environment}.json - 环境特定配置");
        Console.WriteLine("   • 命令行参数 - 运行时参数覆盖");
        Console.WriteLine("   • 环境变量 - 系统环境配置");
        
        Console.WriteLine("\n2. 当前配置值:");
        
        // 显示一些示例配置值
        var loggingLevel = _configuration["Logging:LogLevel:Default"] ?? "Information";
        Console.WriteLine($"   • 默认日志级别: {loggingLevel}");
        
        var environment = _configuration["Environment"] ?? "Development";
        Console.WriteLine($"   • 运行环境: {environment}");
        
        // 尝试读取自定义配置
        var customConfig = _configuration["SerialPort:DefaultBaudRate"];
        if (!string.IsNullOrEmpty(customConfig))
        {
            Console.WriteLine($"   • 默认波特率: {customConfig}");
        }
        
        Console.WriteLine("\n3. 配置优先级 (从高到低):");
        Console.WriteLine("   1. 命令行参数");
        Console.WriteLine("   2. 环境变量");
        Console.WriteLine("   3. appsettings.{Environment}.json");
        Console.WriteLine("   4. appsettings.json");
        
        await _menuService.WaitForKeyAsync();
    }

    private async Task DemonstrateSerialPortConfigurationAsync()
    {
        Console.WriteLine("\n--- 串口配置演示 ---");
        
        Console.WriteLine("1. 预定义配置模板:");
        
        // 显示不同的配置模板
        var configs = new Dictionary<string, SerialPortSettings>
        {
            ["低速设备"] = new SerialPortSettings
            {
                BaudRate = 9600,
                DataBits = 8,
                StopBits = StopBits.One,
                Parity = Parity.None,
                ReadTimeout = 5000,
                WriteTimeout = 5000
            },
            ["高速设备"] = new SerialPortSettings
            {
                BaudRate = 115200,
                DataBits = 8,
                StopBits = StopBits.One,
                Parity = Parity.None,
                ReadTimeout = 1000,
                WriteTimeout = 1000
            },
            ["工业设备"] = new SerialPortSettings
            {
                BaudRate = 38400,
                DataBits = 8,
                StopBits = StopBits.Two,
                Parity = Parity.Even,
                ReadTimeout = 10000,
                WriteTimeout = 10000,
                AutoReconnect = true,
                AutoReconnectInterval = 5000
            }
        };

        foreach (var config in configs)
        {
            Console.WriteLine($"\n   {config.Key}:");
            Console.WriteLine($"     波特率: {config.Value.BaudRate}");
            Console.WriteLine($"     数据位: {config.Value.DataBits}");
            Console.WriteLine($"     停止位: {config.Value.StopBits}");
            Console.WriteLine($"     校验位: {config.Value.Parity}");
            Console.WriteLine($"     读超时: {config.Value.ReadTimeout}ms");
            Console.WriteLine($"     写超时: {config.Value.WriteTimeout}ms");
            
            if (config.Value.AutoReconnect)
            {
                Console.WriteLine($"     自动重连: 启用 (间隔: {config.Value.AutoReconnectInterval}ms)");
            }
        }

        Console.WriteLine("\n2. 配置验证:");
        foreach (var config in configs)
        {
            var validation = config.Value.Validate();
            Console.WriteLine($"   {config.Key}: {(validation.IsValid ? "✓ 有效" : "✗ 无效")}");
            
            if (!validation.IsValid)
            {
                foreach (var error in validation.Errors)
                {
                    Console.WriteLine($"     错误: {error}");
                }
            }
        }

        await _menuService.WaitForKeyAsync();
    }

    private async Task DemonstrateLoggingConfigurationAsync()
    {
        Console.WriteLine("\n--- 日志配置演示 ---");
        
        Console.WriteLine("1. 当前日志配置:");
        
        // 演示不同级别的日志记录
        _logger.LogTrace("这是 Trace 级别日志 - 最详细的调试信息");
        _logger.LogDebug("这是 Debug 级别日志 - 调试信息");
        _logger.LogInformation("这是 Information 级别日志 - 一般信息");
        _logger.LogWarning("这是 Warning 级别日志 - 警告信息");
        _logger.LogError("这是 Error 级别日志 - 错误信息");
        _logger.LogCritical("这是 Critical 级别日志 - 严重错误");
        
        Console.WriteLine("\n2. 结构化日志演示:");
        
        var operationId = Guid.NewGuid().ToString("N")[..8];
        var portName = "COM1";
        var baudRate = 9600;
        
        _logger.LogInformation("开始串口操作 {OperationId} 在端口 {PortName} 波特率 {BaudRate}", 
            operationId, portName, baudRate);
        
        using var scope = _logger.BeginScope("SerialPort Operation {OperationId}", operationId);
        _logger.LogDebug("配置串口参数");
        _logger.LogInformation("串口连接成功");
        _logger.LogDebug("发送数据包");
        _logger.LogInformation("操作完成");
        
        Console.WriteLine("\n3. 日志级别说明:");
        Console.WriteLine("   • Trace: 最详细的信息，通常只在开发时启用");
        Console.WriteLine("   • Debug: 调试信息，用于开发和故障排除");
        Console.WriteLine("   • Information: 一般信息，记录应用程序的正常流程");
        Console.WriteLine("   • Warning: 警告信息，表示潜在问题");
        Console.WriteLine("   • Error: 错误信息，表示操作失败但应用程序可以继续");
        Console.WriteLine("   • Critical: 严重错误，可能导致应用程序终止");
        
        await _menuService.WaitForKeyAsync();
    }

    private async Task DemonstrateDependencyInjectionAsync()
    {
        Console.WriteLine("\n--- 依赖注入演示 ---");
        
        Console.WriteLine("1. 已注册的服务:");
        Console.WriteLine("   • ISerialPortService - 串口服务 (Transient)");
        Console.WriteLine("   • ISerialPortDiscovery - 设备发现服务 (Singleton)");
        Console.WriteLine("   • ISerialPortConnection - 连接管理服务 (Transient)");
        Console.WriteLine("   • ISerialPortDataHandler - 数据处理服务 (Transient)");
        Console.WriteLine("   • IMenuService - 菜单服务 (Transient)");
        Console.WriteLine("   • IPerformanceMonitor - 性能监控服务 (Transient)");
        
        Console.WriteLine("\n2. 服务生命周期:");
        Console.WriteLine("   • Singleton: 整个应用程序生命周期内只创建一个实例");
        Console.WriteLine("   • Scoped: 每个作用域内创建一个实例");
        Console.WriteLine("   • Transient: 每次请求都创建新实例");
        
        Console.WriteLine("\n3. 依赖注入的优势:");
        Console.WriteLine("   • 松耦合: 类之间的依赖关系通过接口定义");
        Console.WriteLine("   • 可测试性: 可以轻松注入模拟对象进行单元测试");
        Console.WriteLine("   • 可配置性: 可以在运行时更改实现");
        Console.WriteLine("   • 生命周期管理: 容器自动管理对象的创建和销毁");
        
        Console.WriteLine("\n4. 当前服务实例信息:");
        Console.WriteLine($"   • SerialPortService 类型: {_serialPortService.GetType().Name}");
        Console.WriteLine($"   • MenuService 类型: {_menuService.GetType().Name}");
        Console.WriteLine($"   • PerformanceMonitor 类型: {_performanceMonitor.GetType().Name}");
        Console.WriteLine($"   • Logger 类型: {_logger.GetType().Name}");
        
        await _menuService.WaitForKeyAsync();
    }

    private async Task DemonstrateRuntimeConfigurationAsync()
    {
        Console.WriteLine("\n--- 运行时配置演示 ---");
        
        Console.WriteLine("1. 动态配置修改:");
        
        // 演示运行时修改串口服务配置
        Console.WriteLine($"   当前自动重连状态: {_serialPortService.AutoReconnectEnabled}");
        
        var newAutoReconnectState = !_serialPortService.AutoReconnectEnabled;
        _serialPortService.AutoReconnectEnabled = newAutoReconnectState;
        Console.WriteLine($"   修改后自动重连状态: {_serialPortService.AutoReconnectEnabled}");
        
        Console.WriteLine("\n2. 配置热重载演示:");
        Console.WriteLine("   • 修改 appsettings.json 文件");
        Console.WriteLine("   • 配置系统会自动检测文件变化");
        Console.WriteLine("   • 应用程序可以响应配置变化");
        
        Console.WriteLine("\n3. 环境特定配置:");
        Console.WriteLine("   • Development: 开发环境配置");
        Console.WriteLine("   • Staging: 预发布环境配置");
        Console.WriteLine("   • Production: 生产环境配置");
        
        Console.WriteLine("\n4. 配置最佳实践:");
        Console.WriteLine("   • 敏感信息使用用户机密或环境变量");
        Console.WriteLine("   • 使用强类型配置类");
        Console.WriteLine("   • 验证配置值的有效性");
        Console.WriteLine("   • 为不同环境提供适当的默认值");
        
        Console.WriteLine("\n5. 示例配置文件结构:");
        Console.WriteLine("   {");
        Console.WriteLine("     \"Logging\": {");
        Console.WriteLine("       \"LogLevel\": {");
        Console.WriteLine("         \"Default\": \"Information\",");
        Console.WriteLine("         \"Liam.SerialPort\": \"Debug\"");
        Console.WriteLine("       }");
        Console.WriteLine("     },");
        Console.WriteLine("     \"SerialPort\": {");
        Console.WriteLine("       \"DefaultBaudRate\": 9600,");
        Console.WriteLine("       \"DefaultTimeout\": 5000,");
        Console.WriteLine("       \"AutoReconnect\": true");
        Console.WriteLine("     }");
        Console.WriteLine("   }");
        
        await _menuService.WaitForKeyAsync();
    }
}
