using Liam.Logging.Constants;
using <PERSON>.Logging.Interfaces;
using Liam.Logging.Models;

namespace Liam.Logging.Example.Services;

/// <summary>
/// 结构化日志记录演示服务实现
/// 演示结构化日志和自定义属性
/// </summary>
public class StructuredLoggingDemoService : IStructuredLoggingDemoService
{
    private readonly ILiamLogger _logger;

    /// <summary>
    /// 初始化结构化日志记录演示服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public StructuredLoggingDemoService(ILiamLogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 演示结构化日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateStructuredLoggingAsync()
    {
        Console.WriteLine("=== 结构化日志记录演示 ===\n");

        Console.WriteLine("1. 基本结构化日志记录:");
        
        // 使用消息模板和参数
        _logger.LogStructured(LogLevel.Information, 
            "用户 {UserName} 在 {LoginTime} 登录系统，来源IP: {IpAddress}",
            "张三", DateTime.Now, "*************");

        _logger.LogStructured(LogLevel.Information,
            "订单 {OrderId} 创建成功，金额: {Amount:C}，商品数量: {ItemCount}",
            "ORD-2024-001", 299.99m, 3);

        Console.WriteLine("✓ 基本结构化日志已记录");

        Console.WriteLine("\n2. 复杂对象结构化日志:");
        
        var user = new
        {
            Id = 12345,
            Name = "李四",
            Email = "<EMAIL>",
            Role = "Administrator",
            LastLogin = DateTime.Now.AddDays(-1)
        };

        var order = new
        {
            OrderId = "ORD-2024-002",
            CustomerId = user.Id,
            Items = new[]
            {
                new { ProductId = "P001", Name = "笔记本电脑", Price = 5999.00m, Quantity = 1 },
                new { ProductId = "P002", Name = "无线鼠标", Price = 199.00m, Quantity = 2 }
            },
            TotalAmount = 6397.00m,
            Status = "Processing",
            CreatedAt = DateTime.Now
        };

        _logger.LogStructured(LogLevel.Information,
            "用户 {User} 创建了订单 {Order}",
            user, order);

        Console.WriteLine("✓ 复杂对象结构化日志已记录");

        Console.WriteLine("\n3. 性能指标结构化日志:");
        
        await SimulatePerformanceLoggingAsync();

        Console.WriteLine("\n4. 错误信息结构化日志:");
        
        await SimulateErrorLoggingAsync();

        Console.WriteLine("\n5. 业务事件结构化日志:");
        
        await SimulateBusinessEventLoggingAsync();

        Console.WriteLine("\n=== 结构化日志记录演示完成 ===");
    }

    /// <summary>
    /// 演示自定义属性日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateCustomPropertiesAsync()
    {
        Console.WriteLine("=== 自定义属性日志记录演示 ===\n");

        Console.WriteLine("1. 使用LogEvent记录自定义属性:");
        
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "用户执行了重要操作",
            Timestamp = DateTime.Now,
            Category = "BusinessOperation",
            Properties = new Dictionary<string, object?>
            {
                { "UserId", 12345 },
                { "UserName", "王五" },
                { "Operation", "UpdateProfile" },
                { "IpAddress", "*************" },
                { "UserAgent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" },
                { "SessionId", Guid.NewGuid().ToString() },
                { "RequestId", Guid.NewGuid().ToString() },
                { "Duration", TimeSpan.FromMilliseconds(245) },
                { "Success", true }
            }
        };

        _logger.LogEvent(logEvent);
        Console.WriteLine("✓ 自定义属性日志事件已记录");

        Console.WriteLine("\n2. 异步记录自定义属性:");
        
        var asyncLogEvent = new LogEvent
        {
            Level = LogLevel.Warning,
            Message = "系统资源使用率较高",
            Timestamp = DateTime.Now,
            Category = "SystemMonitoring",
            Properties = new Dictionary<string, object?>
            {
                { "CpuUsage", 85.5 },
                { "MemoryUsage", 78.2 },
                { "DiskUsage", 92.1 },
                { "NetworkIn", 1024 * 1024 * 15 }, // 15MB
                { "NetworkOut", 1024 * 1024 * 8 }, // 8MB
                { "ActiveConnections", 156 },
                { "QueueLength", 23 },
                { "ResponseTime", TimeSpan.FromMilliseconds(1250) }
            }
        };

        await _logger.LogEventAsync(asyncLogEvent);
        Console.WriteLine("✓ 异步自定义属性日志事件已记录");

        Console.WriteLine("\n3. 批量记录自定义属性:");
        
        var events = new[]
        {
            CreateSampleLogEvent("数据库查询", "DatabaseOperation", new { Query = "SELECT * FROM Users", Duration = 125, RowCount = 50 }),
            CreateSampleLogEvent("缓存命中", "CacheOperation", new { Key = "user:12345", HitRate = 0.95, Size = 2048 }),
            CreateSampleLogEvent("API调用", "ApiOperation", new { Endpoint = "/api/users", Method = "GET", StatusCode = 200 }),
            CreateSampleLogEvent("文件上传", "FileOperation", new { FileName = "document.pdf", Size = 1024 * 1024 * 2, Duration = 3500 }),
            CreateSampleLogEvent("邮件发送", "EmailOperation", new { To = "<EMAIL>", Subject = "Welcome", Success = true })
        };

        var tasks = events.Select(evt => _logger.LogEventAsync(evt));
        await Task.WhenAll(tasks);
        
        Console.WriteLine($"✓ 批量记录了 {events.Length} 个自定义属性日志事件");

        Console.WriteLine("\n4. 动态属性日志记录:");
        
        await SimulateDynamicPropertiesLoggingAsync();

        Console.WriteLine("\n=== 自定义属性日志记录演示完成 ===");
    }

    /// <summary>
    /// 模拟性能日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task SimulatePerformanceLoggingAsync()
    {
        var operations = new[]
        {
            new { Name = "数据库查询", Duration = 125, Success = true },
            new { Name = "Redis缓存", Duration = 15, Success = true },
            new { Name = "外部API调用", Duration = 850, Success = false },
            new { Name = "文件读取", Duration = 45, Success = true },
            new { Name = "数据处理", Duration = 320, Success = true }
        };

        foreach (var op in operations)
        {
            _logger.LogStructured(op.Success ? LogLevel.Information : LogLevel.Warning,
                "操作 {OperationName} 完成，耗时: {Duration}ms，状态: {Success}",
                op.Name, op.Duration, op.Success ? "成功" : "失败");
            
            await Task.Delay(100);
        }

        Console.WriteLine("✓ 性能指标结构化日志已记录");
    }

    /// <summary>
    /// 模拟错误日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task SimulateErrorLoggingAsync()
    {
        try
        {
            // 模拟一个可能失败的操作
            await SimulateFailingOperationAsync();
        }
        catch (Exception ex)
        {
            _logger.LogStructured(LogLevel.Error,
                "操作失败: {Operation}，错误类型: {ErrorType}，错误消息: {ErrorMessage}，用户: {UserId}",
                "ProcessPayment", ex.GetType().Name, ex.Message, 12345);
        }

        Console.WriteLine("✓ 错误信息结构化日志已记录");
    }

    /// <summary>
    /// 模拟业务事件日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task SimulateBusinessEventLoggingAsync()
    {
        var events = new object[]
        {
            new { Event = "UserRegistered", UserId = 12346, Email = "<EMAIL>", Source = "Website" },
            new { Event = "OrderPlaced", UserId = 12345, OrderId = "ORD-2024-003", Amount = 599.99 },
            new { Event = "PaymentProcessed", OrderId = "ORD-2024-003", PaymentMethod = "CreditCard", Status = "Success" },
            new { Event = "ProductViewed", UserId = 12345, ProductId = "P003", Category = "Electronics" },
            new { Event = "CartAbandoned", UserId = 12347, CartValue = 299.99, ItemCount = 2 }
        };

        foreach (var evt in events)
        {
            var eventType = evt.GetType().GetProperty("Event")?.GetValue(evt)?.ToString() ?? "Unknown";
            _logger.LogStructured(LogLevel.Information,
                "业务事件: {EventType}，详情: {@EventData}",
                eventType, evt);

            await Task.Delay(50);
        }

        Console.WriteLine("✓ 业务事件结构化日志已记录");
    }

    /// <summary>
    /// 模拟动态属性日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task SimulateDynamicPropertiesLoggingAsync()
    {
        var random = new Random();
        
        for (int i = 0; i < 5; i++)
        {
            var properties = new Dictionary<string, object?>
            {
                { "Iteration", i + 1 },
                { "Timestamp", DateTime.Now },
                { "RandomValue", random.Next(1, 1000) },
                { "IsEven", (i + 1) % 2 == 0 },
                { "ProcessId", Environment.ProcessId },
                { "ThreadId", Environment.CurrentManagedThreadId },
                { "MachineName", Environment.MachineName }
            };

            // 根据条件添加额外属性
            if (i % 2 == 0)
            {
                properties.Add("SpecialFlag", true);
                properties.Add("Category", "Even");
            }
            else
            {
                properties.Add("Category", "Odd");
            }

            var logEvent = new LogEvent
            {
                Level = LogLevel.Debug,
                Message = $"动态属性日志记录 - 迭代 {i + 1}",
                Timestamp = DateTime.Now,
                Category = "DynamicLogging",
                Properties = properties
            };

            await _logger.LogEventAsync(logEvent);
            await Task.Delay(200);
        }

        Console.WriteLine("✓ 动态属性日志记录已完成");
    }

    /// <summary>
    /// 创建示例日志事件
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="category">类别</param>
    /// <param name="data">数据</param>
    /// <returns>日志事件</returns>
    private LogEvent CreateSampleLogEvent(string message, string category, object data)
    {
        var properties = new Dictionary<string, object?>();
        
        // 将匿名对象的属性转换为字典
        foreach (var prop in data.GetType().GetProperties())
        {
            properties[prop.Name] = prop.GetValue(data);
        }

        return new LogEvent
        {
            Level = LogLevel.Information,
            Message = message,
            Timestamp = DateTime.Now,
            Category = category,
            Properties = properties
        };
    }

    /// <summary>
    /// 模拟可能失败的操作
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task SimulateFailingOperationAsync()
    {
        await Task.Delay(100);
        throw new InvalidOperationException("支付处理失败：余额不足");
    }
}
