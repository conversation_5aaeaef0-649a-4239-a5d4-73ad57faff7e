{"Logging": {"MinimumLevel": "Warning", "IncludeSourceInfo": false, "Environment": "Production", "GlobalProperties": {"Environment": "Production", "Debug": false}, "Providers": [{"TypeName": "ConsoleLogProvider", "Enabled": false}, {"TypeName": "FileLogProvider", "Enabled": true, "MinimumLevel": "Warning", "Settings": {"FilePath": "logs/prod-app.log", "EnableRotation": true, "MaxFileSize": 52428800, "RetainedFileCount": 30, "RotationInterval": "1.00:00:00", "AutoFlush": false, "BufferSize": 8192, "FormatterType": "Json"}}], "Filters": [{"Type": "Level", "Condition": "Information", "Action": "Exclude", "Settings": {}}, {"Type": "Category", "Condition": "Microsoft.*", "Action": "Exclude", "Settings": {}}, {"Type": "Category", "Condition": "System.*", "Action": "Exclude", "Settings": {}}]}, "ExampleSettings": {"PerformanceTestIterations": 50000, "ConcurrentThreads": 20, "EnableDetailedOutput": false, "SimulateErrors": false, "ErrorRate": 0.01}}