﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors />

    <!-- NuGet包信息 -->
    <PackageId>Liam.TcpClient</PackageId>
    <Version>1.0.8</Version>
    <Authors>Liam</Authors>
    <Description>现代化TCP客户端通信库，支持连接管理、异步数据传输、SSL/TLS安全通信、自动重连、心跳检测、连接池管理等功能，基于.NET 8.0构建</Description>
    <PackageTags>tcp;client;socket;network;communication;async;ssl;tls;dotnet8;liam</PackageTags>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <RepositoryUrl>https://gitee.com/liam-gitee/liam</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageProjectUrl>https://gitee.com/liam-gitee/liam</PackageProjectUrl>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
    <PackageIcon>icon.png</PackageIcon>
    <PackageReadmeFile>README.md</PackageReadmeFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Liam.Logging\Liam.Logging.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="../../icon.png" Pack="true" PackagePath="\" />
    <None Include="README.md" Pack="true" PackagePath="\" />
  </ItemGroup>

</Project>
