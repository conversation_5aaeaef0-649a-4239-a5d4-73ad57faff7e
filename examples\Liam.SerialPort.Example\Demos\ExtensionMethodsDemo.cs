using Microsoft.Extensions.Logging;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;
using Liam.SerialPort.Extensions;
using Liam.SerialPort.Example.Services;
using System.Text;

namespace Liam.SerialPort.Example.Demos;

/// <summary>
/// 扩展方法演示
/// 展示Liam.SerialPort库提供的各种扩展方法功能
/// </summary>
public class ExtensionMethodsDemo
{
    private readonly ILogger<ExtensionMethodsDemo> _logger;
    private readonly ISerialPortService _serialPortService;
    private readonly IMenuService _menuService;
    private readonly IPerformanceMonitor _performanceMonitor;

    /// <summary>
    /// 构造函数
    /// </summary>
    public ExtensionMethodsDemo(
        ILogger<ExtensionMethodsDemo> logger,
        ISerialPortService serialPortService,
        IMenuService menuService,
        IPerformanceMonitor performanceMonitor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serialPortService = serialPortService ?? throw new ArgumentNullException(nameof(serialPortService));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
    }

    /// <summary>
    /// 运行扩展方法演示
    /// </summary>
    public async Task<bool> RunAsync()
    {
        _menuService.ClearScreen();
        
        Console.ForegroundColor = ConsoleColor.DarkCyan;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    扩展方法演示                              ║");
        Console.WriteLine("║  演示内容：十六进制发送、批量发送、等待数据、校验和计算      ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();

        try
        {
            // 确保连接
            if (!await EnsureConnectionAsync())
            {
                return true;
            }

            // 1. 演示十六进制发送扩展方法
            await DemonstrateHexSendingAsync();

            // 2. 演示带换行符发送
            await DemonstrateLineSendingAsync();

            // 3. 演示批量发送
            await DemonstrateBatchSendingAsync();

            // 4. 演示等待数据功能
            await DemonstrateWaitForDataAsync();

            // 5. 演示校验和功能
            await DemonstrateChecksumFunctionsAsync();

            // 6. 演示数据模式查找
            await DemonstratePatternSearchAsync();

            // 7. 演示串口设置创建
            await DemonstrateSettingsCreationAsync();

            _menuService.ShowStatus("扩展方法演示完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "扩展方法演示过程中发生错误");
            _menuService.ShowStatus($"演示失败: {ex.Message}", StatusType.Error);
        }
        finally
        {
            if (_serialPortService.IsConnected)
            {
                await _serialPortService.DisconnectAsync();
            }
        }

        return true;
    }

    /// <summary>
    /// 确保串口连接
    /// </summary>
    private async Task<bool> EnsureConnectionAsync()
    {
        if (_serialPortService.IsConnected)
        {
            return true;
        }

        var ports = await _serialPortService.GetAvailablePortsAsync();
        var portList = ports.ToList();

        if (!portList.Any())
        {
            _menuService.ShowStatus("未发现可用串口", StatusType.Warning);
            return false;
        }

        var selectedPort = portList.First();
        var settings = SerialPortExtensions.CreateSettings(9600);

        var connected = await _serialPortService.ConnectAsync(selectedPort, settings);
        if (connected)
        {
            _menuService.ShowStatus($"已连接到 {selectedPort.PortName}", StatusType.Success);
        }

        return connected;
    }

    /// <summary>
    /// 演示十六进制发送扩展方法
    /// </summary>
    private async Task DemonstrateHexSendingAsync()
    {
        Console.WriteLine("\n--- 十六进制发送扩展方法演示 ---");
        
        var hexStrings = new[]
        {
            "48 65 6C 6C 6F",           // "Hello"
            "41:54:0D:0A",              // "AT\r\n" 用冒号分隔
            "DEADBEEF",                 // 无分隔符
            "01 02 03 04 05",           // 数字序列
            "FF-FE-FD-FC"               // 用短横线分隔
        };

        foreach (var hexString in hexStrings)
        {
            try
            {
                Console.WriteLine($"发送十六进制: {hexString}");
                
                var operationId = _performanceMonitor.StartOperation("十六进制发送");
                await _serialPortService.SendHexAsync(hexString);
                _performanceMonitor.EndOperation(operationId, true);
                
                // 显示转换后的字节
                var bytes = SerialPortExtensions.HexStringToBytes(hexString);
                Console.WriteLine($"  转换为字节: [{string.Join(", ", bytes.Select(b => $"0x{b:X2}"))}]");
                Console.WriteLine($"  ASCII解释: {Encoding.ASCII.GetString(bytes).Replace('\0', '.')}");
                
                _menuService.ShowStatus("十六进制发送成功", StatusType.Success);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "十六进制发送失败: {HexString}", hexString);
                _menuService.ShowStatus($"发送失败: {ex.Message}", StatusType.Error);
            }
            
            await Task.Delay(1000);
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示带换行符发送
    /// </summary>
    private async Task DemonstrateLineSendingAsync()
    {
        Console.WriteLine("\n--- 带换行符发送演示 ---");
        
        var messages = new[]
        {
            "AT",
            "Hello World",
            "测试中文",
            "Command123"
        };

        var newLineTypes = new[]
        {
            ("\r\n", "CRLF (Windows)"),
            ("\n", "LF (Unix)"),
            ("\r", "CR (Mac)"),
            (null, "默认 (CRLF)")
        };

        foreach (var (newLine, description) in newLineTypes)
        {
            Console.WriteLine($"\n使用换行符: {description}");
            
            foreach (var message in messages)
            {
                try
                {
                    Console.WriteLine($"发送: {message}");
                    
                    var operationId = _performanceMonitor.StartOperation("带换行符发送");
                    await _serialPortService.SendLineAsync(message, newLine);
                    _performanceMonitor.EndOperation(operationId, true);
                    
                    // 显示实际发送的内容
                    var actualNewLine = newLine ?? "\r\n";
                    var fullMessage = message + actualNewLine;
                    var bytes = Encoding.UTF8.GetBytes(fullMessage);
                    Console.WriteLine($"  实际发送: {bytes.ToHexString()}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "带换行符发送失败");
                    _menuService.ShowStatus($"发送失败: {ex.Message}", StatusType.Error);
                }
                
                await Task.Delay(500);
            }
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示批量发送
    /// </summary>
    private async Task DemonstrateBatchSendingAsync()
    {
        Console.WriteLine("\n--- 批量发送演示 ---");
        
        // 字节数组批量发送
        Console.WriteLine("1. 字节数组批量发送:");
        var byteDataList = new List<byte[]>
        {
            Encoding.UTF8.GetBytes("Packet1"),
            Encoding.UTF8.GetBytes("Packet2"),
            Encoding.UTF8.GetBytes("Packet3"),
            new byte[] { 0x01, 0x02, 0x03 },
            new byte[] { 0xFF, 0xFE, 0xFD }
        };

        try
        {
            var operationId = _performanceMonitor.StartOperation("批量发送字节数组");
            await _serialPortService.SendBatchAsync(byteDataList, TimeSpan.FromMilliseconds(500));
            _performanceMonitor.EndOperation(operationId, true);
            
            Console.WriteLine($"成功发送 {byteDataList.Count} 个数据包");
            _menuService.ShowStatus("字节数组批量发送完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "字节数组批量发送失败");
            _menuService.ShowStatus($"批量发送失败: {ex.Message}", StatusType.Error);
        }

        await Task.Delay(2000);

        // 字符串批量发送
        Console.WriteLine("\n2. 字符串批量发送:");
        var stringDataList = new List<string>
        {
            "Command1",
            "Command2", 
            "Command3",
            "测试命令1",
            "测试命令2"
        };

        try
        {
            var operationId = _performanceMonitor.StartOperation("批量发送字符串");
            await _serialPortService.SendBatchAsync(stringDataList, TimeSpan.FromMilliseconds(800));
            _performanceMonitor.EndOperation(operationId, true);
            
            Console.WriteLine($"成功发送 {stringDataList.Count} 个字符串");
            _menuService.ShowStatus("字符串批量发送完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "字符串批量发送失败");
            _menuService.ShowStatus($"批量发送失败: {ex.Message}", StatusType.Error);
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示等待数据功能
    /// </summary>
    private async Task DemonstrateWaitForDataAsync()
    {
        Console.WriteLine("\n--- 等待数据功能演示 ---");
        
        // 等待特定字节数据
        Console.WriteLine("1. 等待特定字节数据:");
        var expectedBytes = new byte[] { 0x4F, 0x4B }; // "OK"
        
        try
        {
            Console.WriteLine($"等待数据: {expectedBytes.ToHexString()} (超时: 5秒)");
            
            var operationId = _performanceMonitor.StartOperation("等待字节数据");
            var received = await _serialPortService.WaitForDataAsync(expectedBytes, TimeSpan.FromSeconds(5));
            _performanceMonitor.EndOperation(operationId, received);
            
            Console.WriteLine($"等待结果: {(received ? "成功接收" : "超时")}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "等待字节数据失败");
            _menuService.ShowStatus($"等待失败: {ex.Message}", StatusType.Error);
        }

        await Task.Delay(1000);

        // 等待特定字符串
        Console.WriteLine("\n2. 等待特定字符串:");
        var expectedString = "READY";
        
        try
        {
            Console.WriteLine($"等待字符串: {expectedString} (超时: 5秒)");
            
            var operationId = _performanceMonitor.StartOperation("等待字符串");
            var received = await _serialPortService.WaitForStringAsync(expectedString, TimeSpan.FromSeconds(5));
            _performanceMonitor.EndOperation(operationId, received);
            
            Console.WriteLine($"等待结果: {(received ? "成功接收" : "超时")}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "等待字符串失败");
            _menuService.ShowStatus($"等待失败: {ex.Message}", StatusType.Error);
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示校验和功能
    /// </summary>
    private async Task DemonstrateChecksumFunctionsAsync()
    {
        Console.WriteLine("\n--- 校验和功能演示 ---");
        
        var testData = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05 };
        
        Console.WriteLine($"测试数据: {testData.ToHexString()}");
        
        // 演示不同类型的校验和
        var checksumTypes = new[]
        {
            (ChecksumType.Sum, "简单求和"),
            (ChecksumType.Xor, "异或校验"),
            (ChecksumType.TwosComplement, "二进制补码")
        };

        foreach (var (type, description) in checksumTypes)
        {
            var checksum = testData.CalculateChecksum(type);
            Console.WriteLine($"{description} 校验和: 0x{checksum:X2}");
            
            // 创建带校验和的数据
            var dataWithChecksum = testData.Concat(new[] { checksum }).ToArray();
            Console.WriteLine($"  带校验和数据: {dataWithChecksum.ToHexString()}");
            
            // 验证校验和
            var isValid = dataWithChecksum.VerifyChecksum(type);
            Console.WriteLine($"  校验和验证: {(isValid ? "通过" : "失败")}");
            
            // 发送带校验和的数据
            try
            {
                await _serialPortService.SendAsync(dataWithChecksum);
                Console.WriteLine($"  已发送带{description}校验和的数据");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送校验和数据失败");
            }
            
            Console.WriteLine();
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示数据模式查找
    /// </summary>
    private async Task DemonstratePatternSearchAsync()
    {
        Console.WriteLine("\n--- 数据模式查找演示 ---");
        
        var data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x02, 0x03, 0x06, 0x07 };
        var pattern = new byte[] { 0x02, 0x03 };
        
        Console.WriteLine($"数据: {data.ToHexString()}");
        Console.WriteLine($"模式: {pattern.ToHexString()}");
        
        // 检查是否包含模式
        var contains = data.ContainsPattern(pattern);
        Console.WriteLine($"包含模式: {contains}");
        
        // 查找模式位置
        var position = data.FindPattern(pattern);
        Console.WriteLine($"模式位置: {(position >= 0 ? position.ToString() : "未找到")}");
        
        // 查找所有匹配位置
        var positions = new List<int>();
        for (int i = 0; i <= data.Length - pattern.Length; i++)
        {
            var found = true;
            for (int j = 0; j < pattern.Length; j++)
            {
                if (data[i + j] != pattern[j])
                {
                    found = false;
                    break;
                }
            }
            if (found)
            {
                positions.Add(i);
            }
        }
        
        Console.WriteLine($"所有匹配位置: [{string.Join(", ", positions)}]");
        
        // 演示在实际数据中查找协议头
        Console.WriteLine("\n协议解析示例:");
        var protocolData = new byte[] { 0xAA, 0xBB, 0x01, 0x02, 0x03, 0xCC, 0xDD, 0xAA, 0xBB, 0x04, 0x05, 0xCC, 0xDD };
        var header = new byte[] { 0xAA, 0xBB };
        var footer = new byte[] { 0xCC, 0xDD };
        
        Console.WriteLine($"协议数据: {protocolData.ToHexString()}");
        Console.WriteLine($"协议头: {header.ToHexString()}");
        Console.WriteLine($"协议尾: {footer.ToHexString()}");
        
        var headerPos = protocolData.FindPattern(header);
        var footerPos = protocolData.FindPattern(footer);
        
        if (headerPos >= 0 && footerPos >= 0 && footerPos > headerPos)
        {
            var payloadStart = headerPos + header.Length;
            var payloadLength = footerPos - payloadStart;
            var payload = protocolData.Skip(payloadStart).Take(payloadLength).ToArray();
            
            Console.WriteLine($"有效载荷: {payload.ToHexString()}");
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示串口设置创建
    /// </summary>
    private async Task DemonstrateSettingsCreationAsync()
    {
        Console.WriteLine("\n--- 串口设置创建演示 ---");
        
        // 演示预定义设置
        Console.WriteLine("1. 预定义设置:");
        
        var defaultSettings = SerialPortExtensions.CreateSettings();
        Console.WriteLine($"默认设置: {defaultSettings}");
        
        var highSpeedSettings = SerialPortExtensions.CreateHighSpeedSettings();
        Console.WriteLine($"高速设置: {highSpeedSettings}");
        Console.WriteLine($"  缓冲区: 接收={highSpeedSettings.ReceiveBufferSize}, 发送={highSpeedSettings.SendBufferSize}");
        
        var lowSpeedSettings = SerialPortExtensions.CreateLowSpeedSettings();
        Console.WriteLine($"低速设置: {lowSpeedSettings}");
        Console.WriteLine($"  缓冲区: 接收={lowSpeedSettings.ReceiveBufferSize}, 发送={lowSpeedSettings.SendBufferSize}");
        
        // 演示波特率验证
        Console.WriteLine("\n2. 波特率验证:");
        var testBaudRates = new[] { 300, 9600, 115200, 921600, 1000000, 2000000 };
        
        foreach (var baudRate in testBaudRates)
        {
            var isValid = SerialPortExtensions.IsValidBaudRate(baudRate);
            Console.WriteLine($"波特率 {baudRate}: {(isValid ? "有效" : "无效")}");
        }
        
        // 演示推荐波特率
        Console.WriteLine("\n3. 推荐波特率:");
        var recommendedRates = SerialPortExtensions.GetRecommendedBaudRates();
        Console.WriteLine($"推荐波特率: [{string.Join(", ", recommendedRates)}]");
        
        await _menuService.WaitForKeyAsync();
    }
}
