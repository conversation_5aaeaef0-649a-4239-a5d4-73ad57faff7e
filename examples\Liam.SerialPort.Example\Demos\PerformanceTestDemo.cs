using Microsoft.Extensions.Logging;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;
using Liam.SerialPort.Example.Services;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text;

namespace Liam.SerialPort.Example.Demos;

/// <summary>
/// 性能测试演示
/// 展示吞吐量测试、延迟测试、压力测试等性能相关功能
/// </summary>
public class PerformanceTestDemo
{
    private readonly ILogger<PerformanceTestDemo> _logger;
    private readonly ISerialPortService _serialPortService;
    private readonly IMenuService _menuService;
    private readonly IPerformanceMonitor _performanceMonitor;

    /// <summary>
    /// 构造函数
    /// </summary>
    public PerformanceTestDemo(
        ILogger<PerformanceTestDemo> logger,
        ISerialPortService serialPortService,
        IMenuService menuService,
        IPerformanceMonitor performanceMonitor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serialPortService = serialPortService ?? throw new ArgumentNullException(nameof(serialPortService));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
    }

    /// <summary>
    /// 运行性能测试演示
    /// </summary>
    public async Task<bool> RunAsync()
    {
        _menuService.ClearScreen();
        
        Console.ForegroundColor = ConsoleColor.Yellow;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    性能测试演示                              ║");
        Console.WriteLine("║  演示内容：吞吐量测试、延迟测试、压力测试、并发测试          ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();

        try
        {
            // 确保连接
            if (!await EnsureConnectionAsync())
            {
                return true;
            }

            // 1. 吞吐量测试
            await DemonstrateThroughputTestAsync();

            // 2. 延迟测试
            await DemonstrateLatencyTestAsync();

            // 3. 压力测试
            await DemonstrateStressTestAsync();

            // 4. 并发测试
            await DemonstrateConcurrencyTestAsync();

            _menuService.ShowStatus("性能测试演示完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "性能测试演示过程中发生错误");
            _menuService.ShowStatus($"演示失败: {ex.Message}", StatusType.Error);
        }
        finally
        {
            if (_serialPortService.IsConnected)
            {
                await _serialPortService.DisconnectAsync();
            }
        }

        return true;
    }

    /// <summary>
    /// 确保串口连接
    /// </summary>
    private async Task<bool> EnsureConnectionAsync()
    {
        if (_serialPortService.IsConnected)
        {
            return true;
        }

        var ports = await _serialPortService.GetAvailablePortsAsync();
        var portList = ports.ToList();

        if (!portList.Any())
        {
            _menuService.ShowStatus("未发现可用串口，无法进行性能测试", StatusType.Warning);
            return false;
        }

        var selectedPort = portList.First();
        var settings = SerialPortSettings.Default;
        settings.BaudRate = 115200; // 使用高波特率进行性能测试

        Console.WriteLine($"连接到 {selectedPort.PortName} (波特率: {settings.BaudRate})...");
        var connected = await _serialPortService.ConnectAsync(selectedPort, settings);

        if (!connected)
        {
            _menuService.ShowStatus("连接失败，无法进行性能测试", StatusType.Error);
            return false;
        }

        _menuService.ShowStatus($"已连接到 {selectedPort.PortName}", StatusType.Success);
        return true;
    }

    /// <summary>
    /// 演示吞吐量测试
    /// </summary>
    private async Task DemonstrateThroughputTestAsync()
    {
        _menuService.ShowStatus("正在进行吞吐量测试...", StatusType.Info);
        
        Console.WriteLine("\n--- 吞吐量测试 ---");
        
        var testDuration = TimeSpan.FromSeconds(10);
        var packetSize = 1024; // 1KB 数据包
        var testData = new byte[packetSize];
        new Random().NextBytes(testData);
        
        var stopwatch = Stopwatch.StartNew();
        var packetsSent = 0;
        var totalBytesSent = 0L;
        
        Console.WriteLine($"测试参数: 数据包大小={packetSize}字节, 测试时长={testDuration.TotalSeconds}秒");
        Console.WriteLine("开始发送数据包...");
        
        while (stopwatch.Elapsed < testDuration)
        {
            try
            {
                await _serialPortService.SendAsync(testData);
                packetsSent++;
                totalBytesSent += packetSize;
                
                if (packetsSent % 10 == 0)
                {
                    var throughput = totalBytesSent / stopwatch.Elapsed.TotalSeconds / 1024; // KB/s
                    Console.Write($"\r已发送: {packetsSent} 包 | 吞吐量: {throughput:F2} KB/s");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "发送数据包失败");
            }
        }
        
        stopwatch.Stop();
        
        var finalThroughput = totalBytesSent / stopwatch.Elapsed.TotalSeconds / 1024; // KB/s
        var packetsPerSecond = packetsSent / stopwatch.Elapsed.TotalSeconds;
        
        Console.WriteLine($"\n\n=== 吞吐量测试结果 ===");
        Console.WriteLine($"测试时长: {stopwatch.Elapsed.TotalSeconds:F2} 秒");
        Console.WriteLine($"发送数据包: {packetsSent} 个");
        Console.WriteLine($"总字节数: {totalBytesSent:N0} 字节 ({totalBytesSent / 1024.0:F2} KB)");
        Console.WriteLine($"平均吞吐量: {finalThroughput:F2} KB/s");
        Console.WriteLine($"包发送率: {packetsPerSecond:F2} 包/秒");
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示延迟测试
    /// </summary>
    private async Task DemonstrateLatencyTestAsync()
    {
        _menuService.ShowStatus("正在进行延迟测试...", StatusType.Info);
        
        Console.WriteLine("\n--- 延迟测试 ---");
        
        var testCount = 50;
        var latencies = new List<double>();
        
        Console.WriteLine($"测试参数: 测试次数={testCount}");
        Console.WriteLine("开始延迟测试...");
        
        for (int i = 1; i <= testCount; i++)
        {
            try
            {
                var testMessage = $"LATENCY_TEST_{i:D3}";
                var stopwatch = Stopwatch.StartNew();
                
                // 发送数据并等待响应（模拟）
                await _serialPortService.SendAsync(testMessage);
                await Task.Delay(10); // 模拟网络延迟
                
                stopwatch.Stop();
                var latency = stopwatch.Elapsed.TotalMilliseconds;
                latencies.Add(latency);
                
                Console.Write($"\r测试 {i}/{testCount}: {latency:F2}ms");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "延迟测试失败: {TestIndex}", i);
            }
        }
        
        if (latencies.Any())
        {
            var avgLatency = latencies.Average();
            var minLatency = latencies.Min();
            var maxLatency = latencies.Max();
            var p95Latency = latencies.OrderBy(x => x).Skip((int)(latencies.Count * 0.95)).First();
            
            Console.WriteLine($"\n\n=== 延迟测试结果 ===");
            Console.WriteLine($"测试次数: {latencies.Count}");
            Console.WriteLine($"平均延迟: {avgLatency:F2} ms");
            Console.WriteLine($"最小延迟: {minLatency:F2} ms");
            Console.WriteLine($"最大延迟: {maxLatency:F2} ms");
            Console.WriteLine($"P95延迟: {p95Latency:F2} ms");
            
            var jitter = latencies.Select(x => Math.Abs(x - avgLatency)).Average();
            Console.WriteLine($"延迟抖动: {jitter:F2} ms");
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示压力测试
    /// </summary>
    private async Task DemonstrateStressTestAsync()
    {
        _menuService.ShowStatus("正在进行压力测试...", StatusType.Info);
        
        Console.WriteLine("\n--- 压力测试 ---");
        Console.WriteLine("将在短时间内发送大量数据，测试系统稳定性");
        
        var testDuration = TimeSpan.FromSeconds(15);
        var maxConcurrency = 5;
        var packetSize = 512;
        
        Console.WriteLine($"测试参数: 并发数={maxConcurrency}, 数据包大小={packetSize}字节, 测试时长={testDuration.TotalSeconds}秒");
        
        var cts = new CancellationTokenSource(testDuration);
        var tasks = new List<Task>();
        var totalPackets = 0;
        var successPackets = 0;
        var failedPackets = 0;
        var lockObject = new object();
        
        for (int i = 0; i < maxConcurrency; i++)
        {
            var workerId = i + 1;
            var task = Task.Run(async () =>
            {
                var testData = new byte[packetSize];
                new Random().NextBytes(testData);
                var workerPackets = 0;
                
                while (!cts.Token.IsCancellationRequested)
                {
                    try
                    {
                        await _serialPortService.SendAsync(testData);
                        
                        lock (lockObject)
                        {
                            successPackets++;
                            totalPackets++;
                        }
                        
                        workerPackets++;
                        
                        if (workerPackets % 20 == 0)
                        {
                            Console.Write($"\r工作线程 {workerId}: {workerPackets} 包 | 总成功: {successPackets} | 总失败: {failedPackets}");
                        }
                    }
                    catch (Exception)
                    {
                        lock (lockObject)
                        {
                            failedPackets++;
                            totalPackets++;
                        }
                    }
                    
                    await Task.Delay(50, cts.Token); // 控制发送频率
                }
            }, cts.Token);
            
            tasks.Add(task);
        }
        
        Console.WriteLine("压力测试进行中...");
        await Task.WhenAll(tasks);
        
        Console.WriteLine($"\n\n=== 压力测试结果 ===");
        Console.WriteLine($"总数据包: {totalPackets}");
        Console.WriteLine($"成功发送: {successPackets}");
        Console.WriteLine($"发送失败: {failedPackets}");
        Console.WriteLine($"成功率: {(totalPackets > 0 ? (double)successPackets / totalPackets * 100 : 0):F2}%");
        Console.WriteLine($"平均发送率: {totalPackets / testDuration.TotalSeconds:F2} 包/秒");
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示并发测试
    /// </summary>
    private async Task DemonstrateConcurrencyTestAsync()
    {
        _menuService.ShowStatus("正在进行并发测试...", StatusType.Info);
        
        Console.WriteLine("\n--- 并发测试 ---");
        Console.WriteLine("测试多线程同时访问串口的并发安全性");
        
        var concurrentTasks = 10;
        var tasksPerThread = 20;
        
        Console.WriteLine($"测试参数: 并发任务数={concurrentTasks}, 每任务操作数={tasksPerThread}");
        
        var tasks = new List<Task>();
        var results = new ConcurrentBag<TaskResult>();
        
        for (int i = 0; i < concurrentTasks; i++)
        {
            var taskId = i + 1;
            var task = Task.Run(async () =>
            {
                var taskResult = new TaskResult { TaskId = taskId };
                var stopwatch = Stopwatch.StartNew();
                
                for (int j = 1; j <= tasksPerThread; j++)
                {
                    try
                    {
                        var message = $"CONCURRENT_T{taskId}_M{j}";
                        await _serialPortService.SendAsync(message);
                        taskResult.SuccessCount++;
                    }
                    catch (Exception)
                    {
                        taskResult.FailureCount++;
                    }
                }
                
                stopwatch.Stop();
                taskResult.Duration = stopwatch.Elapsed;
                results.Add(taskResult);
                
                Console.WriteLine($"任务 {taskId} 完成: 成功={taskResult.SuccessCount}, 失败={taskResult.FailureCount}, 耗时={taskResult.Duration.TotalMilliseconds:F0}ms");
            });
            
            tasks.Add(task);
        }
        
        Console.WriteLine("并发测试进行中...");
        await Task.WhenAll(tasks);
        
        var totalSuccess = results.Sum(r => r.SuccessCount);
        var totalFailure = results.Sum(r => r.FailureCount);
        var totalOperations = totalSuccess + totalFailure;
        var avgDuration = results.Average(r => r.Duration.TotalMilliseconds);
        
        Console.WriteLine($"\n=== 并发测试结果 ===");
        Console.WriteLine($"总操作数: {totalOperations}");
        Console.WriteLine($"成功操作: {totalSuccess}");
        Console.WriteLine($"失败操作: {totalFailure}");
        Console.WriteLine($"成功率: {(totalOperations > 0 ? (double)totalSuccess / totalOperations * 100 : 0):F2}%");
        Console.WriteLine($"平均任务耗时: {avgDuration:F2} ms");
        Console.WriteLine($"并发安全性: {(totalFailure == 0 ? "良好" : "存在问题")}");
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 任务结果
    /// </summary>
    private class TaskResult
    {
        public int TaskId { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public TimeSpan Duration { get; set; }
    }
}
