{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Liam.SerialPort": "Debug", "Liam.SerialPort.Example": "Debug"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}, "SerialPort": {"DefaultBaudRate": 9600, "DefaultTimeout": 5000, "AutoReconnect": true, "AutoReconnectInterval": 5000, "MaxAutoReconnectAttempts": 5, "EnableDataLogging": false, "MaxLogSize": 1048576}, "Performance": {"EnableMonitoring": true, "MetricsUpdateInterval": 1000, "EnableRealTimeMetrics": true}, "Example": {"ShowWelcomeMessage": true, "DefaultDemoTimeout": 30, "EnableDetailedOutput": true}}