namespace Liam.SerialPort.Example.Services;

/// <summary>
/// 性能监控接口
/// 负责监控和统计应用程序的性能指标
/// </summary>
public interface IPerformanceMonitor
{
    /// <summary>
    /// 开始监控
    /// </summary>
    void Start();

    /// <summary>
    /// 停止监控
    /// </summary>
    void Stop();

    /// <summary>
    /// 记录操作开始
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <returns>操作ID</returns>
    string StartOperation(string operationName);

    /// <summary>
    /// 记录操作完成
    /// </summary>
    /// <param name="operationId">操作ID</param>
    /// <param name="success">是否成功</param>
    void EndOperation(string operationId, bool success = true);

    /// <summary>
    /// 记录数据传输
    /// </summary>
    /// <param name="bytesTransferred">传输的字节数</param>
    /// <param name="direction">传输方向</param>
    void RecordDataTransfer(long bytesTransferred, TransferDirection direction);

    /// <summary>
    /// 记录连接事件
    /// </summary>
    /// <param name="eventType">事件类型</param>
    /// <param name="portName">串口名称</param>
    void RecordConnectionEvent(ConnectionEventType eventType, string portName);

    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    /// <returns>性能统计</returns>
    PerformanceStatistics GetStatistics();

    /// <summary>
    /// 重置统计信息
    /// </summary>
    void Reset();

    /// <summary>
    /// 获取实时性能指标
    /// </summary>
    /// <returns>实时性能指标</returns>
    RealTimeMetrics GetRealTimeMetrics();
}

/// <summary>
/// 传输方向
/// </summary>
public enum TransferDirection
{
    /// <summary>
    /// 发送
    /// </summary>
    Send,

    /// <summary>
    /// 接收
    /// </summary>
    Receive
}

/// <summary>
/// 连接事件类型
/// </summary>
public enum ConnectionEventType
{
    /// <summary>
    /// 连接建立
    /// </summary>
    Connected,

    /// <summary>
    /// 连接断开
    /// </summary>
    Disconnected,

    /// <summary>
    /// 连接失败
    /// </summary>
    ConnectionFailed,

    /// <summary>
    /// 自动重连
    /// </summary>
    AutoReconnect,

    /// <summary>
    /// 设备热插拔
    /// </summary>
    DeviceHotplug
}

/// <summary>
/// 性能统计信息
/// </summary>
public class PerformanceStatistics
{
    /// <summary>
    /// 总运行时间
    /// </summary>
    public TimeSpan TotalRunTime { get; set; }

    /// <summary>
    /// 总操作数
    /// </summary>
    public long TotalOperations { get; set; }

    /// <summary>
    /// 成功操作数
    /// </summary>
    public long SuccessfulOperations { get; set; }

    /// <summary>
    /// 失败操作数
    /// </summary>
    public long FailedOperations { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalOperations > 0 ? (double)SuccessfulOperations / TotalOperations : 0;

    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    public double AverageResponseTime { get; set; }

    /// <summary>
    /// 最小响应时间（毫秒）
    /// </summary>
    public double MinResponseTime { get; set; }

    /// <summary>
    /// 最大响应时间（毫秒）
    /// </summary>
    public double MaxResponseTime { get; set; }

    /// <summary>
    /// 总发送字节数
    /// </summary>
    public long TotalBytesSent { get; set; }

    /// <summary>
    /// 总接收字节数
    /// </summary>
    public long TotalBytesReceived { get; set; }

    /// <summary>
    /// 总连接数
    /// </summary>
    public long TotalConnections { get; set; }

    /// <summary>
    /// 连接失败数
    /// </summary>
    public long ConnectionFailures { get; set; }

    /// <summary>
    /// 自动重连次数
    /// </summary>
    public long AutoReconnectCount { get; set; }

    /// <summary>
    /// 内存使用量（字节）
    /// </summary>
    public long MemoryUsage { get; set; }

    /// <summary>
    /// CPU使用率（百分比）
    /// </summary>
    public double CpuUsage { get; set; }
}

/// <summary>
/// 实时性能指标
/// </summary>
public class RealTimeMetrics
{
    /// <summary>
    /// 当前活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// 每秒操作数
    /// </summary>
    public double OperationsPerSecond { get; set; }

    /// <summary>
    /// 每秒发送字节数
    /// </summary>
    public double BytesPerSecondSent { get; set; }

    /// <summary>
    /// 每秒接收字节数
    /// </summary>
    public double BytesPerSecondReceived { get; set; }

    /// <summary>
    /// 当前响应时间（毫秒）
    /// </summary>
    public double CurrentResponseTime { get; set; }

    /// <summary>
    /// 错误率（百分比）
    /// </summary>
    public double ErrorRate { get; set; }

    /// <summary>
    /// 内存增长率（字节/秒）
    /// </summary>
    public double MemoryGrowthRate { get; set; }
}
