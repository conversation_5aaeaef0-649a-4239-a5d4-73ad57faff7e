using Liam.Logging.Extensions;
using Liam.Logging.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Liam.Logging.Example;

/// <summary>
/// 简化的测试程序，用于验证基本功能
/// </summary>
class TestProgram
{
    /// <summary>
    /// 测试程序入口点
    /// </summary>
    /// <param name="args">命令行参数</param>
    /// <returns>异步任务</returns>
    public static async Task TestMain(string[] args)
    {
        try
        {
            Console.WriteLine("=== Liam.Logging 基本功能测试 ===");
            
            // 创建服务集合
            var services = new ServiceCollection();

            // 添加基本配置
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    { "Logging:MinimumLevel", "Debug" },
                    { "Logging:EnableAsync", "true" },
                    { "Logging:ApplicationName", "Liam.Logging.Test" }
                })
                .Build();

            services.AddSingleton<IConfiguration>(configuration);

            // 注册Liam日志记录服务
            services.AddLiamLogging(configuration, "Logging");

            // 添加控制台日志提供程序
            services.AddConsoleLogging(config =>
            {
                config.EnableColors = true;
                config.TimestampFormat = "yyyy-MM-dd HH:mm:ss.fff";
            });

            // 添加文件日志提供程序
            services.AddFileLogging(config =>
            {
                config.FilePath = "logs/test.log";
                config.EnableRotation = true;
                config.MaxFileSize = 10 * 1024 * 1024; // 10MB
                config.RetainedFileCount = 5;
            });

            var serviceProvider = services.BuildServiceProvider();

            // 获取日志记录器
            var logger = serviceProvider.GetRequiredService<ILiamLogger>();

            Console.WriteLine("1. 测试基本日志记录...");
            
            // 测试不同级别的日志
            logger.LogTrace("这是一条跟踪日志");
            logger.LogDebug("这是一条调试日志");
            logger.LogInformation("这是一条信息日志");
            logger.LogWarning("这是一条警告日志");
            logger.LogError("这是一条错误日志");
            logger.LogCritical("这是一条严重错误日志");

            Console.WriteLine("2. 测试结构化日志记录...");
            
            // 测试结构化日志
            logger.LogStructured(Liam.Logging.Constants.LogLevel.Information,
                "用户 {UserId} 在 {LoginTime} 登录系统",
                12345, DateTime.Now);

            Console.WriteLine("3. 测试异步日志记录...");
            
            // 测试异步日志
            await logger.LogAsync(Liam.Logging.Constants.LogLevel.Information,
                "这是一条异步日志消息");

            Console.WriteLine("4. 测试异常日志记录...");
            
            // 测试异常日志
            try
            {
                int zero = 0;
                var result = 10 / zero;
            }
            catch (Exception ex)
            {
                logger.LogError("测试异常记录", ex);
            }

            Console.WriteLine("5. 测试日志作用域...");
            
            // 测试日志作用域
            using (logger.BeginScope(new { UserId = 12345, Operation = "TestOperation" }))
            {
                logger.LogInformation("作用域内的日志消息");
                
                using (logger.BeginScope("嵌套作用域"))
                {
                    logger.LogInformation("嵌套作用域内的日志消息");
                }
            }

            Console.WriteLine("6. 等待异步日志完成...");
            await Task.Delay(2000); // 等待异步日志完成

            serviceProvider.Dispose();

            Console.WriteLine("=== 测试完成 ===");
            Console.WriteLine("请检查控制台输出和 logs/test.log 文件");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试程序运行出现异常: {ex.Message}");
            Console.WriteLine($"异常详情: {ex}");
        }
    }
}
