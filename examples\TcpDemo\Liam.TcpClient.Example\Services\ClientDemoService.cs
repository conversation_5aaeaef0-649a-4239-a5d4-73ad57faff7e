using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using System.Text;
using System.Net;

namespace Liam.TcpClient.Example.Services;

/// <summary>
/// TCP客户端演示服务
/// 提供各种TCP客户端功能的演示和测试
/// </summary>
public class ClientDemoService
{
    private readonly ILogger<ClientDemoService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ITcpClient _tcpClient;
    private readonly List<ITcpClient> _activeClients;
    private readonly object _lockObject = new();

    public ClientDemoService(
        ILogger<ClientDemoService> logger,
        IConfiguration configuration,
        ITcpClient tcpClient)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _tcpClient = tcpClient ?? throw new ArgumentNullException(nameof(tcpClient));
        _activeClients = new List<ITcpClient>();
    }

    /// <summary>
    /// 基本连接测试
    /// </summary>
    public async Task BasicConnectionTestAsync()
    {
        Console.WriteLine("=== 基本连接测试 ===");
        
        try
        {
            var serverHost = _configuration["TcpClient:ServerHost"] ?? "localhost";
            var serverPort = _configuration.GetValue<int>("TcpClient:ServerPort", 8080);
            
            Console.WriteLine($"正在连接到服务器: {serverHost}:{serverPort}");
            
            // 配置客户端
            var config = new TcpClientConfig
            {
                ServerHost = serverHost,
                ServerPort = serverPort,
                ConnectTimeout = TimeSpan.FromSeconds(30),
                ReceiveTimeout = TimeSpan.FromSeconds(30),
                BufferSize = 4096,
                EnableSsl = false,
                EnableHeartbeat = false,
                EnableAutoReconnect = false
            };

            // 注册事件处理器
            RegisterEventHandlers(_tcpClient);

            // 连接到服务器
            await _tcpClient.ConnectAsync(config);
            
            lock (_lockObject)
            {
                _activeClients.Add(_tcpClient);
            }

            Console.WriteLine("连接成功！");
            Console.WriteLine("客户端配置:");
            Console.WriteLine($"  - 服务器地址: {config.ServerHost}:{config.ServerPort}");
            Console.WriteLine($"  - 连接超时: {config.ConnectTimeout.TotalSeconds} 秒");
            Console.WriteLine($"  - 缓冲区大小: {config.BufferSize} 字节");
            Console.WriteLine($"  - SSL/TLS: {config.EnableSsl}");
            
            // 发送测试消息
            var testMessage = "Hello from Liam.TcpClient!";
            Console.WriteLine($"\n发送测试消息: {testMessage}");
            await _tcpClient.SendAsync(Encoding.UTF8.GetBytes(testMessage));
            
            // 等待响应
            Console.WriteLine("等待服务器响应...");
            await Task.Delay(2000);
            
            _logger.LogInformation("基本连接测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"连接测试失败: {ex.Message}");
            _logger.LogError(ex, "基本连接测试失败");
        }
    }

    /// <summary>
    /// SSL/TLS连接测试
    /// </summary>
    public async Task SslConnectionTestAsync()
    {
        Console.WriteLine("=== SSL/TLS连接测试 ===");
        Console.WriteLine("注意: 此测试需要服务器支持SSL/TLS");
        
        try
        {
            var serverHost = _configuration["TcpClient:ServerHost"] ?? "localhost";
            var sslPort = 8443; // SSL端口
            
            var config = new TcpClientConfig
            {
                ServerHost = serverHost,
                ServerPort = sslPort,
                EnableSsl = true,
                SslServerName = serverHost,
                ValidateServerCertificate = false, // 测试环境可以关闭证书验证
                ConnectTimeout = TimeSpan.FromSeconds(30)
            };

            Console.WriteLine($"正在建立SSL连接到: {serverHost}:{sslPort}");
            
            RegisterEventHandlers(_tcpClient);
            await _tcpClient.ConnectAsync(config);
            
            Console.WriteLine("SSL连接建立成功！");
            Console.WriteLine("SSL配置:");
            Console.WriteLine($"  - 服务器名称: {config.SslServerName}");
            Console.WriteLine($"  - 证书验证: {config.ValidateServerCertificate}");
            
            // 发送加密消息
            var message = "Encrypted message from SSL client";
            Console.WriteLine($"\n发送加密消息: {message}");
            await _tcpClient.SendAsync(Encoding.UTF8.GetBytes(message));
            
            await Task.Delay(2000);
            
            _logger.LogInformation("SSL/TLS连接测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"SSL连接测试失败: {ex.Message}");
            _logger.LogError(ex, "SSL/TLS连接测试失败");
        }
    }

    /// <summary>
    /// 连接池演示
    /// </summary>
    public async Task ConnectionPoolDemoAsync()
    {
        Console.WriteLine("=== 连接池演示 ===");
        
        try
        {
            var poolSize = _configuration.GetValue<int>("TcpClient:ConnectionPoolSize", 5);
            var serverHost = _configuration["TcpClient:ServerHost"] ?? "localhost";
            var serverPort = _configuration.GetValue<int>("TcpClient:ServerPort", 8080);
            
            Console.WriteLine($"创建连接池，大小: {poolSize}");
            
            var clients = new List<ITcpClient>();
            var tasks = new List<Task>();
            
            // 创建多个客户端连接
            for (int i = 0; i < poolSize; i++)
            {
                var clientId = i + 1;
                tasks.Add(CreatePooledConnectionAsync(clientId, serverHost, serverPort));
            }
            
            await Task.WhenAll(tasks);
            
            Console.WriteLine($"连接池创建完成，共 {poolSize} 个连接");
            
            // 测试并发消息发送
            Console.WriteLine("\n测试并发消息发送...");
            var sendTasks = new List<Task>();
            
            lock (_lockObject)
            {
                for (int i = 0; i < _activeClients.Count; i++)
                {
                    var client = _activeClients[i];
                    var messageId = i + 1;
                    sendTasks.Add(SendPooledMessageAsync(client, messageId));
                }
            }
            
            await Task.WhenAll(sendTasks);
            
            Console.WriteLine("连接池测试完成");
            _logger.LogInformation("连接池演示完成，连接数: {Count}", poolSize);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"连接池演示失败: {ex.Message}");
            _logger.LogError(ex, "连接池演示失败");
        }
    }

    /// <summary>
    /// 心跳检测演示
    /// </summary>
    public async Task HeartbeatDemoAsync()
    {
        Console.WriteLine("=== 心跳检测演示 ===");

        try
        {
            var serverHost = _configuration["TcpClient:ServerHost"] ?? "localhost";
            var serverPort = _configuration.GetValue<int>("TcpClient:ServerPort", 8080);

            var config = new TcpClientConfig
            {
                ServerHost = serverHost,
                ServerPort = serverPort,
                EnableHeartbeat = true,
                HeartbeatInterval = TimeSpan.FromSeconds(5),
                HeartbeatTimeout = TimeSpan.FromSeconds(3)
            };

            Console.WriteLine("启动带心跳检测的客户端...");
            Console.WriteLine($"心跳间隔: {config.HeartbeatInterval.TotalSeconds} 秒");
            Console.WriteLine($"心跳超时: {config.HeartbeatTimeout.TotalSeconds} 秒");

            RegisterEventHandlers(_tcpClient);
            await _tcpClient.ConnectAsync(config);

            Console.WriteLine("心跳检测客户端已连接");
            Console.WriteLine("将定期发送心跳包到服务器...");

            // 监控心跳状态30秒
            Console.WriteLine("\n监控心跳状态 (30秒):");
            for (int i = 0; i < 30; i++)
            {
                await Task.Delay(1000);
                var isConnected = _tcpClient.IsConnected;
                var quality = await _tcpClient.GetConnectionQualityAsync();

                Console.Write($"\r时间: {i+1,2}s | 连接状态: {(isConnected ? "正常" : "断开")} | " +
                            $"延迟: {quality.Latency.TotalMilliseconds,6:F1}ms | " +
                            $"质量: {quality.Quality:P0}");
            }
            Console.WriteLine();

            _logger.LogInformation("心跳检测演示完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"心跳检测演示失败: {ex.Message}");
            _logger.LogError(ex, "心跳检测演示失败");
        }
    }

    /// <summary>
    /// 自动重连演示
    /// </summary>
    public async Task AutoReconnectDemoAsync()
    {
        Console.WriteLine("=== 自动重连演示 ===");

        try
        {
            var serverHost = _configuration["TcpClient:ServerHost"] ?? "localhost";
            var serverPort = _configuration.GetValue<int>("TcpClient:ServerPort", 8080);

            var config = new TcpClientConfig
            {
                ServerHost = serverHost,
                ServerPort = serverPort,
                EnableAutoReconnect = true,
                ReconnectInterval = TimeSpan.FromSeconds(3),
                MaxReconnectAttempts = 5
            };

            Console.WriteLine("启动带自动重连的客户端...");
            Console.WriteLine($"重连间隔: {config.ReconnectInterval.TotalSeconds} 秒");
            Console.WriteLine($"最大重连次数: {config.MaxReconnectAttempts}");

            RegisterEventHandlers(_tcpClient);
            await _tcpClient.ConnectAsync(config);

            Console.WriteLine("客户端已连接，自动重连功能已启用");
            Console.WriteLine("如果连接断开，客户端将自动尝试重连");

            // 模拟连接监控
            Console.WriteLine("\n连接状态监控 (60秒):");
            for (int i = 0; i < 60; i++)
            {
                await Task.Delay(1000);
                var isConnected = _tcpClient.IsConnected;
                var stats = await _tcpClient.GetStatisticsAsync();

                Console.Write($"\r时间: {i+1,2}s | 连接: {(isConnected ? "正常" : "断开")} | " +
                            $"重连次数: {stats.ReconnectCount,2} | " +
                            $"消息数: {stats.MessagesSent,3}");

                // 每10秒发送一条消息测试连接
                if (i % 10 == 0 && isConnected)
                {
                    try
                    {
                        var message = $"Test message at {DateTime.Now:HH:mm:ss}";
                        await _tcpClient.SendAsync(Encoding.UTF8.GetBytes(message));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "发送测试消息失败");
                    }
                }
            }
            Console.WriteLine();

            _logger.LogInformation("自动重连演示完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"自动重连演示失败: {ex.Message}");
            _logger.LogError(ex, "自动重连演示失败");
        }
    }

    /// <summary>
    /// 性能监控演示
    /// </summary>
    public async Task PerformanceMonitoringDemoAsync()
    {
        Console.WriteLine("=== 性能监控演示 ===");

        try
        {
            if (!_tcpClient.IsConnected)
            {
                Console.WriteLine("请先建立连接");
                return;
            }

            var stats = await _tcpClient.GetStatisticsAsync();
            var quality = await _tcpClient.GetConnectionQualityAsync();

            Console.WriteLine("客户端性能指标:");
            Console.WriteLine($"  - 连接状态: {(_tcpClient.IsConnected ? "已连接" : "未连接")}");
            Console.WriteLine($"  - 连接时长: {stats.ConnectionDuration}");
            Console.WriteLine($"  - 发送消息数: {stats.MessagesSent}");
            Console.WriteLine($"  - 接收消息数: {stats.MessagesReceived}");
            Console.WriteLine($"  - 发送字节数: {stats.BytesSent:N0}");
            Console.WriteLine($"  - 接收字节数: {stats.BytesReceived:N0}");
            Console.WriteLine($"  - 重连次数: {stats.ReconnectCount}");
            Console.WriteLine($"  - 错误次数: {stats.ErrorCount}");

            Console.WriteLine("\n连接质量指标:");
            Console.WriteLine($"  - 网络延迟: {quality.Latency.TotalMilliseconds:F1} ms");
            Console.WriteLine($"  - 连接质量: {quality.Quality:P1}");
            Console.WriteLine($"  - 丢包率: {quality.PacketLoss:P2}");
            Console.WriteLine($"  - 吞吐量: {quality.Throughput:F1} KB/s");

            // 实时性能监控
            Console.WriteLine("\n开始实时性能监控 (20秒)...");
            for (int i = 0; i < 20; i++)
            {
                await Task.Delay(1000);

                var currentStats = await _tcpClient.GetStatisticsAsync();
                var currentQuality = await _tcpClient.GetConnectionQualityAsync();

                Console.Write($"\r延迟: {currentQuality.Latency.TotalMilliseconds,6:F1}ms | " +
                            $"质量: {currentQuality.Quality,6:P0} | " +
                            $"消息: {currentStats.MessagesSent,4} | " +
                            $"内存: {GC.GetTotalMemory(false) / 1024 / 1024,6:F1}MB");
            }
            Console.WriteLine();

            _logger.LogInformation("性能监控演示完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"性能监控演示失败: {ex.Message}");
            _logger.LogError(ex, "性能监控演示失败");
        }
    }

    /// <summary>
    /// 并发连接测试
    /// </summary>
    public async Task ConcurrentConnectionTestAsync()
    {
        Console.WriteLine("=== 并发连接测试 ===");

        Console.Write("请输入并发连接数 (默认10): ");
        var input = Console.ReadLine();
        if (!int.TryParse(input, out int connectionCount) || connectionCount <= 0)
        {
            connectionCount = 10;
        }

        Console.Write("请输入测试持续时间(秒) (默认30): ");
        input = Console.ReadLine();
        if (!int.TryParse(input, out int duration) || duration <= 0)
        {
            duration = 30;
        }

        try
        {
            Console.WriteLine($"开始并发连接测试: {connectionCount} 个连接，持续 {duration} 秒");

            var serverHost = _configuration["TcpClient:ServerHost"] ?? "localhost";
            var serverPort = _configuration.GetValue<int>("TcpClient:ServerPort", 8080);

            var tasks = new List<Task>();
            var startTime = DateTime.Now;

            // 创建并发连接
            for (int i = 0; i < connectionCount; i++)
            {
                var clientId = i + 1;
                tasks.Add(CreateConcurrentClientAsync(clientId, serverHost, serverPort, duration));

                // 每5个连接暂停一下
                if (i % 5 == 0)
                {
                    await Task.Delay(100);
                }
            }

            Console.WriteLine($"已启动 {connectionCount} 个并发客户端");

            // 等待所有任务完成
            await Task.WhenAll(tasks);

            var endTime = DateTime.Now;
            var totalTime = endTime - startTime;

            Console.WriteLine($"\n并发连接测试完成:");
            Console.WriteLine($"  - 总耗时: {totalTime.TotalSeconds:F1} 秒");
            Console.WriteLine($"  - 并发连接数: {connectionCount}");
            Console.WriteLine($"  - 平均连接时间: {totalTime.TotalMilliseconds / connectionCount:F1} ms");

            _logger.LogInformation("并发连接测试完成，连接数: {Count}, 耗时: {Duration}秒",
                connectionCount, totalTime.TotalSeconds);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"并发连接测试失败: {ex.Message}");
            _logger.LogError(ex, "并发连接测试失败");
        }
    }

    /// <summary>
    /// 消息收发测试
    /// </summary>
    public async Task MessageTransferTestAsync()
    {
        Console.WriteLine("=== 消息收发测试 ===");

        try
        {
            if (!_tcpClient.IsConnected)
            {
                Console.WriteLine("请先建立连接");
                return;
            }

            Console.Write("请输入要发送的消息数量 (默认100): ");
            var input = Console.ReadLine();
            if (!int.TryParse(input, out int messageCount) || messageCount <= 0)
            {
                messageCount = 100;
            }

            Console.WriteLine($"开始发送 {messageCount} 条消息...");

            var startTime = DateTime.Now;
            var tasks = new List<Task>();

            for (int i = 0; i < messageCount; i++)
            {
                var messageId = i + 1;
                tasks.Add(SendTestMessageAsync(messageId));

                // 每10条消息暂停一下
                if (i % 10 == 0)
                {
                    await Task.Delay(10);
                }
            }

            await Task.WhenAll(tasks);

            var endTime = DateTime.Now;
            var totalTime = endTime - startTime;

            Console.WriteLine($"\n消息发送完成:");
            Console.WriteLine($"  - 发送消息数: {messageCount}");
            Console.WriteLine($"  - 总耗时: {totalTime.TotalMilliseconds:F1} ms");
            Console.WriteLine($"  - 平均耗时: {totalTime.TotalMilliseconds / messageCount:F2} ms/消息");
            Console.WriteLine($"  - 吞吐量: {messageCount / totalTime.TotalSeconds:F1} 消息/秒");

            _logger.LogInformation("消息收发测试完成，消息数: {Count}, 耗时: {Duration}ms",
                messageCount, totalTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"消息收发测试失败: {ex.Message}");
            _logger.LogError(ex, "消息收发测试失败");
        }
    }

    /// <summary>
    /// 显示客户端状态
    /// </summary>
    public async Task ShowClientStatusAsync()
    {
        Console.WriteLine("=== 客户端状态 ===");

        try
        {
            lock (_lockObject)
            {
                Console.WriteLine($"活跃客户端数量: {_activeClients.Count}");
            }

            if (_tcpClient.IsConnected)
            {
                var stats = await _tcpClient.GetStatisticsAsync();
                var quality = await _tcpClient.GetConnectionQualityAsync();

                Console.WriteLine("\n主客户端详细状态:");
                Console.WriteLine($"  连接状态: 已连接");
                Console.WriteLine($"  服务器地址: {_tcpClient.RemoteEndPoint}");
                Console.WriteLine($"  本地地址: {_tcpClient.LocalEndPoint}");
                Console.WriteLine($"  连接时长: {stats.ConnectionDuration}");
                Console.WriteLine($"  发送消息数: {stats.MessagesSent}");
                Console.WriteLine($"  接收消息数: {stats.MessagesReceived}");
                Console.WriteLine($"  发送字节数: {stats.BytesSent:N0}");
                Console.WriteLine($"  接收字节数: {stats.BytesReceived:N0}");
                Console.WriteLine($"  重连次数: {stats.ReconnectCount}");
                Console.WriteLine($"  错误次数: {stats.ErrorCount}");

                Console.WriteLine("\n连接质量:");
                Console.WriteLine($"  网络延迟: {quality.Latency.TotalMilliseconds:F1} ms");
                Console.WriteLine($"  连接质量: {quality.Quality:P1}");
                Console.WriteLine($"  丢包率: {quality.PacketLoss:P2}");
                Console.WriteLine($"  吞吐量: {quality.Throughput:F1} KB/s");
            }
            else
            {
                Console.WriteLine("\n主客户端状态: 未连接");
            }

            // 系统资源信息
            Console.WriteLine("\n系统资源:");
            Console.WriteLine($"  内存使用: {GC.GetTotalMemory(false) / 1024 / 1024:F1} MB");
            Console.WriteLine($"  GC回收次数: Gen0={GC.CollectionCount(0)}, Gen1={GC.CollectionCount(1)}, Gen2={GC.CollectionCount(2)}");

            _logger.LogInformation("客户端状态查询完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取客户端状态失败: {ex.Message}");
            _logger.LogError(ex, "获取客户端状态失败");
        }
    }

    /// <summary>
    /// 断开所有连接
    /// </summary>
    public async Task DisconnectAllAsync()
    {
        Console.WriteLine("=== 断开所有连接 ===");

        try
        {
            List<ITcpClient> clientsToDisconnect;
            lock (_lockObject)
            {
                clientsToDisconnect = new List<ITcpClient>(_activeClients);
                _activeClients.Clear();
            }

            if (clientsToDisconnect.Count == 0)
            {
                Console.WriteLine("没有活跃的连接");
                return;
            }

            Console.WriteLine($"正在断开 {clientsToDisconnect.Count} 个连接...");

            var disconnectTasks = clientsToDisconnect.Select(async client =>
            {
                try
                {
                    await client.DisconnectAsync();
                    Console.WriteLine($"客户端 {client.LocalEndPoint} 已断开");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"断开客户端 {client.LocalEndPoint} 失败: {ex.Message}");
                    _logger.LogError(ex, "断开客户端连接失败");
                }
            });

            await Task.WhenAll(disconnectTasks);

            Console.WriteLine("所有连接已断开");
            _logger.LogInformation("所有客户端连接已断开");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"断开连接失败: {ex.Message}");
            _logger.LogError(ex, "断开所有连接失败");
        }
    }

    /// <summary>
    /// 注册事件处理器
    /// </summary>
    private void RegisterEventHandlers(ITcpClient client)
    {
        client.Connected += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 已连接到服务器: {e.RemoteEndPoint}");
            _logger.LogInformation("已连接到服务器: {RemoteEndPoint}", e.RemoteEndPoint);
        };

        client.Disconnected += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 与服务器断开连接: {e.RemoteEndPoint}");
            _logger.LogInformation("与服务器断开连接: {RemoteEndPoint}", e.RemoteEndPoint);
        };

        client.DataReceived += (sender, e) =>
        {
            var message = Encoding.UTF8.GetString(e.Data);
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 收到服务器消息: {message.Trim()}");
            _logger.LogDebug("收到服务器消息: {Message}", message.Trim());
        };

        client.ErrorOccurred += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 客户端错误: {e.Exception.Message}");
            _logger.LogError(e.Exception, "客户端错误");
        };

        client.Reconnecting += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 正在重连... (第 {e.AttemptCount} 次)");
            _logger.LogInformation("正在重连，尝试次数: {AttemptCount}", e.AttemptCount);
        };

        client.Reconnected += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 重连成功: {e.RemoteEndPoint}");
            _logger.LogInformation("重连成功: {RemoteEndPoint}", e.RemoteEndPoint);
        };
    }

    /// <summary>
    /// 创建连接池中的连接
    /// </summary>
    private async Task CreatePooledConnectionAsync(int clientId, string serverHost, int serverPort)
    {
        try
        {
            var client = _tcpClient; // 在实际应用中，这里应该创建新的客户端实例
            var config = new TcpClientConfig
            {
                ServerHost = serverHost,
                ServerPort = serverPort,
                ConnectTimeout = TimeSpan.FromSeconds(10)
            };

            RegisterEventHandlers(client);
            await client.ConnectAsync(config);

            lock (_lockObject)
            {
                _activeClients.Add(client);
            }

            Console.WriteLine($"连接池客户端 #{clientId} 已连接");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"连接池客户端 #{clientId} 连接失败: {ex.Message}");
            _logger.LogError(ex, "连接池客户端连接失败，ID: {ClientId}", clientId);
        }
    }

    /// <summary>
    /// 发送连接池消息
    /// </summary>
    private async Task SendPooledMessageAsync(ITcpClient client, int messageId)
    {
        try
        {
            var message = $"Pooled message #{messageId} from {client.LocalEndPoint}";
            await client.SendAsync(Encoding.UTF8.GetBytes(message));
            _logger.LogDebug("发送连接池消息: {MessageId}", messageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送连接池消息失败，ID: {MessageId}", messageId);
        }
    }

    /// <summary>
    /// 创建并发客户端连接
    /// </summary>
    private async Task CreateConcurrentClientAsync(int clientId, string serverHost, int serverPort, int durationSeconds)
    {
        try
        {
            var client = _tcpClient; // 在实际应用中，这里应该创建新的客户端实例
            var config = new TcpClientConfig
            {
                ServerHost = serverHost,
                ServerPort = serverPort,
                ConnectTimeout = TimeSpan.FromSeconds(5)
            };

            await client.ConnectAsync(config);

            var endTime = DateTime.Now.AddSeconds(durationSeconds);
            var messageCount = 0;

            while (DateTime.Now < endTime && client.IsConnected)
            {
                try
                {
                    var message = $"Concurrent client #{clientId} message #{++messageCount}";
                    await client.SendAsync(Encoding.UTF8.GetBytes(message));
                    await Task.Delay(1000); // 每秒发送一条消息
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "并发客户端发送消息失败，ID: {ClientId}", clientId);
                    break;
                }
            }

            await client.DisconnectAsync();
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "并发客户端连接异常，ID: {ClientId}", clientId);
        }
    }

    /// <summary>
    /// 发送测试消息
    /// </summary>
    private async Task SendTestMessageAsync(int messageId)
    {
        try
        {
            var message = $"Test message #{messageId} at {DateTime.Now:HH:mm:ss.fff}";
            await _tcpClient.SendAsync(Encoding.UTF8.GetBytes(message));
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "发送测试消息失败，ID: {MessageId}", messageId);
        }
    }
}
