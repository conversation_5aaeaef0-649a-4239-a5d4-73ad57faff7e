using Liam.Logging.Interfaces;

namespace Liam.Logging.Example.Services;

/// <summary>
/// 异常处理演示服务实现
/// 演示异常处理和错误日志记录最佳实践
/// </summary>
public class ExceptionHandlingDemoService : IExceptionHandlingDemoService
{
    private readonly ILiamLogger _logger;

    /// <summary>
    /// 初始化异常处理演示服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ExceptionHandlingDemoService(ILiamLogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 演示异常处理
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateExceptionHandlingAsync()
    {
        Console.WriteLine("=== 异常处理演示 ===\n");

        Console.WriteLine("1. 基本异常处理:");
        
        await DemonstrateBasicExceptionHandlingAsync();

        Console.WriteLine("\n2. 嵌套异常处理:");
        
        await DemonstrateNestedExceptionHandlingAsync();

        Console.WriteLine("\n3. 异步异常处理:");
        
        await DemonstrateAsyncExceptionHandlingAsync();

        Console.WriteLine("\n4. 自定义异常处理:");
        
        await DemonstrateCustomExceptionHandlingAsync();

        Console.WriteLine("\n5. 异常聚合处理:");
        
        await DemonstrateAggregateExceptionHandlingAsync();

        Console.WriteLine("\n=== 异常处理演示完成 ===");
    }

    /// <summary>
    /// 演示错误恢复
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateErrorRecoveryAsync()
    {
        Console.WriteLine("=== 错误恢复演示 ===\n");

        Console.WriteLine("1. 重试机制:");
        
        await DemonstrateRetryMechanismAsync();

        Console.WriteLine("\n2. 断路器模式:");
        
        await DemonstrateCircuitBreakerAsync();

        Console.WriteLine("\n3. 降级处理:");
        
        await DemonstrateFallbackHandlingAsync();

        Console.WriteLine("\n4. 资源清理:");
        
        await DemonstrateResourceCleanupAsync();

        Console.WriteLine("\n5. 错误通知:");
        
        await DemonstrateErrorNotificationAsync();

        Console.WriteLine("\n=== 错误恢复演示完成 ===");
    }

    /// <summary>
    /// 演示基本异常处理
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateBasicExceptionHandlingAsync()
    {
        var scenarios = new (string Name, Func<Task> Action)[]
        {
            ("除零异常", () => { ThrowDivideByZeroException(); return Task.CompletedTask; }),
            ("空引用异常", () => { string? nullStr = null; var length = nullStr!.Length; return Task.CompletedTask; }),
            ("索引越界异常", () => { var array = new int[3]; var value = array[5]; return Task.CompletedTask; }),
            ("格式异常", () => { var number = int.Parse("abc"); return Task.CompletedTask; }),
            ("文件未找到异常", () => { File.ReadAllText("nonexistent.txt"); return Task.CompletedTask; })
        };

        foreach (var (scenarioName, action) in scenarios)
        {
            try
            {
                Console.WriteLine($"   测试 {scenarioName}:");
                _logger.LogInformation($"开始测试 {scenarioName}");
                
                await action();
                
                Console.WriteLine($"   ✓ {scenarioName} 未发生异常");
                _logger.LogInformation($"{scenarioName} 执行成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ {scenarioName} 发生异常: {ex.GetType().Name}");
                _logger.LogError($"{scenarioName} 发生异常", ex);
                
                // 记录异常的详细信息
                _logger.LogError($"异常详情 - 类型: {ex.GetType().FullName}, 消息: {ex.Message}");
                
                if (!string.IsNullOrEmpty(ex.StackTrace))
                {
                    _logger.LogDebug($"堆栈跟踪: {ex.StackTrace}");
                }
            }
            
            await Task.Delay(100);
        }

        Console.WriteLine("✓ 基本异常处理演示完成");
    }

    /// <summary>
    /// 演示嵌套异常处理
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateNestedExceptionHandlingAsync()
    {
        try
        {
            Console.WriteLine("   模拟复杂业务操作:");
            _logger.LogInformation("开始复杂业务操作");
            
            await SimulateComplexBusinessOperationAsync();
            
            Console.WriteLine("   ✓ 复杂业务操作成功");
            _logger.LogInformation("复杂业务操作成功完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ✗ 复杂业务操作失败: {ex.Message}");
            _logger.LogError("复杂业务操作失败", ex);
            
            // 记录完整的异常链
            var currentEx = ex;
            var level = 0;
            
            while (currentEx != null)
            {
                var indent = new string(' ', level * 2);
                Console.WriteLine($"   {indent}异常级别 {level}: {currentEx.GetType().Name} - {currentEx.Message}");
                _logger.LogError($"异常链级别 {level}: {currentEx.GetType().Name} - {currentEx.Message}");
                
                currentEx = currentEx.InnerException;
                level++;
            }
        }

        Console.WriteLine("✓ 嵌套异常处理演示完成");
    }

    /// <summary>
    /// 演示异步异常处理
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateAsyncExceptionHandlingAsync()
    {
        Console.WriteLine("   异步操作异常处理:");
        
        var tasks = new List<Task>();
        
        // 创建多个可能失败的异步任务
        for (int i = 0; i < 5; i++)
        {
            var taskId = i;
            var task = HandleAsyncTaskAsync(taskId);
            tasks.Add(task);
        }
        
        try
        {
            await Task.WhenAll(tasks);
            Console.WriteLine("   ✓ 所有异步任务完成");
            _logger.LogInformation("所有异步任务成功完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ✗ 异步任务组失败: {ex.Message}");
            _logger.LogError("异步任务组执行失败", ex);
            
            // 检查各个任务的状态
            for (int i = 0; i < tasks.Count; i++)
            {
                var task = tasks[i];
                var status = task.Status;
                
                Console.WriteLine($"     任务 {i}: {status}");
                _logger.LogDebug($"任务 {i} 状态: {status}");
                
                if (task.IsFaulted && task.Exception != null)
                {
                    foreach (var innerEx in task.Exception.InnerExceptions)
                    {
                        _logger.LogError($"任务 {i} 异常", innerEx);
                    }
                }
            }
        }

        Console.WriteLine("✓ 异步异常处理演示完成");
    }

    /// <summary>
    /// 演示自定义异常处理
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateCustomExceptionHandlingAsync()
    {
        try
        {
            Console.WriteLine("   自定义异常场景:");
            _logger.LogInformation("开始自定义异常演示");
            
            await SimulateBusinessValidationAsync();
            
            Console.WriteLine("   ✓ 业务验证通过");
            _logger.LogInformation("业务验证成功");
        }
        catch (BusinessValidationException ex)
        {
            Console.WriteLine($"   ✗ 业务验证失败: {ex.Message}");
            _logger.LogWarning($"业务验证失败: {ex.Message}", ex);
            
            // 记录业务异常的特定信息
            _logger.LogWarning($"验证错误代码: {ex.ErrorCode}");
            _logger.LogWarning($"验证字段: {ex.FieldName}");
            
            if (ex.ValidationErrors.Any())
            {
                foreach (var error in ex.ValidationErrors)
                {
                    Console.WriteLine($"     验证错误: {error}");
                    _logger.LogWarning($"验证错误详情: {error}");
                }
            }
        }
        catch (SystemResourceException ex)
        {
            Console.WriteLine($"   ✗ 系统资源异常: {ex.Message}");
            _logger.LogError($"系统资源异常: {ex.Message}", ex);
            
            _logger.LogError($"资源类型: {ex.ResourceType}");
            _logger.LogError($"资源名称: {ex.ResourceName}");
            _logger.LogError($"当前使用量: {ex.CurrentUsage}");
            _logger.LogError($"最大限制: {ex.MaxLimit}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ✗ 未知异常: {ex.Message}");
            _logger.LogError("未知异常发生", ex);
        }

        Console.WriteLine("✓ 自定义异常处理演示完成");
    }

    /// <summary>
    /// 演示聚合异常处理
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateAggregateExceptionHandlingAsync()
    {
        Console.WriteLine("   聚合异常处理:");
        
        try
        {
            var tasks = new[]
            {
                SimulateFailingTaskAsync("任务A", 0.3),
                SimulateFailingTaskAsync("任务B", 0.7),
                SimulateFailingTaskAsync("任务C", 0.5),
                SimulateFailingTaskAsync("任务D", 0.2),
                SimulateFailingTaskAsync("任务E", 0.8)
            };
            
            await Task.WhenAll(tasks);
            
            Console.WriteLine("   ✓ 所有任务成功完成");
            _logger.LogInformation("聚合任务全部成功");
        }
        catch (AggregateException aggEx)
        {
            Console.WriteLine($"   ✗ 聚合异常发生，包含 {aggEx.InnerExceptions.Count} 个异常:");
            _logger.LogError($"聚合异常发生，包含 {aggEx.InnerExceptions.Count} 个内部异常", aggEx);
            
            for (int i = 0; i < aggEx.InnerExceptions.Count; i++)
            {
                var innerEx = aggEx.InnerExceptions[i];
                Console.WriteLine($"     异常 {i + 1}: {innerEx.GetType().Name} - {innerEx.Message}");
                _logger.LogError($"聚合异常内部异常 {i + 1}", innerEx);
            }
            
            // 按异常类型分组处理
            var groupedExceptions = aggEx.InnerExceptions.GroupBy(ex => ex.GetType());
            foreach (var group in groupedExceptions)
            {
                var exceptionType = group.Key.Name;
                var count = group.Count();
                Console.WriteLine($"     {exceptionType}: {count} 个");
                _logger.LogWarning($"异常类型统计: {exceptionType} 出现 {count} 次");
            }
        }

        Console.WriteLine("✓ 聚合异常处理演示完成");
    }

    /// <summary>
    /// 演示重试机制
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateRetryMechanismAsync()
    {
        const int maxRetries = 3;
        var retryCount = 0;
        
        Console.WriteLine($"   重试机制演示 (最大重试次数: {maxRetries}):");
        _logger.LogInformation($"开始重试机制演示，最大重试次数: {maxRetries}");
        
        while (retryCount <= maxRetries)
        {
            try
            {
                Console.WriteLine($"     尝试 {retryCount + 1}/{maxRetries + 1}:");
                _logger.LogInformation($"执行尝试 {retryCount + 1}");
                
                await SimulateUnstableOperationAsync(0.6); // 60% 失败率
                
                Console.WriteLine($"     ✓ 操作成功 (尝试 {retryCount + 1} 次)");
                _logger.LogInformation($"操作成功，总尝试次数: {retryCount + 1}");
                break;
            }
            catch (Exception ex)
            {
                retryCount++;
                
                if (retryCount <= maxRetries)
                {
                    var delay = TimeSpan.FromMilliseconds(1000 * retryCount); // 递增延迟
                    Console.WriteLine($"     ✗ 尝试 {retryCount} 失败: {ex.Message}，{delay.TotalSeconds}秒后重试");
                    _logger.LogWarning($"尝试 {retryCount} 失败，{delay.TotalSeconds}秒后重试", ex);
                    
                    await Task.Delay(delay);
                }
                else
                {
                    Console.WriteLine($"     ✗ 所有重试均失败，操作最终失败");
                    _logger.LogError("重试机制失败，操作最终失败", ex);
                }
            }
        }

        Console.WriteLine("✓ 重试机制演示完成");
    }

    /// <summary>
    /// 演示断路器模式
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateCircuitBreakerAsync()
    {
        Console.WriteLine("   断路器模式演示:");
        _logger.LogInformation("开始断路器模式演示");
        
        var circuitBreaker = new SimpleCircuitBreaker(3, TimeSpan.FromSeconds(2));
        
        for (int i = 1; i <= 8; i++)
        {
            try
            {
                Console.WriteLine($"     请求 {i}:");
                _logger.LogDebug($"发送请求 {i}");
                
                await circuitBreaker.ExecuteAsync(async () =>
                {
                    await SimulateUnstableOperationAsync(0.7); // 70% 失败率
                    return "成功";
                });
                
                Console.WriteLine($"     ✓ 请求 {i} 成功");
                _logger.LogInformation($"请求 {i} 成功");
            }
            catch (CircuitBreakerOpenException)
            {
                Console.WriteLine($"     ⚠ 请求 {i} 被断路器阻止");
                _logger.LogWarning($"请求 {i} 被断路器阻止");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"     ✗ 请求 {i} 失败: {ex.Message}");
                _logger.LogError($"请求 {i} 失败", ex);
            }
            
            await Task.Delay(300);
        }

        Console.WriteLine("✓ 断路器模式演示完成");
    }

    /// <summary>
    /// 演示降级处理
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateFallbackHandlingAsync()
    {
        Console.WriteLine("   降级处理演示:");
        _logger.LogInformation("开始降级处理演示");
        
        var scenarios = new[]
        {
            ("主要服务", 0.3),
            ("备用服务", 0.8),
            ("缓存服务", 0.1)
        };

        foreach (var (serviceName, failureRate) in scenarios)
        {
            try
            {
                Console.WriteLine($"     尝试 {serviceName}:");
                _logger.LogInformation($"尝试调用 {serviceName}");
                
                var result = await CallServiceWithFallbackAsync(serviceName, failureRate);
                
                Console.WriteLine($"     ✓ {serviceName} 调用成功: {result}");
                _logger.LogInformation($"{serviceName} 调用成功: {result}");
                break;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"     ✗ {serviceName} 调用失败: {ex.Message}");
                _logger.LogWarning($"{serviceName} 调用失败", ex);
                
                if (serviceName == "缓存服务")
                {
                    Console.WriteLine($"     使用默认值作为最终降级");
                    _logger.LogInformation("使用默认值作为最终降级方案");
                }
            }
        }

        Console.WriteLine("✓ 降级处理演示完成");
    }

    /// <summary>
    /// 演示资源清理
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateResourceCleanupAsync()
    {
        Console.WriteLine("   资源清理演示:");
        _logger.LogInformation("开始资源清理演示");
        
        var resources = new List<IDisposable>();
        
        try
        {
            // 模拟分配多个资源
            for (int i = 1; i <= 3; i++)
            {
                var resource = new MockResource($"资源{i}");
                resources.Add(resource);
                
                Console.WriteLine($"     分配 {resource.Name}");
                _logger.LogDebug($"分配资源: {resource.Name}");
            }
            
            // 模拟使用资源时发生异常
            await Task.Delay(100);
            throw new InvalidOperationException("资源使用过程中发生异常");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"     ✗ 资源使用失败: {ex.Message}");
            _logger.LogError("资源使用失败", ex);
        }
        finally
        {
            // 确保资源被正确清理
            Console.WriteLine($"     清理 {resources.Count} 个资源:");
            _logger.LogInformation($"开始清理 {resources.Count} 个资源");
            
            foreach (var resource in resources)
            {
                try
                {
                    if (resource is MockResource mockResource)
                    {
                        Console.WriteLine($"       清理 {mockResource.Name}");
                        _logger.LogDebug($"清理资源: {mockResource.Name}");
                    }
                    
                    resource.Dispose();
                }
                catch (Exception cleanupEx)
                {
                    Console.WriteLine($"       ✗ 资源清理失败: {cleanupEx.Message}");
                    _logger.LogError("资源清理失败", cleanupEx);
                }
            }
            
            resources.Clear();
        }

        Console.WriteLine("✓ 资源清理演示完成");
    }

    /// <summary>
    /// 演示错误通知
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateErrorNotificationAsync()
    {
        Console.WriteLine("   错误通知演示:");
        _logger.LogInformation("开始错误通知演示");
        
        try
        {
            await SimulateCriticalSystemFailureAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"     ✗ 严重系统错误: {ex.Message}");
            _logger.LogCritical("严重系统错误发生", ex);
            
            // 模拟发送错误通知
            await SendErrorNotificationAsync(ex);
            
            Console.WriteLine($"     ✓ 错误通知已发送");
            _logger.LogInformation("错误通知发送完成");
        }

        Console.WriteLine("✓ 错误通知演示完成");
    }

    // 辅助方法和类定义
    private async Task SimulateComplexBusinessOperationAsync()
    {
        try
        {
            await SimulateDataAccessAsync();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("业务操作失败", ex);
        }
    }

    private async Task SimulateDataAccessAsync()
    {
        await Task.Delay(50);
        throw new TimeoutException("数据库连接超时");
    }

    private async Task HandleAsyncTaskAsync(int taskId)
    {
        await Task.Delay(Random.Shared.Next(100, 300));
        
        if (Random.Shared.NextDouble() < 0.4) // 40% 失败率
        {
            throw new InvalidOperationException($"异步任务 {taskId} 失败");
        }
    }

    private async Task SimulateBusinessValidationAsync()
    {
        await Task.Delay(50);
        
        if (Random.Shared.NextDouble() < 0.6)
        {
            throw new BusinessValidationException("BV001", "Email", "邮箱格式无效", new[] { "邮箱不能为空", "邮箱格式不正确" });
        }
        
        if (Random.Shared.NextDouble() < 0.3)
        {
            throw new SystemResourceException("Memory", "系统内存", 95, 90);
        }
    }

    private async Task<string> SimulateFailingTaskAsync(string taskName, double failureRate)
    {
        await Task.Delay(Random.Shared.Next(50, 200));
        
        if (Random.Shared.NextDouble() < failureRate)
        {
            throw new InvalidOperationException($"{taskName} 执行失败");
        }
        
        return $"{taskName} 成功";
    }

    private async Task SimulateUnstableOperationAsync(double failureRate)
    {
        await Task.Delay(Random.Shared.Next(100, 300));
        
        if (Random.Shared.NextDouble() < failureRate)
        {
            throw new TimeoutException("操作超时");
        }
    }

    private async Task<string> CallServiceWithFallbackAsync(string serviceName, double failureRate)
    {
        await Task.Delay(100);
        
        if (Random.Shared.NextDouble() < failureRate)
        {
            throw new ServiceUnavailableException($"{serviceName} 不可用");
        }
        
        return $"{serviceName} 响应数据";
    }

    private async Task SimulateCriticalSystemFailureAsync()
    {
        await Task.Delay(100);
        throw new SystemException("系统核心组件故障");
    }

    private async Task SendErrorNotificationAsync(Exception ex)
    {
        await Task.Delay(200);
        _logger.LogInformation($"模拟发送错误通知: {ex.GetType().Name} - {ex.Message}");
    }

    /// <summary>
    /// 故意抛出除零异常用于演示
    /// </summary>
    private static void ThrowDivideByZeroException()
    {
        int zero = 0;
        var result = 10 / zero;
    }
}

// 自定义异常类
public class BusinessValidationException : Exception
{
    public string ErrorCode { get; }
    public string FieldName { get; }
    public IEnumerable<string> ValidationErrors { get; }

    public BusinessValidationException(string errorCode, string fieldName, string message, IEnumerable<string> validationErrors)
        : base(message)
    {
        ErrorCode = errorCode;
        FieldName = fieldName;
        ValidationErrors = validationErrors;
    }
}

public class SystemResourceException : Exception
{
    public string ResourceType { get; }
    public string ResourceName { get; }
    public double CurrentUsage { get; }
    public double MaxLimit { get; }

    public SystemResourceException(string resourceType, string resourceName, double currentUsage, double maxLimit)
        : base($"{resourceName} 使用率过高: {currentUsage}% (限制: {maxLimit}%)")
    {
        ResourceType = resourceType;
        ResourceName = resourceName;
        CurrentUsage = currentUsage;
        MaxLimit = maxLimit;
    }
}

public class ServiceUnavailableException : Exception
{
    public ServiceUnavailableException(string message) : base(message) { }
}

public class CircuitBreakerOpenException : Exception
{
    public CircuitBreakerOpenException() : base("断路器处于开启状态") { }
}

// 简单断路器实现
public class SimpleCircuitBreaker
{
    private readonly int _failureThreshold;
    private readonly TimeSpan _timeout;
    private int _failureCount;
    private DateTime _lastFailureTime;
    private bool _isOpen;

    public SimpleCircuitBreaker(int failureThreshold, TimeSpan timeout)
    {
        _failureThreshold = failureThreshold;
        _timeout = timeout;
    }

    public async Task<T> ExecuteAsync<T>(Func<Task<T>> operation)
    {
        if (_isOpen)
        {
            if (DateTime.Now - _lastFailureTime > _timeout)
            {
                _isOpen = false;
                _failureCount = 0;
            }
            else
            {
                throw new CircuitBreakerOpenException();
            }
        }

        try
        {
            var result = await operation();
            _failureCount = 0;
            return result;
        }
        catch
        {
            _failureCount++;
            _lastFailureTime = DateTime.Now;
            
            if (_failureCount >= _failureThreshold)
            {
                _isOpen = true;
            }
            
            throw;
        }
    }
}

// 模拟资源类
public class MockResource : IDisposable
{
    public string Name { get; }
    private bool _disposed;

    public MockResource(string name)
    {
        Name = name;
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
        }
    }
}
