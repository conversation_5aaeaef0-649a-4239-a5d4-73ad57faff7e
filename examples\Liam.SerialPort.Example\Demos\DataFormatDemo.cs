using Microsoft.Extensions.Logging;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;
using Liam.SerialPort.Example.Services;
using System.Text;

namespace Liam.SerialPort.Example.Demos;

/// <summary>
/// 数据格式演示
/// 展示字符串、字节数组、十六进制等多种数据格式的处理
/// </summary>
public class DataFormatDemo
{
    private readonly ILogger<DataFormatDemo> _logger;
    private readonly ISerialPortService _serialPortService;
    private readonly IMenuService _menuService;
    private readonly IPerformanceMonitor _performanceMonitor;

    public DataFormatDemo(
        ILogger<DataFormatDemo> logger,
        ISerialPortService serialPortService,
        IMenuService menuService,
        IPerformanceMonitor performanceMonitor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serialPortService = serialPortService ?? throw new ArgumentNullException(nameof(serialPortService));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
    }

    public async Task<bool> RunAsync()
    {
        _menuService.ClearScreen();
        
        Console.ForegroundColor = ConsoleColor.Cyan;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    数据格式演示                              ║");
        Console.WriteLine("║  演示内容：字符串、字节数组、十六进制、编码转换              ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();

        try
        {
            // 确保连接
            if (!await EnsureConnectionAsync())
            {
                return true;
            }

            // 演示不同数据格式
            await DemonstrateStringFormatsAsync();
            await DemonstrateByteFormatsAsync();
            await DemonstrateEncodingFormatsAsync();
            await DemonstrateHexFormatsAsync();

            _menuService.ShowStatus("数据格式演示完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据格式演示过程中发生错误");
            _menuService.ShowStatus($"演示失败: {ex.Message}", StatusType.Error);
        }
        finally
        {
            if (_serialPortService.IsConnected)
            {
                await _serialPortService.DisconnectAsync();
            }
        }

        return true;
    }

    private async Task<bool> EnsureConnectionAsync()
    {
        if (_serialPortService.IsConnected)
        {
            return true;
        }

        var ports = await _serialPortService.GetAvailablePortsAsync();
        var portList = ports.ToList();

        if (!portList.Any())
        {
            _menuService.ShowStatus("未发现可用串口", StatusType.Warning);
            return false;
        }

        var selectedPort = portList.First();
        var settings = SerialPortSettings.Default;

        var connected = await _serialPortService.ConnectAsync(selectedPort, settings);
        if (connected)
        {
            _menuService.ShowStatus($"已连接到 {selectedPort.PortName}", StatusType.Success);
        }

        return connected;
    }

    private async Task DemonstrateStringFormatsAsync()
    {
        Console.WriteLine("\n--- 字符串格式演示 ---");
        
        var testStrings = new[]
        {
            "Hello World",
            "中文测试",
            "Special chars: !@#$%^&*()",
            "Numbers: 123456789",
            "Mixed: ABC123中文!@#"
        };

        foreach (var testString in testStrings)
        {
            Console.WriteLine($"发送字符串: {testString}");
            await _serialPortService.SendAsync(testString);
            
            var bytes = Encoding.UTF8.GetBytes(testString);
            Console.WriteLine($"  UTF-8字节: {Convert.ToHexString(bytes)}");
            Console.WriteLine($"  字节长度: {bytes.Length}");
            
            await Task.Delay(500);
        }
        
        await _menuService.WaitForKeyAsync();
    }

    private async Task DemonstrateByteFormatsAsync()
    {
        Console.WriteLine("\n--- 字节数组格式演示 ---");
        
        var testByteArrays = new[]
        {
            new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05 },
            new byte[] { 0xFF, 0xFE, 0xFD, 0xFC },
            new byte[] { 0x00, 0x7F, 0x80, 0xFF },
            Encoding.ASCII.GetBytes("ASCII"),
            Encoding.UTF8.GetBytes("UTF8测试")
        };

        foreach (var byteArray in testByteArrays)
        {
            Console.WriteLine($"发送字节数组: {Convert.ToHexString(byteArray)}");
            await _serialPortService.SendAsync(byteArray);
            
            Console.WriteLine($"  长度: {byteArray.Length} 字节");
            Console.WriteLine($"  ASCII解释: {Encoding.ASCII.GetString(byteArray).Replace('\0', '.')}");
            
            await Task.Delay(500);
        }
        
        await _menuService.WaitForKeyAsync();
    }

    private async Task DemonstrateEncodingFormatsAsync()
    {
        Console.WriteLine("\n--- 编码格式演示 ---");
        
        var testText = "编码测试 Encoding Test 123";
        var encodings = new[]
        {
            Encoding.UTF8,
            Encoding.ASCII,
            Encoding.Unicode,
            Encoding.UTF32
        };

        foreach (var encoding in encodings)
        {
            try
            {
                var bytes = encoding.GetBytes(testText);
                Console.WriteLine($"编码: {encoding.EncodingName}");
                Console.WriteLine($"  原文: {testText}");
                Console.WriteLine($"  字节: {Convert.ToHexString(bytes)}");
                Console.WriteLine($"  长度: {bytes.Length} 字节");
                
                await _serialPortService.SendAsync(bytes);
                
                // 尝试解码
                var decoded = encoding.GetString(bytes);
                Console.WriteLine($"  解码: {decoded}");
                Console.WriteLine($"  匹配: {decoded == testText}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  编码失败: {ex.Message}");
            }
            
            Console.WriteLine();
            await Task.Delay(500);
        }
        
        await _menuService.WaitForKeyAsync();
    }

    private async Task DemonstrateHexFormatsAsync()
    {
        Console.WriteLine("\n--- 十六进制格式演示 ---");
        
        var hexStrings = new[]
        {
            "48656C6C6F", // "Hello"
            "DEADBEEF",
            "01020304050607080910",
            "FF00FF00FF00",
            "A1B2C3D4E5F6"
        };

        foreach (var hexString in hexStrings)
        {
            try
            {
                var bytes = Convert.FromHexString(hexString);
                Console.WriteLine($"十六进制字符串: {hexString}");
                Console.WriteLine($"  转换为字节: {string.Join(" ", bytes.Select(b => $"0x{b:X2}"))}");
                Console.WriteLine($"  ASCII解释: {Encoding.ASCII.GetString(bytes).Replace('\0', '.')}");
                
                await _serialPortService.SendAsync(bytes);
                
                // 演示反向转换
                var backToHex = Convert.ToHexString(bytes);
                Console.WriteLine($"  转回十六进制: {backToHex}");
                Console.WriteLine($"  匹配: {backToHex.Equals(hexString, StringComparison.OrdinalIgnoreCase)}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  转换失败: {ex.Message}");
            }
            
            Console.WriteLine();
            await Task.Delay(500);
        }
        
        await _menuService.WaitForKeyAsync();
    }
}
