using Microsoft.Extensions.Logging;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;
using Liam.SerialPort.Example.Services;

namespace Liam.SerialPort.Example.Demos;

/// <summary>
/// 错误处理演示
/// 展示异常处理、错误恢复、故障转移等错误处理机制
/// </summary>
public class ErrorHandlingDemo
{
    private readonly ILogger<ErrorHandlingDemo> _logger;
    private readonly ISerialPortService _serialPortService;
    private readonly IMenuService _menuService;
    private readonly IPerformanceMonitor _performanceMonitor;

    public ErrorHandlingDemo(
        ILogger<ErrorHandlingDemo> logger,
        ISerialPortService serialPortService,
        IMenuService menuService,
        IPerformanceMonitor performanceMonitor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serialPortService = serialPortService ?? throw new ArgumentNullException(nameof(serialPortService));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
    }

    public async Task<bool> RunAsync()
    {
        _menuService.ClearScreen();
        
        Console.ForegroundColor = ConsoleColor.Red;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    错误处理演示                              ║");
        Console.WriteLine("║  演示内容：异常处理、错误恢复、故障转移、重试机制            ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();

        try
        {
            // 注册错误事件处理器
            _serialPortService.ErrorOccurred += OnErrorOccurred;

            // 演示各种错误处理场景
            await DemonstrateConnectionErrorsAsync();
            await DemonstrateDataTransmissionErrorsAsync();
            await DemonstrateTimeoutHandlingAsync();
            await DemonstrateRetryMechanismAsync();

            _menuService.ShowStatus("错误处理演示完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "错误处理演示过程中发生错误");
            _menuService.ShowStatus($"演示失败: {ex.Message}", StatusType.Error);
        }
        finally
        {
            _serialPortService.ErrorOccurred -= OnErrorOccurred;
            
            if (_serialPortService.IsConnected)
            {
                await _serialPortService.DisconnectAsync();
            }
        }

        return true;
    }

    private async Task DemonstrateConnectionErrorsAsync()
    {
        Console.WriteLine("\n--- 连接错误处理演示 ---");
        
        // 尝试连接到不存在的串口
        Console.WriteLine("1. 尝试连接到不存在的串口...");
        try
        {
            var invalidPort = new SerialPortInfo { PortName = "COM999", Description = "不存在的串口" };
            var settings = SerialPortSettings.Default;
            
            var connected = await _serialPortService.ConnectAsync(invalidPort, settings);
            Console.WriteLine($"连接结果: {(connected ? "成功" : "失败")}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"捕获异常: {ex.GetType().Name} - {ex.Message}");
            _logger.LogWarning(ex, "连接到无效串口失败");
        }

        // 尝试使用无效参数连接
        Console.WriteLine("\n2. 尝试使用无效参数连接...");
        try
        {
            var ports = await _serialPortService.GetAvailablePortsAsync();
            var portList = ports.ToList();
            
            if (portList.Any())
            {
                var validPort = portList.First();
                var invalidSettings = new SerialPortSettings
                {
                    BaudRate = -1, // 无效波特率
                    DataBits = 0,  // 无效数据位
                    ReadTimeout = -1000 // 无效超时
                };
                
                var validationResult = invalidSettings.Validate();
                Console.WriteLine($"参数验证结果: {(validationResult.IsValid ? "有效" : "无效")}");
                
                if (!validationResult.IsValid)
                {
                    Console.WriteLine("验证错误:");
                    foreach (var error in validationResult.Errors)
                    {
                        Console.WriteLine($"  • {error}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"捕获异常: {ex.GetType().Name} - {ex.Message}");
        }

        await _menuService.WaitForKeyAsync();
    }

    private async Task DemonstrateDataTransmissionErrorsAsync()
    {
        Console.WriteLine("\n--- 数据传输错误处理演示 ---");
        
        // 在未连接状态下尝试发送数据
        Console.WriteLine("1. 在未连接状态下发送数据...");
        try
        {
            if (!_serialPortService.IsConnected)
            {
                await _serialPortService.SendAsync("测试数据");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"捕获异常: {ex.GetType().Name} - {ex.Message}");
            _logger.LogWarning(ex, "在未连接状态下发送数据失败");
        }

        // 发送空数据
        Console.WriteLine("\n2. 发送空数据...");
        try
        {
            // 先建立连接
            var ports = await _serialPortService.GetAvailablePortsAsync();
            var portList = ports.ToList();
            
            if (portList.Any())
            {
                var selectedPort = portList.First();
                var settings = SerialPortSettings.Default;
                
                if (await _serialPortService.ConnectAsync(selectedPort, settings))
                {
                    await _serialPortService.SendAsync((byte[])null!);
                }
            }
        }
        catch (ArgumentNullException ex)
        {
            Console.WriteLine($"捕获空参数异常: {ex.Message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"捕获其他异常: {ex.GetType().Name} - {ex.Message}");
        }

        await _menuService.WaitForKeyAsync();
    }

    private async Task DemonstrateTimeoutHandlingAsync()
    {
        Console.WriteLine("\n--- 超时处理演示 ---");
        
        if (!_serialPortService.IsConnected)
        {
            var ports = await _serialPortService.GetAvailablePortsAsync();
            var portList = ports.ToList();
            
            if (portList.Any())
            {
                var selectedPort = portList.First();
                var settings = SerialPortSettings.Default;
                settings.ReadTimeout = 1000; // 1秒超时
                
                await _serialPortService.ConnectAsync(selectedPort, settings);
            }
        }

        if (_serialPortService.IsConnected)
        {
            Console.WriteLine("1. 发送数据并等待响应（短超时）...");
            try
            {
                var response = await _serialPortService.SendAndReceiveAsync("AT", TimeSpan.FromMilliseconds(100));
                Console.WriteLine($"收到响应: {response}");
            }
            catch (TimeoutException ex)
            {
                Console.WriteLine($"超时异常: {ex.Message}");
                _logger.LogWarning("发送并接收数据超时");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"其他异常: {ex.GetType().Name} - {ex.Message}");
            }

            Console.WriteLine("\n2. 发送数据并等待响应（正常超时）...");
            try
            {
                var response = await _serialPortService.SendAndReceiveAsync("AT", TimeSpan.FromSeconds(5));
                Console.WriteLine($"收到响应: {response}");
            }
            catch (TimeoutException ex)
            {
                Console.WriteLine($"超时异常: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"其他异常: {ex.GetType().Name} - {ex.Message}");
            }
        }

        await _menuService.WaitForKeyAsync();
    }

    private async Task DemonstrateRetryMechanismAsync()
    {
        Console.WriteLine("\n--- 重试机制演示 ---");
        
        var maxRetries = 3;
        var retryDelay = TimeSpan.FromSeconds(1);
        
        Console.WriteLine($"演示重试机制: 最大重试次数={maxRetries}, 重试间隔={retryDelay.TotalSeconds}秒");
        
        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            Console.WriteLine($"\n尝试 {attempt}/{maxRetries}:");
            
            try
            {
                // 模拟可能失败的操作
                if (attempt < maxRetries)
                {
                    throw new InvalidOperationException($"模拟第{attempt}次操作失败");
                }
                
                Console.WriteLine("操作成功！");
                break;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"操作失败: {ex.Message}");
                
                if (attempt < maxRetries)
                {
                    Console.WriteLine($"等待 {retryDelay.TotalSeconds} 秒后重试...");
                    await Task.Delay(retryDelay);
                }
                else
                {
                    Console.WriteLine("已达到最大重试次数，操作最终失败");
                    _logger.LogError(ex, "重试机制演示：操作最终失败");
                }
            }
        }

        // 演示指数退避重试
        Console.WriteLine("\n--- 指数退避重试演示 ---");
        var baseDelay = TimeSpan.FromMilliseconds(500);
        
        for (int attempt = 1; attempt <= 4; attempt++)
        {
            var delay = TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * Math.Pow(2, attempt - 1));
            Console.WriteLine($"尝试 {attempt}: 延迟 {delay.TotalMilliseconds}ms");
            
            try
            {
                if (attempt < 4)
                {
                    throw new Exception($"模拟失败 {attempt}");
                }
                Console.WriteLine("指数退避重试成功！");
                break;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"失败: {ex.Message}");
                if (attempt < 4)
                {
                    await Task.Delay(delay);
                }
            }
        }

        await _menuService.WaitForKeyAsync();
    }

    private void OnErrorOccurred(object? sender, Liam.SerialPort.Events.SerialPortErrorEventArgs e)
    {
        Console.WriteLine($"\n[错误事件] 类型: {e.ErrorType}, 消息: {e.Message}");
        if (e.Exception != null)
        {
            Console.WriteLine($"[错误事件] 异常: {e.Exception.GetType().Name} - {e.Exception.Message}");
        }
        
        _logger.LogError(e.Exception, "串口错误事件: {ErrorType} - {Message}", e.ErrorType, e.Message);
    }
}
