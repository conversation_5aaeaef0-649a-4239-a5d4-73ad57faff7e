using Microsoft.Extensions.Logging;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;
using Liam.SerialPort.Example.Services;

namespace Liam.SerialPort.Example.Demos;

/// <summary>
/// 连接管理演示
/// 展示ISerialPortConnection接口的直接使用和连接统计功能
/// </summary>
public class ConnectionManagementDemo
{
    private readonly ILogger<ConnectionManagementDemo> _logger;
    private readonly ISerialPortService _serialPortService;
    private readonly ISerialPortConnection _connection;
    private readonly IMenuService _menuService;
    private readonly IPerformanceMonitor _performanceMonitor;

    /// <summary>
    /// 构造函数
    /// </summary>
    public ConnectionManagementDemo(
        ILogger<ConnectionManagementDemo> logger,
        ISerialPortService serialPortService,
        ISerialPortConnection connection,
        IMenuService menuService,
        IPerformanceMonitor performanceMonitor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serialPortService = serialPortService ?? throw new ArgumentNullException(nameof(serialPortService));
        _connection = connection ?? throw new ArgumentNullException(nameof(connection));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
    }

    /// <summary>
    /// 运行连接管理演示
    /// </summary>
    public async Task<bool> RunAsync()
    {
        _menuService.ClearScreen();
        
        Console.ForegroundColor = ConsoleColor.DarkGreen;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    连接管理演示                              ║");
        Console.WriteLine("║  演示内容：手动重连、连接测试、统计信息、状态扩展方法        ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();

        try
        {
            // 1. 演示连接状态扩展方法
            await DemonstrateConnectionStatusExtensionsAsync();

            // 2. 演示连接测试功能
            await DemonstrateConnectionTestAsync();

            // 3. 演示手动重连功能
            await DemonstrateManualReconnectAsync();

            // 4. 演示连接统计信息
            await DemonstrateConnectionStatisticsAsync();

            // 5. 演示连接生命周期管理
            await DemonstrateConnectionLifecycleAsync();

            _menuService.ShowStatus("连接管理演示完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接管理演示过程中发生错误");
            _menuService.ShowStatus($"演示失败: {ex.Message}", StatusType.Error);
        }
        finally
        {
            if (_serialPortService.IsConnected)
            {
                await _serialPortService.DisconnectAsync();
            }
        }

        return true;
    }

    /// <summary>
    /// 演示连接状态扩展方法
    /// </summary>
    private async Task DemonstrateConnectionStatusExtensionsAsync()
    {
        Console.WriteLine("\n--- 连接状态扩展方法演示 ---");
        
        // 演示所有连接状态及其扩展方法
        var allStatuses = Enum.GetValues<ConnectionStatus>();
        
        Console.WriteLine("连接状态及其属性:");
        Console.WriteLine("状态".PadRight(20) + "中文描述".PadRight(15) + "已连接".PadRight(8) + "已断开".PadRight(8) + "过渡中".PadRight(8) + "错误".PadRight(8) + "可连接".PadRight(8) + "可断开".PadRight(8) + "可发送");
        Console.WriteLine(new string('-', 100));
        
        foreach (var status in allStatuses)
        {
            Console.WriteLine(
                status.ToString().PadRight(20) +
                status.GetDescription().PadRight(15) +
                status.IsConnected().ToString().PadRight(8) +
                status.IsDisconnected().ToString().PadRight(8) +
                status.IsTransitioning().ToString().PadRight(8) +
                status.IsError().ToString().PadRight(8) +
                status.CanConnect().ToString().PadRight(8) +
                status.CanDisconnect().ToString().PadRight(8) +
                status.CanSendData()
            );
        }
        
        Console.WriteLine("\n当前连接状态分析:");
        var currentStatus = _serialPortService.Status;
        Console.WriteLine($"当前状态: {currentStatus} ({currentStatus.GetDescription()})");
        Console.WriteLine($"  可以连接: {currentStatus.CanConnect()}");
        Console.WriteLine($"  可以断开: {currentStatus.CanDisconnect()}");
        Console.WriteLine($"  可以发送数据: {currentStatus.CanSendData()}");
        Console.WriteLine($"  是否为错误状态: {currentStatus.IsError()}");
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示连接测试功能
    /// </summary>
    private async Task DemonstrateConnectionTestAsync()
    {
        Console.WriteLine("\n--- 连接测试功能演示 ---");
        
        // 获取可用端口进行测试
        var ports = await _serialPortService.GetAvailablePortsAsync();
        var portList = ports.ToList();
        
        if (!portList.Any())
        {
            Console.WriteLine("未发现可用端口，跳过连接测试");
            return;
        }
        
        var testPort = portList.First();
        var settings = SerialPortSettings.Default;
        
        Console.WriteLine($"测试端口: {testPort.PortName}");
        Console.WriteLine($"测试设置: {settings}");
        
        try
        {
            Console.WriteLine("\n1. 执行连接测试...");
            var operationId = _performanceMonitor.StartOperation("连接测试");
            
            var testResult = await _connection.TestConnectionAsync();
            _performanceMonitor.EndOperation(operationId, testResult);
            
            Console.WriteLine($"连接测试结果: {(testResult ? "成功" : "失败")}");
            
            if (testResult)
            {
                _menuService.ShowStatus("连接测试通过", StatusType.Success);
                
                // 如果测试成功，建立实际连接
                Console.WriteLine("\n2. 建立实际连接...");
                var connected = await _serialPortService.ConnectAsync(testPort, settings);
                
                if (connected)
                {
                    Console.WriteLine("实际连接建立成功");
                    
                    // 测试数据传输
                    Console.WriteLine("\n3. 测试数据传输...");
                    try
                    {
                        await _serialPortService.SendAsync("CONNECTION_TEST");
                        Console.WriteLine("数据发送测试成功");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"数据发送测试失败: {ex.Message}");
                    }
                }
                else
                {
                    Console.WriteLine("实际连接建立失败");
                }
            }
            else
            {
                _menuService.ShowStatus("连接测试失败", StatusType.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接测试失败");
            Console.WriteLine($"连接测试异常: {ex.Message}");
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示手动重连功能
    /// </summary>
    private async Task DemonstrateManualReconnectAsync()
    {
        Console.WriteLine("\n--- 手动重连功能演示 ---");
        
        if (!_serialPortService.IsConnected)
        {
            Console.WriteLine("当前未连接，先建立连接...");
            
            var ports = await _serialPortService.GetAvailablePortsAsync();
            var portList = ports.ToList();
            
            if (!portList.Any())
            {
                Console.WriteLine("未发现可用端口，跳过重连演示");
                return;
            }
            
            var port = portList.First();
            var settings = SerialPortSettings.Default;
            await _serialPortService.ConnectAsync(port, settings);
        }
        
        if (_serialPortService.IsConnected)
        {
            Console.WriteLine($"当前连接状态: {_serialPortService.Status}");
            Console.WriteLine($"连接端口: {_serialPortService.CurrentPort?.PortName}");
            
            try
            {
                Console.WriteLine("\n1. 执行手动重连...");
                var operationId = _performanceMonitor.StartOperation("手动重连");
                
                var reconnected = await _connection.ReconnectAsync();
                _performanceMonitor.EndOperation(operationId, reconnected);
                
                Console.WriteLine($"重连结果: {(reconnected ? "成功" : "失败")}");
                
                if (reconnected)
                {
                    _menuService.ShowStatus("手动重连成功", StatusType.Success);
                    Console.WriteLine($"重连后状态: {_serialPortService.Status}");
                    
                    // 测试重连后的功能
                    Console.WriteLine("\n2. 测试重连后的数据传输...");
                    try
                    {
                        await _serialPortService.SendAsync("RECONNECT_TEST");
                        Console.WriteLine("重连后数据传输正常");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"重连后数据传输失败: {ex.Message}");
                    }
                }
                else
                {
                    _menuService.ShowStatus("手动重连失败", StatusType.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手动重连失败");
                Console.WriteLine($"重连异常: {ex.Message}");
            }
        }
        else
        {
            Console.WriteLine("无法建立初始连接，跳过重连演示");
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示连接统计信息
    /// </summary>
    private async Task DemonstrateConnectionStatisticsAsync()
    {
        Console.WriteLine("\n--- 连接统计信息演示 ---");
        
        if (!_serialPortService.IsConnected)
        {
            Console.WriteLine("当前未连接，先建立连接以获取统计信息...");
            
            var ports = await _serialPortService.GetAvailablePortsAsync();
            var portList = ports.ToList();
            
            if (portList.Any())
            {
                var port = portList.First();
                var settings = SerialPortSettings.Default;
                await _serialPortService.ConnectAsync(port, settings);
            }
        }
        
        if (_serialPortService.IsConnected)
        {
            try
            {
                Console.WriteLine("1. 获取连接统计信息...");
                var statistics = _connection.GetStatistics();
                
                Console.WriteLine("\n=== 连接统计信息 ===");
                Console.WriteLine($"连接建立时间: {statistics.ConnectedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未知"}");
                Console.WriteLine($"最后活动时间: {statistics.LastActivity?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未知"}");
                Console.WriteLine($"连接持续时间: {statistics.Duration?.ToString(@"hh\:mm\:ss") ?? "未知"}");
                Console.WriteLine($"发送字节数: {statistics.BytesSent:N0}");
                Console.WriteLine($"接收字节数: {statistics.BytesReceived:N0}");
                Console.WriteLine($"发送消息数: {statistics.MessagesSent:N0}");
                Console.WriteLine($"接收消息数: {statistics.MessagesReceived:N0}");
                Console.WriteLine($"连接尝试次数: {statistics.ConnectionAttempts}");
                Console.WriteLine($"重连次数: {statistics.ReconnectionCount}");
                Console.WriteLine($"错误次数: {statistics.ErrorCount}");
                Console.WriteLine($"最后错误时间: {statistics.LastErrorAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "无"}");
                Console.WriteLine($"最后错误信息: {statistics.LastError ?? "无"}");
                
                Console.WriteLine("\n2. 生成一些活动以更新统计信息...");
                
                // 发送一些数据来更新统计
                for (int i = 1; i <= 5; i++)
                {
                    var message = $"Statistics Test {i}";
                    await _serialPortService.SendAsync(message);
                    Console.WriteLine($"发送: {message}");
                    await Task.Delay(500);
                }
                
                Console.WriteLine("\n3. 获取更新后的统计信息...");
                var updatedStatistics = _connection.GetStatistics();
                
                Console.WriteLine("\n=== 更新后的统计信息 ===");
                Console.WriteLine($"发送字节数: {updatedStatistics.BytesSent:N0} (增加: {updatedStatistics.BytesSent - statistics.BytesSent})");
                Console.WriteLine($"发送消息数: {updatedStatistics.MessagesSent:N0} (增加: {updatedStatistics.MessagesSent - statistics.MessagesSent})");
                Console.WriteLine($"最后活动时间: {updatedStatistics.LastActivity?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未知"}");
                
                // 演示统计信息的手动操作
                Console.WriteLine("\n4. 演示统计信息操作...");
                
                // 手动记录一些统计
                updatedStatistics.RecordSent(100);
                updatedStatistics.RecordReceived(50);
                updatedStatistics.RecordError("演示错误");
                
                Console.WriteLine("手动记录统计后:");
                Console.WriteLine($"发送字节数: {updatedStatistics.BytesSent:N0}");
                Console.WriteLine($"接收字节数: {updatedStatistics.BytesReceived:N0}");
                Console.WriteLine($"错误次数: {updatedStatistics.ErrorCount}");
                Console.WriteLine($"最后错误: {updatedStatistics.LastError}");
                
                // 重置统计
                Console.WriteLine("\n5. 重置统计信息...");
                updatedStatistics.Reset();
                
                Console.WriteLine("重置后的统计信息:");
                Console.WriteLine($"发送字节数: {updatedStatistics.BytesSent}");
                Console.WriteLine($"接收字节数: {updatedStatistics.BytesReceived}");
                Console.WriteLine($"连接时间: {updatedStatistics.ConnectedAt?.ToString() ?? "null"}");
                Console.WriteLine($"错误次数: {updatedStatistics.ErrorCount}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取连接统计信息失败");
                Console.WriteLine($"统计信息获取失败: {ex.Message}");
            }
        }
        else
        {
            Console.WriteLine("无连接，无法获取统计信息");
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示连接生命周期管理
    /// </summary>
    private async Task DemonstrateConnectionLifecycleAsync()
    {
        Console.WriteLine("\n--- 连接生命周期管理演示 ---");
        
        // 注册状态变化事件
        var statusChanges = new List<(ConnectionStatus Old, ConnectionStatus New, DateTime Time)>();
        
        _serialPortService.StatusChanged += (sender, e) =>
        {
            statusChanges.Add((e.OldStatus, e.NewStatus, DateTime.Now));
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 状态变化: {e.OldStatus} -> {e.NewStatus}");
        };

        try
        {
            Console.WriteLine("监控连接生命周期中的状态变化...");
            
            var ports = await _serialPortService.GetAvailablePortsAsync();
            var portList = ports.ToList();
            
            if (portList.Any())
            {
                var port = portList.First();
                var settings = SerialPortSettings.Default;
                
                Console.WriteLine($"\n1. 连接到 {port.PortName}...");
                await _serialPortService.ConnectAsync(port, settings);
                await Task.Delay(1000);
                
                Console.WriteLine("\n2. 发送数据...");
                await _serialPortService.SendAsync("Lifecycle Test");
                await Task.Delay(1000);
                
                Console.WriteLine("\n3. 断开连接...");
                await _serialPortService.DisconnectAsync();
                await Task.Delay(1000);
                
                Console.WriteLine("\n4. 重新连接...");
                await _serialPortService.ConnectAsync(port, settings);
                await Task.Delay(1000);
                
                Console.WriteLine("\n5. 最终断开...");
                await _serialPortService.DisconnectAsync();
                await Task.Delay(1000);
            }
            
            Console.WriteLine("\n=== 生命周期状态变化汇总 ===");
            for (int i = 0; i < statusChanges.Count; i++)
            {
                var change = statusChanges[i];
                Console.WriteLine($"{i + 1}. [{change.Time:HH:mm:ss.fff}] {change.Old} -> {change.New}");
            }
            
            Console.WriteLine($"\n总状态变化次数: {statusChanges.Count}");
            
            // 分析状态变化模式
            var connectingCount = statusChanges.Count(c => c.New == ConnectionStatus.Connecting);
            var connectedCount = statusChanges.Count(c => c.New == ConnectionStatus.Connected);
            var disconnectingCount = statusChanges.Count(c => c.New == ConnectionStatus.Disconnecting);
            var disconnectedCount = statusChanges.Count(c => c.New == ConnectionStatus.Disconnected);
            
            Console.WriteLine("\n状态变化统计:");
            Console.WriteLine($"  进入连接中状态: {connectingCount} 次");
            Console.WriteLine($"  进入已连接状态: {connectedCount} 次");
            Console.WriteLine($"  进入断开中状态: {disconnectingCount} 次");
            Console.WriteLine($"  进入已断开状态: {disconnectedCount} 次");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接生命周期演示失败");
            Console.WriteLine($"生命周期演示失败: {ex.Message}");
        }
        
        await _menuService.WaitForKeyAsync();
    }
}
