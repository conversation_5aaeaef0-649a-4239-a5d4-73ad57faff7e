namespace Liam.SerialPort.Example.Services;

/// <summary>
/// 菜单服务接口
/// 负责显示用户界面和处理用户输入
/// </summary>
public interface IMenuService
{
    /// <summary>
    /// 显示主菜单并获取用户选择
    /// </summary>
    /// <returns>用户选择的菜单项</returns>
    Task<MenuChoice> ShowMainMenuAsync();

    /// <summary>
    /// 显示串口选择菜单
    /// </summary>
    /// <param name="ports">可用的串口列表</param>
    /// <returns>用户选择的串口索引，-1表示取消</returns>
    Task<int> ShowPortSelectionMenuAsync(IEnumerable<string> ports);

    /// <summary>
    /// 显示确认对话框
    /// </summary>
    /// <param name="message">确认消息</param>
    /// <returns>用户是否确认</returns>
    Task<bool> ShowConfirmationAsync(string message);

    /// <summary>
    /// 显示输入对话框
    /// </summary>
    /// <param name="prompt">输入提示</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>用户输入的值</returns>
    Task<string> ShowInputDialogAsync(string prompt, string? defaultValue = null);

    /// <summary>
    /// 显示数字输入对话框
    /// </summary>
    /// <param name="prompt">输入提示</param>
    /// <param name="defaultValue">默认值</param>
    /// <param name="minValue">最小值</param>
    /// <param name="maxValue">最大值</param>
    /// <returns>用户输入的数字</returns>
    Task<int> ShowNumberInputDialogAsync(string prompt, int defaultValue, int minValue = int.MinValue, int maxValue = int.MaxValue);

    /// <summary>
    /// 显示进度条
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="progress">进度百分比 (0-100)</param>
    void ShowProgress(string title, int progress);

    /// <summary>
    /// 显示状态信息
    /// </summary>
    /// <param name="message">状态消息</param>
    /// <param name="type">消息类型</param>
    void ShowStatus(string message, StatusType type = StatusType.Info);

    /// <summary>
    /// 清屏
    /// </summary>
    void ClearScreen();

    /// <summary>
    /// 等待用户按键
    /// </summary>
    /// <param name="message">等待消息</param>
    /// <returns>用户按下的键</returns>
    Task<ConsoleKeyInfo> WaitForKeyAsync(string message = "按任意键继续...");
}

/// <summary>
/// 状态消息类型
/// </summary>
public enum StatusType
{
    /// <summary>
    /// 信息
    /// </summary>
    Info,

    /// <summary>
    /// 成功
    /// </summary>
    Success,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 错误
    /// </summary>
    Error
}
