using <PERSON>.TcpClient.Events;
using <PERSON>.TcpClient.Models;

namespace Liam.TcpClient.Interfaces;

/// <summary>
/// TCP客户端接口
/// </summary>
public interface ITcpClient : IDisposable, IAsyncDisposable
{
    /// <summary>
    /// 客户端配置
    /// </summary>
    TcpClientConfig Configuration { get; }

    /// <summary>
    /// 连接信息
    /// </summary>
    ConnectionInfo? ConnectionInfo { get; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// 是否正在连接
    /// </summary>
    bool IsConnecting { get; }

    /// <summary>
    /// 是否正在重连
    /// </summary>
    bool IsReconnecting { get; }

    /// <summary>
    /// 客户端状态
    /// </summary>
    string Status { get; }

    /// <summary>
    /// 连接建立时间
    /// </summary>
    DateTime? ConnectedAt { get; }

    /// <summary>
    /// 连接持续时间
    /// </summary>
    TimeSpan? ConnectionDuration { get; }

    /// <summary>
    /// 连接状态变更事件
    /// </summary>
    event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// 连接建立事件
    /// </summary>
    event EventHandler<ConnectedEventArgs>? Connected;

    /// <summary>
    /// 连接断开事件
    /// </summary>
    event EventHandler<DisconnectedEventArgs>? Disconnected;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    event EventHandler<DataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 数据发送事件
    /// </summary>
    event EventHandler<DataSentEventArgs>? DataSent;

    /// <summary>
    /// 消息接收事件
    /// </summary>
    event EventHandler<MessageReceivedEventArgs>? MessageReceived;

    /// <summary>
    /// 消息发送事件
    /// </summary>
    event EventHandler<MessageSentEventArgs>? MessageSent;

    /// <summary>
    /// 心跳事件
    /// </summary>
    event EventHandler<HeartbeatEventArgs>? Heartbeat;

    /// <summary>
    /// 错误事件
    /// </summary>
    event EventHandler<TcpClientErrorEventArgs>? Error;

    /// <summary>
    /// 连接到服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接任务</returns>
    Task<bool> ConnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 连接到指定服务器
    /// </summary>
    /// <param name="host">服务器主机</param>
    /// <param name="port">服务器端口</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接任务</returns>
    Task<bool> ConnectAsync(string host, int port, CancellationToken cancellationToken = default);

    /// <summary>
    /// 断开连接
    /// </summary>
    /// <param name="reason">断开原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>断开任务</returns>
    Task DisconnectAsync(string? reason = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 重新连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重连任务</returns>
    Task<bool> ReconnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<bool> SendAsync(byte[] data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送文本（使用协议格式）
    /// </summary>
    /// <param name="text">文本</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<bool> SendTextAsync(string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送原始文本（不带协议头）
    /// </summary>
    /// <param name="text">文本</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<bool> SendRawTextAsync(string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<bool> SendMessageAsync(TcpMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送心跳
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<bool> SendHeartbeatAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 等待接收数据
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收的数据</returns>
    Task<byte[]?> ReceiveAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 等待接收文本
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收的文本</returns>
    Task<string?> ReceiveTextAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 等待接收消息
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收的消息</returns>
    Task<TcpMessage?> ReceiveMessageAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    ClientStatistics GetStatistics();

    /// <summary>
    /// 重置统计信息
    /// </summary>
    void ResetStatistics();

    /// <summary>
    /// 测试连接延迟
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>延迟时间（毫秒）</returns>
    Task<double?> PingAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 启用自动重连
    /// </summary>
    void EnableAutoReconnect();

    /// <summary>
    /// 禁用自动重连
    /// </summary>
    void DisableAutoReconnect();

    /// <summary>
    /// 启用心跳检测
    /// </summary>
    void EnableHeartbeat();

    /// <summary>
    /// 禁用心跳检测
    /// </summary>
    void DisableHeartbeat();

    /// <summary>
    /// 获取连接质量评分
    /// </summary>
    /// <returns>质量评分（0-100）</returns>
    double GetConnectionQuality();

    /// <summary>
    /// 检查连接健康状态
    /// </summary>
    /// <returns>是否健康</returns>
    Task<bool> CheckHealthAsync();

    /// <summary>
    /// 刷新连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>刷新任务</returns>
    Task RefreshConnectionAsync(CancellationToken cancellationToken = default);
}
