﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Liam.TcpClient.Extensions;
using Liam.TcpClient.Interfaces;

namespace Liam.TcpClient.NuGetExample;

/// <summary>
/// Liam.TcpClient NuGet包使用示例
/// 演示如何使用已发布的NuGet包
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Liam.TcpClient NuGet包使用示例 ===");
        Console.WriteLine("演示如何使用已发布的Liam.TcpClient NuGet包");
        Console.WriteLine();

        // 配置依赖注入
        var services = new ServiceCollection();

        // 添加日志服务
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        // 添加Liam.TcpClient服务
        services.AddTcpClient(config =>
        {
            config.ConnectionTimeoutSeconds = 30;
            config.ReceiveBufferSize = 4096;
            config.SendBufferSize = 4096;
            config.EnableHeartbeat = true;
            config.HeartbeatIntervalSeconds = 30;
            config.EnableAutoReconnect = true;
            config.ReconnectIntervalSeconds = 5;
            config.MaxReconnectAttempts = 3;
        });

        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var tcpClient = serviceProvider.GetRequiredService<ITcpClient>();

        try
        {
            logger.LogInformation("开始TCP客户端连接演示");

            // 注册事件处理器
            tcpClient.Connected += (sender, e) =>
            {
                Console.WriteLine($"✅ 已连接到服务器: {e.ConnectionInfo.RemoteEndPoint}");
            };

            tcpClient.Disconnected += (sender, e) =>
            {
                Console.WriteLine($"❌ 与服务器断开连接: {e.ConnectionInfo.RemoteEndPoint}");
            };

            tcpClient.DataReceived += (sender, e) =>
            {
                var message = System.Text.Encoding.UTF8.GetString(e.Data);
                Console.WriteLine($"📨 收到服务器消息: {message}");
            };

            tcpClient.Error += (sender, e) =>
            {
                Console.WriteLine($"⚠️ 客户端错误: {e.Exception.Message}");
            };

            // 尝试连接到本地服务器
            Console.WriteLine("正在尝试连接到 localhost:8080...");
            Console.WriteLine("请确保有TCP服务器在端口8080上运行");

            var connected = await tcpClient.ConnectAsync("localhost", 8080);

            if (connected)
            {
                Console.WriteLine("🎉 连接成功！");

                // 发送测试消息
                await tcpClient.SendTextAsync("Hello from Liam.TcpClient NuGet package!");

                // 等待一段时间接收响应
                await Task.Delay(2000);

                // 显示连接统计
                var stats = tcpClient.GetStatistics();
                Console.WriteLine("\n📊 连接统计:");
                Console.WriteLine($"  - 连接时长: {tcpClient.ConnectionDuration}");
                Console.WriteLine($"  - 发送消息数: {stats.TotalMessagesSent}");
                Console.WriteLine($"  - 接收消息数: {stats.TotalMessagesReceived}");
                Console.WriteLine($"  - 发送字节数: {stats.TotalBytesSent}");
                Console.WriteLine($"  - 接收字节数: {stats.TotalBytesReceived}");

                // 断开连接
                await tcpClient.DisconnectAsync();
                Console.WriteLine("🔌 已断开连接");
            }
            else
            {
                Console.WriteLine("❌ 连接失败");
                Console.WriteLine("请确保有TCP服务器在localhost:8080上运行");
                Console.WriteLine("您可以运行TcpDemo中的服务器示例来测试连接");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "示例程序执行失败");
            Console.WriteLine($"❌ 错误: {ex.Message}");
        }
        finally
        {
            serviceProvider.Dispose();
        }

        Console.WriteLine("\n示例程序执行完成，按任意键退出...");
        Console.ReadKey();
    }
}
