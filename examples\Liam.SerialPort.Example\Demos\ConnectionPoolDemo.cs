using Microsoft.Extensions.Logging;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Extensions;
using Liam.SerialPort.Example.Services;

namespace Liam.SerialPort.Example.Demos;

/// <summary>
/// 连接池演示
/// 展示多连接管理和资源池化功能
/// </summary>
public class ConnectionPoolDemo
{
    private readonly ILogger<ConnectionPoolDemo> _logger;
    private readonly IMenuService _menuService;
    private readonly IPerformanceMonitor _performanceMonitor;

    public ConnectionPoolDemo(
        ILogger<ConnectionPoolDemo> logger,
        IMenuService menuService,
        IPerformanceMonitor performanceMonitor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
    }

    public async Task<bool> RunAsync()
    {
        _menuService.ClearScreen();
        
        Console.ForegroundColor = ConsoleColor.Blue;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    连接池演示                                ║");
        Console.WriteLine("║  演示内容：多连接管理、资源池化、连接复用                    ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();

        _menuService.ShowStatus("连接池功能演示 - 此功能需要多个串口设备", StatusType.Info);
        Console.WriteLine("连接池可以有效管理多个串口连接，提高资源利用率");
        Console.WriteLine("在实际应用中，连接池特别适用于需要同时管理多个串口设备的场景");
        
        await _menuService.WaitForKeyAsync();
        return true;
    }
}
