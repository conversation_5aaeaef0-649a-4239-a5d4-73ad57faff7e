# Liam TCP 库示例程序集合

## 概述

这是Liam.TcpServer和Liam.TcpClient库的完整示例程序集合，展示了TCP网络编程的各种功能和最佳实践。

## 项目结构

```
TcpDemo/
├── Liam.TcpServer.Example/     # TCP服务器示例程序
│   ├── Services/               # 演示服务类
│   ├── appsettings.json       # 服务器配置
│   ├── Program.cs             # 主程序入口
│   └── README.md              # 服务器示例文档
├── Liam.TcpClient.Example/     # TCP客户端示例程序
│   ├── Services/               # 演示服务类
│   ├── appsettings.json       # 客户端配置
│   ├── Program.cs             # 主程序入口
│   └── README.md              # 客户端示例文档
└── README.md                   # 本文档
```

## 功能特性

### 🖥️ 服务器端功能 (Liam.TcpServer.Example)
- ✅ 基本TCP服务器启动和管理
- ✅ SSL/TLS安全连接支持
- ✅ 客户端连接管理和监控
- ✅ 心跳检测和连接保活
- ✅ 性能监控和统计信息
- ✅ 压力测试和负载测试
- ✅ 事件驱动的消息处理
- ✅ 完善的日志记录和错误处理

### 📱 客户端功能 (Liam.TcpClient.Example)
- ✅ 基本TCP客户端连接和通信
- ✅ SSL/TLS安全连接支持
- ✅ 连接池管理和复用
- ✅ 心跳检测和连接质量监控
- ✅ 自动重连和故障恢复
- ✅ 性能监控和网络质量分析
- ✅ 并发连接和压力测试
- ✅ 高效的消息收发机制

## 快速开始

### 1. 环境要求
- .NET 8.0 SDK
- Visual Studio 2022 或 VS Code
- Windows/Linux/macOS

### 2. 运行服务器示例
```bash
# 进入服务器示例目录
cd examples/TcpDemo/Liam.TcpServer.Example

# 运行程序
dotnet run

# 选择功能（建议先选择 "1. 启动基本TCP服务器"）
```

### 3. 运行客户端示例
```bash
# 新开一个终端，进入客户端示例目录
cd examples/TcpDemo/Liam.TcpClient.Example

# 运行程序
dotnet run

# 选择功能（建议先选择 "1. 基本连接测试"）
```

### 4. 基本测试流程
1. **启动服务器**: 运行服务器示例，选择"启动基本TCP服务器"
2. **连接客户端**: 运行客户端示例，选择"基本连接测试"
3. **测试通信**: 观察服务器和客户端的消息交互
4. **探索功能**: 尝试其他高级功能如SSL、心跳、连接池等

## 演示场景

### 🔧 基础功能演示
1. **TCP连接建立**: 演示基本的TCP客户端-服务器连接
2. **消息收发**: 展示双向消息传输和回显功能
3. **连接管理**: 演示多客户端连接的管理和监控
4. **错误处理**: 展示网络异常的处理和恢复

### 🔒 安全功能演示
1. **SSL/TLS连接**: 演示加密通信的建立和使用
2. **证书验证**: 展示SSL证书的验证机制
3. **安全配置**: 演示安全连接的各种配置选项

### ⚡ 高级功能演示
1. **心跳检测**: 演示连接状态的自动检测
2. **自动重连**: 展示客户端的智能重连机制
3. **连接池**: 演示连接复用和负载均衡
4. **性能监控**: 展示实时性能指标和质量监控

### 🚀 性能测试演示
1. **压力测试**: 演示服务器的并发处理能力
2. **吞吐量测试**: 测试消息传输的性能指标
3. **延迟分析**: 分析网络延迟和响应时间
4. **资源监控**: 监控CPU、内存等系统资源使用

## 配置说明

### 服务器配置 (appsettings.json)
```json
{
  "TcpServer": {
    "Port": 8080,                    // 监听端口
    "MaxConnections": 100,           // 最大连接数
    "EnableSsl": false,              // SSL/TLS开关
    "EnableHeartbeat": true,         // 心跳检测开关
    "HeartbeatInterval": 30          // 心跳间隔(秒)
  }
}
```

### 客户端配置 (appsettings.json)
```json
{
  "TcpClient": {
    "ServerHost": "localhost",       // 服务器地址
    "ServerPort": 8080,              // 服务器端口
    "EnableAutoReconnect": true,     // 自动重连开关
    "ConnectionPoolSize": 10         // 连接池大小
  }
}
```

## 测试场景

### 场景1: 基本通信测试
1. 启动服务器，监听8080端口
2. 启动客户端，连接到服务器
3. 发送测试消息，验证回显功能
4. 观察连接状态和统计信息

### 场景2: SSL安全连接测试
1. 配置SSL证书（测试环境可跳过）
2. 启动SSL服务器，监听8443端口
3. 启动SSL客户端，建立加密连接
4. 测试加密消息传输

### 场景3: 多客户端并发测试
1. 启动服务器
2. 运行客户端的"并发连接测试"
3. 观察服务器的连接管理和性能表现
4. 分析并发处理能力

### 场景4: 故障恢复测试
1. 建立客户端连接
2. 手动停止服务器
3. 观察客户端的重连行为
4. 重启服务器，验证自动恢复

### 场景5: 性能基准测试
1. 启动服务器的压力测试模式
2. 运行客户端的消息收发测试
3. 记录性能指标和资源使用
4. 分析性能瓶颈和优化点

## 故障排除

### 常见问题
1. **端口被占用**: 更改配置文件中的端口号
2. **防火墙阻止**: 检查防火墙设置，允许TCP连接
3. **SSL证书问题**: 在测试环境中可以关闭证书验证
4. **内存不足**: 减少并发连接数或增加系统内存

### 调试技巧
1. **启用详细日志**: 设置日志级别为Debug
2. **使用网络工具**: netstat、telnet、Wireshark等
3. **监控系统资源**: 任务管理器、性能监视器
4. **分步测试**: 从基本功能开始，逐步测试高级功能

## 扩展开发

### 自定义协议
```csharp
// 实现自定义消息协议
public class CustomProtocolHandler
{
    public async Task<byte[]> ProcessMessageAsync(byte[] data)
    {
        // 自定义消息处理逻辑
        return processedData;
    }
}
```

### 中间件集成
```csharp
// 添加自定义中间件
services.AddLiamTcpServer(configuration)
    .AddMiddleware<CompressionMiddleware>()
    .AddMiddleware<AuthenticationMiddleware>();
```

### 监控集成
```csharp
// 集成监控系统
services.AddLiamTcpServer(configuration)
    .AddMetrics()
    .AddHealthChecks();
```

## 相关资源

- [Liam.TcpServer 文档](../../src/Liam.TcpServer/README.md)
- [Liam.TcpClient 文档](../../src/Liam.TcpClient/README.md)
- [Liam.Logging 文档](../../src/Liam.Logging/README.md)
- [.NET 8 网络编程指南](https://docs.microsoft.com/dotnet/core/extensions/networking)
- [TCP/IP 协议详解](https://tools.ietf.org/html/rfc793)

## 技术支持

如果在使用过程中遇到问题，请：
1. 查看相关文档和故障排除指南
2. 检查日志文件中的错误信息
3. 在GitHub上提交Issue
4. 参考示例代码和最佳实践

---

**注意**: 这些示例程序仅用于演示和学习目的，在生产环境中使用时请根据实际需求进行适当的安全配置和性能优化。
