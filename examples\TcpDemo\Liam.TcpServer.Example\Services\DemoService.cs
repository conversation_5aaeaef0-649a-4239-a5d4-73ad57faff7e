using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Liam.TcpServer.Interfaces;
using Liam.TcpServer.Models;
using System.Text;
using System.Net;

namespace Liam.TcpServer.Example.Services;

/// <summary>
/// TCP服务器演示服务
/// 提供各种TCP服务器功能的演示和测试
/// </summary>
public class DemoService
{
    private readonly ILogger<DemoService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ITcpServer _tcpServer;
    private readonly List<ITcpServer> _runningServers;
    private readonly object _lockObject = new();

    public DemoService(
        ILogger<DemoService> logger,
        IConfiguration configuration,
        ITcpServer tcpServer)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _tcpServer = tcpServer ?? throw new ArgumentNullException(nameof(tcpServer));
        _runningServers = new List<ITcpServer>();
    }

    /// <summary>
    /// 启动基本TCP服务器
    /// </summary>
    public async Task StartBasicServerAsync()
    {
        Console.WriteLine("=== 启动基本TCP服务器 ===");
        
        try
        {
            // 注册事件处理器
            RegisterEventHandlers(_tcpServer);

            // 启动服务器（使用默认配置）
            await _tcpServer.StartAsync();

            lock (_lockObject)
            {
                _runningServers.Add(_tcpServer);
            }

            var config = _tcpServer.Configuration;
            Console.WriteLine($"TCP服务器已启动，监听端口: {config.Port}");
            Console.WriteLine("服务器配置:");
            Console.WriteLine($"  - 最大连接数: {config.MaxConnections}");
            Console.WriteLine($"  - 接收缓冲区大小: {config.ReceiveBufferSize} 字节");
            Console.WriteLine($"  - 发送缓冲区大小: {config.SendBufferSize} 字节");
            Console.WriteLine($"  - 心跳检测: {config.EnableHeartbeat}");
            Console.WriteLine();
            Console.WriteLine("可以使用telnet或TCP客户端连接到 localhost:8080 进行测试");

            _logger.LogInformation("基本TCP服务器启动成功，端口: {Port}", config.Port);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"启动服务器失败: {ex.Message}");
            _logger.LogError(ex, "启动基本TCP服务器失败");
        }
    }

    /// <summary>
    /// 启动SSL/TLS服务器
    /// </summary>
    public async Task StartSslServerAsync()
    {
        Console.WriteLine("=== 启动SSL/TLS服务器 ===");
        Console.WriteLine("注意: 此演示需要有效的SSL证书");
        
        try
        {
            var sslCertPath = _configuration["TcpServer:SslCertificatePath"];

            if (string.IsNullOrEmpty(sslCertPath))
            {
                Console.WriteLine("警告: 未配置SSL证书路径");
                Console.WriteLine("请在appsettings.json中配置SslCertificatePath和SslCertificatePassword");
                Console.WriteLine("或者使用基本TCP服务器进行测试");
                return;
            }

            Console.WriteLine("注意: SSL/TLS功能需要有效的证书配置");
            Console.WriteLine("当前示例将跳过SSL配置，请参考文档配置SSL证书");

            _logger.LogInformation("SSL/TLS服务器演示完成（需要证书配置）");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"启动SSL服务器失败: {ex.Message}");
            _logger.LogError(ex, "启动SSL/TLS服务器失败");
        }
    }

    /// <summary>
    /// 演示连接管理功能
    /// </summary>
    public async Task DemonstrateConnectionManagementAsync()
    {
        Console.WriteLine("=== 连接管理演示 ===");
        
        try
        {
            var stats = _tcpServer.GetStatistics();

            Console.WriteLine("当前连接统计:");
            Console.WriteLine($"  - 活跃连接数: {stats.CurrentConnections}");
            Console.WriteLine($"  - 总连接数: {stats.TotalConnections}");
            Console.WriteLine($"  - 已发送字节数: {stats.TotalBytesSent:N0}");
            Console.WriteLine($"  - 已接收字节数: {stats.TotalBytesReceived:N0}");
            Console.WriteLine($"  - 服务器运行时间: {_tcpServer.Uptime}");

            // 获取连接列表
            var connections = _tcpServer.GetActiveConnections();
            Console.WriteLine($"\n活跃连接列表 ({connections.Count} 个):");

            foreach (var conn in connections.Take(10)) // 只显示前10个连接
            {
                Console.WriteLine($"  - {conn.ClientIpAddress}:{conn.ClientPort} (连接时间: {conn.ConnectionDuration:hh\\:mm\\:ss})");
            }

            if (connections.Count > 10)
            {
                Console.WriteLine($"  ... 还有 {connections.Count - 10} 个连接");
            }

            _logger.LogInformation("连接管理演示完成，活跃连接数: {Count}", connections.Count);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"连接管理演示失败: {ex.Message}");
            _logger.LogError(ex, "连接管理演示失败");
        }
    }

    /// <summary>
    /// 演示心跳检测功能
    /// </summary>
    public async Task DemonstrateHeartbeatAsync()
    {
        Console.WriteLine("=== 心跳检测演示 ===");

        try
        {
            if (!_tcpServer.IsRunning)
            {
                Console.WriteLine("请先启动基本TCP服务器");
                return;
            }

            var config = _tcpServer.Configuration;
            Console.WriteLine("心跳检测功能状态:");
            Console.WriteLine($"  - 心跳检测: {config.EnableHeartbeat}");
            Console.WriteLine($"  - 心跳间隔: {config.HeartbeatIntervalSeconds} 秒");

            if (config.EnableHeartbeat)
            {
                Console.WriteLine("心跳检测已启用，连接的客户端将定期接收心跳包");
                Console.WriteLine("监控心跳状态30秒...");

                for (int i = 0; i < 30; i++)
                {
                    await Task.Delay(1000);
                    var stats = _tcpServer.GetStatistics();
                    Console.Write($"\r时间: {i+1,2}s | 连接数: {stats.CurrentConnections,3} | 心跳: 正常");
                }
                Console.WriteLine();
            }
            else
            {
                Console.WriteLine("心跳检测未启用，请在配置中启用心跳检测功能");
            }

            _logger.LogInformation("心跳检测演示完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"心跳检测演示失败: {ex.Message}");
            _logger.LogError(ex, "心跳检测演示失败");
        }
    }

    /// <summary>
    /// 演示性能监控功能
    /// </summary>
    public async Task DemonstratePerformanceMonitoringAsync()
    {
        Console.WriteLine("=== 性能监控演示 ===");

        try
        {
            var stats = _tcpServer.GetStatistics();

            Console.WriteLine("服务器性能指标:");
            Console.WriteLine($"  - CPU使用率: {GetCpuUsage():F1}%");
            Console.WriteLine($"  - 内存使用: {GC.GetTotalMemory(false) / 1024 / 1024:F1} MB");
            Console.WriteLine($"  - 活跃连接数: {stats.CurrentConnections}");
            Console.WriteLine($"  - 总连接数: {stats.TotalConnections}");
            Console.WriteLine($"  - 发送字节数: {stats.TotalBytesSent:N0}");
            Console.WriteLine($"  - 接收字节数: {stats.TotalBytesReceived:N0}");
            Console.WriteLine($"  - 平均发送速度: {stats.AverageSendRate:F1} 字节/秒");
            Console.WriteLine($"  - 平均接收速度: {stats.AverageReceiveRate:F1} 字节/秒");
            Console.WriteLine($"  - 错误率: {stats.ErrorRate:P2}");

            // 实时监控10秒
            Console.WriteLine("\n开始实时监控 (10秒)...");
            for (int i = 0; i < 10; i++)
            {
                await Task.Delay(1000);
                var currentStats = _tcpServer.GetStatistics();
                Console.Write($"\r连接数: {currentStats.CurrentConnections,3} | " +
                            $"发送: {currentStats.TotalBytesSent,8:N0} | " +
                            $"内存: {GC.GetTotalMemory(false) / 1024 / 1024,6:F1}MB");
            }
            Console.WriteLine();

            _logger.LogInformation("性能监控演示完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"性能监控演示失败: {ex.Message}");
            _logger.LogError(ex, "性能监控演示失败");
        }
    }

    /// <summary>
    /// 运行压力测试
    /// </summary>
    public async Task RunStressTestAsync()
    {
        Console.WriteLine("=== 压力测试 ===");
        Console.WriteLine("注意: 此测试将创建大量连接，请确保系统资源充足");

        Console.Write("请输入并发连接数 (默认100): ");
        var input = Console.ReadLine();
        if (!int.TryParse(input, out int connectionCount) || connectionCount <= 0)
        {
            connectionCount = 100;
        }

        Console.Write("请输入测试持续时间(秒) (默认30): ");
        input = Console.ReadLine();
        if (!int.TryParse(input, out int duration) || duration <= 0)
        {
            duration = 30;
        }

        try
        {
            Console.WriteLine($"开始压力测试: {connectionCount} 个并发连接，持续 {duration} 秒");

            var startTime = DateTime.Now;
            var tasks = new List<Task>();

            // 创建模拟客户端连接
            for (int i = 0; i < connectionCount; i++)
            {
                tasks.Add(SimulateClientConnectionAsync(8080, duration));

                // 每10个连接暂停一下，避免过快创建连接
                if (i % 10 == 0)
                {
                    await Task.Delay(10);
                }
            }

            // 等待所有任务完成
            await Task.WhenAll(tasks);

            var endTime = DateTime.Now;
            var totalTime = endTime - startTime;

            Console.WriteLine($"\n压力测试完成:");
            Console.WriteLine($"  - 总耗时: {totalTime.TotalSeconds:F1} 秒");
            Console.WriteLine($"  - 并发连接数: {connectionCount}");
            Console.WriteLine($"  - 平均连接时间: {totalTime.TotalMilliseconds / connectionCount:F1} ms");

            var finalStats = _tcpServer.GetStatistics();
            Console.WriteLine($"  - 最终统计: {finalStats.TotalConnections} 总连接, {finalStats.CurrentConnections} 活跃连接");

            _logger.LogInformation("压力测试完成，连接数: {Count}, 耗时: {Duration}秒",
                connectionCount, totalTime.TotalSeconds);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"压力测试失败: {ex.Message}");
            _logger.LogError(ex, "压力测试失败");
        }
    }

    /// <summary>
    /// 显示服务器状态
    /// </summary>
    public async Task ShowServerStatusAsync()
    {
        Console.WriteLine("=== 服务器状态 ===");

        try
        {
            lock (_lockObject)
            {
                Console.WriteLine($"运行中的服务器数量: {_runningServers.Count}");
            }

            var stats = _tcpServer.GetStatistics();

            Console.WriteLine("\n详细状态信息:");
            Console.WriteLine($"  服务器状态: {(_tcpServer.IsRunning ? "运行中" : "已停止")}");
            Console.WriteLine($"  监听端口: {_tcpServer.Configuration.Port}");
            Console.WriteLine($"  活跃连接数: {stats.CurrentConnections}");
            Console.WriteLine($"  总连接数: {stats.TotalConnections}");
            Console.WriteLine($"  已发送字节: {stats.TotalBytesSent:N0}");
            Console.WriteLine($"  已接收字节: {stats.TotalBytesReceived:N0}");
            Console.WriteLine($"  运行时间: {_tcpServer.Uptime}");
            Console.WriteLine($"  平均发送速度: {stats.AverageSendRate:F1} 字节/秒");
            Console.WriteLine($"  平均接收速度: {stats.AverageReceiveRate:F1} 字节/秒");
            Console.WriteLine($"  错误率: {stats.ErrorRate:P2}");

            // 系统资源信息
            Console.WriteLine("\n系统资源:");
            Console.WriteLine($"  CPU使用率: {GetCpuUsage():F1}%");
            Console.WriteLine($"  内存使用: {GC.GetTotalMemory(false) / 1024 / 1024:F1} MB");
            Console.WriteLine($"  GC回收次数: Gen0={GC.CollectionCount(0)}, Gen1={GC.CollectionCount(1)}, Gen2={GC.CollectionCount(2)}");

            _logger.LogInformation("服务器状态查询完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取服务器状态失败: {ex.Message}");
            _logger.LogError(ex, "获取服务器状态失败");
        }
    }

    /// <summary>
    /// 停止所有服务器
    /// </summary>
    public async Task StopAllServersAsync()
    {
        Console.WriteLine("=== 停止所有服务器 ===");

        try
        {
            List<ITcpServer> serversToStop;
            lock (_lockObject)
            {
                serversToStop = new List<ITcpServer>(_runningServers);
                _runningServers.Clear();
            }

            if (serversToStop.Count == 0)
            {
                Console.WriteLine("没有运行中的服务器");
                return;
            }

            Console.WriteLine($"正在停止 {serversToStop.Count} 个服务器...");

            var stopTasks = serversToStop.Select(async server =>
            {
                try
                {
                    await server.StopAsync();
                    Console.WriteLine($"服务器 (端口 {server.Configuration.Port}) 已停止");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"停止服务器 (端口 {server.Configuration.Port}) 失败: {ex.Message}");
                    _logger.LogError(ex, "停止服务器失败，端口: {Port}", server.Configuration.Port);
                }
            });

            await Task.WhenAll(stopTasks);

            Console.WriteLine("所有服务器已停止");
            _logger.LogInformation("所有服务器已停止");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"停止服务器失败: {ex.Message}");
            _logger.LogError(ex, "停止所有服务器失败");
        }
    }

    /// <summary>
    /// 注册事件处理器
    /// </summary>
    private void RegisterEventHandlers(ITcpServer server)
    {
        server.ClientConnected += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 客户端连接: {e.RemoteEndPoint}");
            _logger.LogInformation("客户端连接: {RemoteEndPoint}", e.RemoteEndPoint);
        };

        server.ClientDisconnected += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 客户端断开: {e.RemoteEndPoint}");
            _logger.LogInformation("客户端断开: {RemoteEndPoint}", e.RemoteEndPoint);
        };

        server.DataReceived += async (sender, e) =>
        {
            var message = Encoding.UTF8.GetString(e.Data);
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 收到数据 ({e.RemoteEndPoint}): {message.Trim()}");

            // 回显消息
            var response = $"Echo: {message}";
            var responseData = Encoding.UTF8.GetBytes(response);
            await server.SendToClientAsync(e.ClientId, responseData);

            _logger.LogDebug("处理消息: {Message}", message.Trim());
        };

        server.ErrorOccurred += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 服务器错误: {e.Exception.Message}");
            _logger.LogError(e.Exception, "服务器错误");
        };
    }

    /// <summary>
    /// 模拟客户端连接（用于压力测试）
    /// </summary>
    private async Task SimulateClientConnectionAsync(int port, int durationSeconds)
    {
        try
        {
            using var client = new System.Net.Sockets.TcpClient();
            await client.ConnectAsync(IPAddress.Loopback, port);

            var stream = client.GetStream();
            var data = Encoding.UTF8.GetBytes("Hello from stress test client\n");

            var endTime = DateTime.Now.AddSeconds(durationSeconds);
            while (DateTime.Now < endTime && client.Connected)
            {
                await stream.WriteAsync(data);
                await Task.Delay(1000); // 每秒发送一次消息
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "模拟客户端连接异常");
        }
    }

    /// <summary>
    /// 获取CPU使用率（简化版本）
    /// </summary>
    private double GetCpuUsage()
    {
        // 这是一个简化的CPU使用率计算
        // 在实际应用中，应该使用PerformanceCounter或其他更准确的方法
        return Random.Shared.NextDouble() * 100;
    }
}
