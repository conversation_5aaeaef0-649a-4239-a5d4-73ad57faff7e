using Liam.Logging.Constants;
using <PERSON>.Logging.Interfaces;

namespace Liam.Logging.Example.Services;

/// <summary>
/// 过滤演示服务实现
/// 演示日志过滤和格式化功能
/// </summary>
public class FilteringDemoService : IFilteringDemoService
{
    private readonly ILiamLogger _logger;

    /// <summary>
    /// 初始化过滤演示服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public FilteringDemoService(ILiamLogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 演示日志过滤
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateFilteringAsync()
    {
        Console.WriteLine("=== 日志过滤演示 ===\n");

        Console.WriteLine("1. 级别过滤演示:");
        
        await DemonstrateLevelFilteringAsync();

        Console.WriteLine("\n2. 类别过滤演示:");
        
        await DemonstrateCategoryFilteringAsync();

        Console.WriteLine("\n3. 消息内容过滤演示:");
        
        await DemonstrateMessageFilteringAsync();

        Console.WriteLine("\n4. 自定义属性过滤演示:");
        
        await DemonstratePropertyFilteringAsync();

        Console.WriteLine("\n5. 复合过滤条件演示:");
        
        await DemonstrateComplexFilteringAsync();

        Console.WriteLine("\n=== 日志过滤演示完成 ===");
    }

    /// <summary>
    /// 演示日志格式化
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateFormattingAsync()
    {
        Console.WriteLine("=== 日志格式化演示 ===\n");

        Console.WriteLine("1. 基本格式化演示:");
        
        await DemonstrateBasicFormattingAsync();

        Console.WriteLine("\n2. 时间戳格式化演示:");
        
        await DemonstrateTimestampFormattingAsync();

        Console.WriteLine("\n3. 结构化数据格式化演示:");
        
        await DemonstrateStructuredFormattingAsync();

        Console.WriteLine("\n4. 异常信息格式化演示:");
        
        await DemonstrateExceptionFormattingAsync();

        Console.WriteLine("\n5. 自定义格式化演示:");
        
        await DemonstrateCustomFormattingAsync();

        Console.WriteLine("\n=== 日志格式化演示完成 ===");
    }

    /// <summary>
    /// 演示级别过滤
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateLevelFilteringAsync()
    {
        Console.WriteLine("   测试不同级别的日志记录 (根据配置，某些级别可能被过滤):");
        
        // 记录不同级别的日志，观察哪些会被过滤
        var logMessages = new[]
        {
            (LogLevel.Trace, "跟踪级别日志 - 非常详细的调试信息"),
            (LogLevel.Debug, "调试级别日志 - 开发时的调试信息"),
            (LogLevel.Information, "信息级别日志 - 常规运行信息"),
            (LogLevel.Warning, "警告级别日志 - 潜在问题提醒"),
            (LogLevel.Error, "错误级别日志 - 错误信息记录"),
            (LogLevel.Critical, "严重级别日志 - 严重系统错误")
        };

        foreach (var (level, message) in logMessages)
        {
            if (_logger.IsEnabled(level))
            {
                _logger.Log(level, message);
                Console.WriteLine($"   ✓ {level} 级别已启用并记录");
            }
            else
            {
                Console.WriteLine($"   ✗ {level} 级别被过滤，未记录");
            }
            
            await Task.Delay(50);
        }
        
        Console.WriteLine("✓ 级别过滤演示完成");
    }

    /// <summary>
    /// 演示类别过滤
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateCategoryFilteringAsync()
    {
        Console.WriteLine("   测试不同类别的日志记录 (某些类别可能被过滤):");
        
        var categories = new[]
        {
            "Liam.Logging.Example",
            "Microsoft.AspNetCore",
            "Microsoft.EntityFrameworkCore",
            "System.Net.Http",
            "Application.BusinessLogic",
            "Application.DataAccess",
            "Application.Security",
            "ThirdParty.Library"
        };

        foreach (var category in categories)
        {
            // 模拟不同类别的日志记录
            _logger.LogInformation($"来自类别 '{category}' 的日志消息");
            Console.WriteLine($"   记录了类别: {category}");
            await Task.Delay(30);
        }
        
        Console.WriteLine("✓ 类别过滤演示完成 (检查日志输出查看过滤效果)");
    }

    /// <summary>
    /// 演示消息内容过滤
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateMessageFilteringAsync()
    {
        Console.WriteLine("   测试包含特定关键词的日志消息:");
        
        var messages = new[]
        {
            "普通的业务操作日志",
            "性能监控: 响应时间 250ms",
            "用户登录成功",
            "数据库连接超时",
            "缓存命中率: 95%",
            "性能警告: 内存使用率过高",
            "安全事件: 检测到异常登录",
            "系统启动完成",
            "性能统计: 处理了 1000 个请求",
            "错误: 文件未找到"
        };

        foreach (var message in messages)
        {
            _logger.LogInformation(message);
            Console.WriteLine($"   记录消息: {message}");
            await Task.Delay(40);
        }
        
        Console.WriteLine("✓ 消息内容过滤演示完成 (包含'性能'关键词的消息可能被特殊处理)");
    }

    /// <summary>
    /// 演示属性过滤
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstratePropertyFilteringAsync()
    {
        Console.WriteLine("   测试包含特定属性的结构化日志:");
        
        var events = new[]
        {
            new { UserId = 1001, Action = "Login", Success = true, Duration = 150 },
            new { UserId = 1002, Action = "Purchase", Success = true, Duration = 2500 },
            new { UserId = 1003, Action = "Login", Success = false, Duration = 100 },
            new { UserId = 1004, Action = "UpdateProfile", Success = true, Duration = 800 },
            new { UserId = 1005, Action = "Purchase", Success = false, Duration = 1200 }
        };

        foreach (var evt in events)
        {
            _logger.LogStructured(LogLevel.Information,
                "用户操作: UserId={UserId}, Action={Action}, Success={Success}, Duration={Duration}ms",
                evt.UserId, evt.Action, evt.Success, evt.Duration);
            
            Console.WriteLine($"   记录用户 {evt.UserId} 的 {evt.Action} 操作 ({(evt.Success ? "成功" : "失败")})");
            await Task.Delay(60);
        }
        
        Console.WriteLine("✓ 属性过滤演示完成");
    }

    /// <summary>
    /// 演示复合过滤条件
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateComplexFilteringAsync()
    {
        Console.WriteLine("   测试复合过滤条件 (级别 + 类别 + 内容):");
        
        var complexScenarios = new[]
        {
            new { Level = LogLevel.Debug, Category = "Microsoft.AspNetCore", Message = "HTTP请求处理" },
            new { Level = LogLevel.Information, Category = "Application.BusinessLogic", Message = "业务逻辑执行" },
            new { Level = LogLevel.Warning, Category = "System.Performance", Message = "性能监控警告" },
            new { Level = LogLevel.Error, Category = "Microsoft.EntityFrameworkCore", Message = "数据库操作失败" },
            new { Level = LogLevel.Information, Category = "Application.Security", Message = "安全检查通过" },
            new { Level = LogLevel.Debug, Category = "ThirdParty.Library", Message = "第三方库调用" }
        };

        foreach (var scenario in complexScenarios)
        {
            if (_logger.IsEnabled(scenario.Level))
            {
                _logger.Log(scenario.Level, $"[{scenario.Category}] {scenario.Message}");
                Console.WriteLine($"   ✓ 记录: {scenario.Level} - {scenario.Category} - {scenario.Message}");
            }
            else
            {
                Console.WriteLine($"   ✗ 过滤: {scenario.Level} - {scenario.Category} - {scenario.Message}");
            }
            
            await Task.Delay(70);
        }
        
        Console.WriteLine("✓ 复合过滤条件演示完成");
    }

    /// <summary>
    /// 演示基本格式化
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateBasicFormattingAsync()
    {
        Console.WriteLine("   测试基本日志格式化:");
        
        _logger.LogInformation("简单文本消息");
        _logger.LogStructured(LogLevel.Information, "包含数字的消息: {Number}", 42);
        _logger.LogStructured(LogLevel.Information, "包含字符串的消息: {Text}", "Hello World");
        _logger.LogStructured(LogLevel.Information, "包含布尔值的消息: {Flag}", true);
        _logger.LogStructured(LogLevel.Information, "包含多个参数的消息: {Name}, {Age}, {City}", "张三", 25, "北京");
        
        await Task.Delay(100);
        Console.WriteLine("✓ 基本格式化演示完成");
    }

    /// <summary>
    /// 演示时间戳格式化
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateTimestampFormattingAsync()
    {
        Console.WriteLine("   测试时间戳格式化:");
        
        var now = DateTime.Now;
        var utcNow = DateTime.UtcNow;
        
        _logger.LogStructured(LogLevel.Information, "当前本地时间: {LocalTime}", now);
        _logger.LogStructured(LogLevel.Information, "当前UTC时间: {UtcTime}", utcNow);
        _logger.LogStructured(LogLevel.Information, "格式化时间: {FormattedTime}", now.ToString("yyyy-MM-dd HH:mm:ss"));
        _logger.LogStructured(LogLevel.Information, "短时间格式: {ShortTime}", now.ToString("HH:mm:ss"));
        _logger.LogStructured(LogLevel.Information, "日期格式: {DateOnly}", now.ToString("yyyy-MM-dd"));
        
        await Task.Delay(100);
        Console.WriteLine("✓ 时间戳格式化演示完成");
    }

    /// <summary>
    /// 演示结构化数据格式化
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateStructuredFormattingAsync()
    {
        Console.WriteLine("   测试结构化数据格式化:");
        
        var user = new
        {
            Id = 12345,
            Name = "李四",
            Email = "<EMAIL>",
            CreatedAt = DateTime.Now.AddDays(-30),
            IsActive = true,
            Roles = new[] { "User", "Manager" }
        };

        var order = new
        {
            OrderId = "ORD-2024-001",
            Amount = 299.99m,
            Currency = "CNY",
            Items = 3,
            Status = "Completed"
        };

        _logger.LogStructured(LogLevel.Information, "用户信息: {User}", user);
        _logger.LogStructured(LogLevel.Information, "订单信息: {Order}", order);
        _logger.LogStructured(LogLevel.Information, "用户 {UserId} 创建了订单 {OrderId}，金额 {Amount}",
            user.Id, order.OrderId, order.Amount.ToString("C"));
        
        await Task.Delay(100);
        Console.WriteLine("✓ 结构化数据格式化演示完成");
    }

    /// <summary>
    /// 演示异常信息格式化
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateExceptionFormattingAsync()
    {
        Console.WriteLine("   测试异常信息格式化:");
        
        try
        {
            // 创建一个嵌套异常
            try
            {
                throw new FileNotFoundException("配置文件未找到: config.json");
            }
            catch (Exception innerEx)
            {
                throw new InvalidOperationException("系统初始化失败", innerEx);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("捕获到异常", ex);
            _logger.LogStructured(LogLevel.Error, "异常类型: {ExceptionType}, 消息: {Message}", ex.GetType().Name, ex.Message);

            if (ex.InnerException != null)
            {
                _logger.LogStructured(LogLevel.Error, "内部异常: {InnerExceptionType}, 消息: {InnerMessage}",
                    ex.InnerException.GetType().Name, ex.InnerException.Message);
            }
        }
        
        await Task.Delay(100);
        Console.WriteLine("✓ 异常信息格式化演示完成");
    }

    /// <summary>
    /// 演示自定义格式化
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateCustomFormattingAsync()
    {
        Console.WriteLine("   测试自定义格式化:");
        
        // 数值格式化
        var price = 1234.56m;
        var percentage = 0.8567;
        var fileSize = 1024 * 1024 * 15; // 15MB
        
        _logger.LogStructured(LogLevel.Information, "价格格式化: {Price}", price.ToString("C"));
        _logger.LogStructured(LogLevel.Information, "百分比格式化: {Percentage}", percentage.ToString("P2"));
        _logger.LogStructured(LogLevel.Information, "文件大小: {FileSize} bytes", fileSize.ToString("N0"));
        _logger.LogStructured(LogLevel.Information, "十六进制: {Value}", 255.ToString("X8"));
        
        // 自定义对象格式化
        var performance = new
        {
            ResponseTime = TimeSpan.FromMilliseconds(245),
            ThroughputPerSecond = 1250,
            MemoryUsage = 85.6,
            CpuUsage = 42.3
        };
        
        _logger.LogStructured(LogLevel.Information,
            "性能指标: 响应时间={ResponseTime}, 吞吐量={Throughput}/秒, 内存={Memory}%, CPU={Cpu}%",
            performance.ResponseTime, performance.ThroughputPerSecond,
            performance.MemoryUsage.ToString("F1"), performance.CpuUsage.ToString("F1"));
        
        await Task.Delay(100);
        Console.WriteLine("✓ 自定义格式化演示完成");
    }
}
