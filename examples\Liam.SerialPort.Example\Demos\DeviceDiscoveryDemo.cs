using Microsoft.Extensions.Logging;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;
using Liam.SerialPort.Events;
using Liam.SerialPort.Example.Services;

namespace Liam.SerialPort.Example.Demos;

/// <summary>
/// 设备发现演示
/// 展示ISerialPortDiscovery接口的直接使用和设备监控功能
/// </summary>
public class DeviceDiscoveryDemo
{
    private readonly ILogger<DeviceDiscoveryDemo> _logger;
    private readonly ISerialPortDiscovery _discovery;
    private readonly IMenuService _menuService;
    private readonly IPerformanceMonitor _performanceMonitor;
    private readonly CancellationTokenSource _cancellationTokenSource = new();

    /// <summary>
    /// 构造函数
    /// </summary>
    public DeviceDiscoveryDemo(
        ILogger<DeviceDiscoveryDemo> logger,
        ISerialPortDiscovery discovery,
        IMenuService menuService,
        IPerformanceMonitor performanceMonitor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _discovery = discovery ?? throw new ArgumentNullException(nameof(discovery));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
    }

    /// <summary>
    /// 运行设备发现演示
    /// </summary>
    public async Task<bool> RunAsync()
    {
        _menuService.ClearScreen();
        
        Console.ForegroundColor = ConsoleColor.DarkBlue;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    设备发现演示                              ║");
        Console.WriteLine("║  演示内容：设备监控、刷新列表、端口检查、热插拔检测          ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();

        try
        {
            // 注册设备变化事件
            RegisterDeviceEvents();

            // 1. 演示基础设备发现
            await DemonstrateBasicDiscoveryAsync();

            // 2. 演示设备刷新功能
            await DemonstrateRefreshAsync();

            // 3. 演示端口可用性检查
            await DemonstratePortAvailabilityAsync();

            // 4. 演示设备监控
            await DemonstrateDeviceMonitoringAsync();

            // 5. 演示设备信息详细分析
            await DemonstrateDeviceInfoAnalysisAsync();

            _menuService.ShowStatus("设备发现演示完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备发现演示过程中发生错误");
            _menuService.ShowStatus($"演示失败: {ex.Message}", StatusType.Error);
        }
        finally
        {
            // 停止监控
            await _discovery.StopMonitoringAsync();
            UnregisterDeviceEvents();
            _cancellationTokenSource.Cancel();
        }

        return true;
    }

    /// <summary>
    /// 注册设备事件
    /// </summary>
    private void RegisterDeviceEvents()
    {
        _discovery.DeviceChanged += OnDeviceChanged;

        _logger.LogDebug("设备发现事件已注册");
    }

    /// <summary>
    /// 取消注册设备事件
    /// </summary>
    private void UnregisterDeviceEvents()
    {
        _discovery.DeviceChanged -= OnDeviceChanged;

        _logger.LogDebug("设备发现事件已取消注册");
    }

    /// <summary>
    /// 演示基础设备发现
    /// </summary>
    private async Task DemonstrateBasicDiscoveryAsync()
    {
        Console.WriteLine("\n--- 基础设备发现演示 ---");
        
        try
        {
            Console.WriteLine("1. 获取当前可用设备列表...");
            var operationId = _performanceMonitor.StartOperation("设备发现");
            
            var devices = await _discovery.GetAvailablePortsAsync();
            var deviceList = devices.ToList();
            
            _performanceMonitor.EndOperation(operationId, true);
            
            Console.WriteLine($"发现 {deviceList.Count} 个串口设备:");
            
            if (deviceList.Any())
            {
                Console.WriteLine();
                Console.WriteLine("端口名称".PadRight(12) + "描述".PadRight(30) + "制造商".PadRight(20) + "设备类型");
                Console.WriteLine(new string('-', 80));
                
                foreach (var device in deviceList)
                {
                    Console.WriteLine(
                        device.PortName.PadRight(12) +
                        (device.Description ?? "未知").PadRight(30) +
                        (device.Manufacturer ?? "未知").PadRight(20) +
                        (device.DeviceType ?? "未知")
                    );
                }
            }
            else
            {
                Console.WriteLine("  未发现任何串口设备");
                Console.WriteLine("  提示: 请连接串口设备或检查驱动程序");
            }
            
            _menuService.ShowStatus("基础设备发现完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "基础设备发现失败");
            _menuService.ShowStatus($"设备发现失败: {ex.Message}", StatusType.Error);
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示设备刷新功能
    /// </summary>
    private async Task DemonstrateRefreshAsync()
    {
        Console.WriteLine("\n--- 设备刷新功能演示 ---");
        
        try
        {
            // 获取初始设备列表
            Console.WriteLine("1. 获取初始设备列表...");
            var initialDevices = await _discovery.GetAvailablePortsAsync();
            var initialCount = initialDevices.Count();
            Console.WriteLine($"初始设备数量: {initialCount}");
            
            // 等待用户操作
            Console.WriteLine("\n2. 请在接下来的10秒内插拔串口设备进行测试...");
            Console.WriteLine("   (如果没有设备可以插拔，程序将继续演示刷新功能)");
            
            for (int i = 10; i > 0; i--)
            {
                Console.Write($"\r倒计时: {i} 秒");
                await Task.Delay(1000, _cancellationTokenSource.Token);
            }
            Console.WriteLine();
            
            // 刷新设备列表
            Console.WriteLine("\n3. 刷新设备列表...");
            var operationId = _performanceMonitor.StartOperation("设备刷新");
            
            await _discovery.RefreshAsync();
            var refreshedDevices = await _discovery.GetAvailablePortsAsync();
            var refreshedCount = refreshedDevices.Count();
            
            _performanceMonitor.EndOperation(operationId, true);
            
            Console.WriteLine($"刷新后设备数量: {refreshedCount}");
            Console.WriteLine($"设备数量变化: {refreshedCount - initialCount:+#;-#;0}");
            
            if (refreshedCount != initialCount)
            {
                Console.WriteLine("\n检测到设备变化:");
                
                var initialPorts = initialDevices.Select(d => d.PortName).ToHashSet();
                var refreshedPorts = refreshedDevices.Select(d => d.PortName).ToHashSet();
                
                var addedPorts = refreshedPorts.Except(initialPorts);
                var removedPorts = initialPorts.Except(refreshedPorts);
                
                foreach (var port in addedPorts)
                {
                    Console.WriteLine($"  + 新增设备: {port}");
                }
                
                foreach (var port in removedPorts)
                {
                    Console.WriteLine($"  - 移除设备: {port}");
                }
            }
            else
            {
                Console.WriteLine("设备列表无变化");
            }
            
            _menuService.ShowStatus("设备刷新完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备刷新失败");
            _menuService.ShowStatus($"设备刷新失败: {ex.Message}", StatusType.Error);
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示端口可用性检查
    /// </summary>
    private async Task DemonstratePortAvailabilityAsync()
    {
        Console.WriteLine("\n--- 端口可用性检查演示 ---");
        
        try
        {
            // 获取当前设备列表
            var devices = await _discovery.GetAvailablePortsAsync();
            var deviceList = devices.ToList();
            
            // 测试已知存在的端口
            if (deviceList.Any())
            {
                Console.WriteLine("1. 检查已知存在的端口:");
                
                foreach (var device in deviceList.Take(3)) // 只检查前3个
                {
                    var operationId = _performanceMonitor.StartOperation($"检查端口{device.PortName}");
                    var isAvailable = await _discovery.IsPortAvailableAsync(device.PortName);
                    _performanceMonitor.EndOperation(operationId, isAvailable);
                    
                    Console.WriteLine($"  {device.PortName}: {(isAvailable ? "可用" : "不可用")}");
                }
            }
            
            // 测试不存在的端口
            Console.WriteLine("\n2. 检查不存在的端口:");
            var nonExistentPorts = new[] { "COM999", "COM888", "/dev/ttyUSB999" };
            
            foreach (var port in nonExistentPorts)
            {
                var operationId = _performanceMonitor.StartOperation($"检查端口{port}");
                var isAvailable = await _discovery.IsPortAvailableAsync(port);
                _performanceMonitor.EndOperation(operationId, isAvailable);
                
                Console.WriteLine($"  {port}: {(isAvailable ? "可用" : "不可用")}");
            }
            
            // 批量检查
            Console.WriteLine("\n3. 批量端口检查:");
            var testPorts = new[] { "COM1", "COM2", "COM3", "COM4", "COM5" };
            var availabilityTasks = testPorts.Select(async port =>
            {
                var isAvailable = await _discovery.IsPortAvailableAsync(port);
                return new { Port = port, Available = isAvailable };
            });
            
            var results = await Task.WhenAll(availabilityTasks);
            
            foreach (var result in results)
            {
                Console.WriteLine($"  {result.Port}: {(result.Available ? "可用" : "不可用")}");
            }
            
            var availableCount = results.Count(r => r.Available);
            Console.WriteLine($"\n批量检查结果: {availableCount}/{results.Length} 个端口可用");
            
            _menuService.ShowStatus("端口可用性检查完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "端口可用性检查失败");
            _menuService.ShowStatus($"端口检查失败: {ex.Message}", StatusType.Error);
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示设备监控
    /// </summary>
    private async Task DemonstrateDeviceMonitoringAsync()
    {
        Console.WriteLine("\n--- 设备监控演示 ---");
        
        try
        {
            Console.WriteLine("1. 启动设备监控...");
            var operationId = _performanceMonitor.StartOperation("设备监控");
            
            await _discovery.StartMonitoringAsync();
            _performanceMonitor.EndOperation(operationId, true);
            
            Console.WriteLine("设备监控已启动");
            Console.WriteLine("\n监控说明:");
            Console.WriteLine("  • 系统将自动检测设备的插入和拔出");
            Console.WriteLine("  • 设备变化将通过事件通知");
            Console.WriteLine("  • 监控将持续30秒");
            
            Console.WriteLine("\n提示: 请尝试插拔串口设备来测试监控功能");
            
            // 监控30秒
            var monitorDuration = TimeSpan.FromSeconds(30);
            var startTime = DateTime.UtcNow;
            var eventCount = 0;
            
            // 临时事件计数器
            EventHandler<DeviceChangedEventArgs> eventCounter = (sender, e) => eventCount++;
            _discovery.DeviceChanged += eventCounter;
            
            try
            {
                while (DateTime.UtcNow - startTime < monitorDuration && !_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    var elapsed = DateTime.UtcNow - startTime;
                    var remaining = monitorDuration - elapsed;
                    
                    Console.Write($"\r监控中... 剩余时间: {remaining.TotalSeconds:F0}s | 检测到事件: {eventCount} 个");
                    
                    await Task.Delay(1000, _cancellationTokenSource.Token);
                }
            }
            finally
            {
                _discovery.DeviceChanged -= eventCounter;
            }
            
            Console.WriteLine($"\n\n监控完成，共检测到 {eventCount} 个设备变化事件");
            
            Console.WriteLine("\n2. 停止设备监控...");
            await _discovery.StopMonitoringAsync();
            Console.WriteLine("设备监控已停止");
            
            _menuService.ShowStatus("设备监控演示完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备监控失败");
            _menuService.ShowStatus($"设备监控失败: {ex.Message}", StatusType.Error);
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示设备信息详细分析
    /// </summary>
    private async Task DemonstrateDeviceInfoAnalysisAsync()
    {
        Console.WriteLine("\n--- 设备信息详细分析演示 ---");
        
        try
        {
            var devices = await _discovery.GetAvailablePortsAsync();
            var deviceList = devices.ToList();
            
            if (!deviceList.Any())
            {
                Console.WriteLine("未发现设备，跳过详细分析");
                return;
            }
            
            Console.WriteLine($"分析 {deviceList.Count} 个设备的详细信息:");
            
            // 按制造商分组
            var byManufacturer = deviceList.GroupBy(d => d.Manufacturer ?? "未知制造商");
            Console.WriteLine("\n1. 按制造商分组:");
            foreach (var group in byManufacturer)
            {
                Console.WriteLine($"  {group.Key}: {group.Count()} 个设备");
                foreach (var device in group)
                {
                    Console.WriteLine($"    - {device.PortName} ({device.Description ?? "无描述"})");
                }
            }
            
            // 按设备类型分组
            var byType = deviceList.GroupBy(d => d.DeviceType ?? "未知类型");
            Console.WriteLine("\n2. 按设备类型分组:");
            foreach (var group in byType)
            {
                Console.WriteLine($"  {group.Key}: {group.Count()} 个设备");
            }
            
            // 端口名称分析
            Console.WriteLine("\n3. 端口名称分析:");
            var windowsPorts = deviceList.Where(d => d.PortName.StartsWith("COM")).ToList();
            var linuxPorts = deviceList.Where(d => d.PortName.StartsWith("/dev/")).ToList();
            
            Console.WriteLine($"  Windows风格端口 (COMx): {windowsPorts.Count} 个");
            Console.WriteLine($"  Linux风格端口 (/dev/xxx): {linuxPorts.Count} 个");
            
            // 详细设备信息
            Console.WriteLine("\n4. 详细设备信息:");
            foreach (var device in deviceList.Take(5)) // 只显示前5个
            {
                Console.WriteLine($"\n设备: {device.PortName}");
                Console.WriteLine($"  描述: {device.Description ?? "无"}");
                Console.WriteLine($"  制造商: {device.Manufacturer ?? "无"}");
                Console.WriteLine($"  设备类型: {device.DeviceType ?? "无"}");
                Console.WriteLine($"  设备实例ID: {device.DeviceInstanceId ?? "无"}");
                Console.WriteLine($"  是否可用: {await _discovery.IsPortAvailableAsync(device.PortName)}");
            }
            
            if (deviceList.Count > 5)
            {
                Console.WriteLine($"\n... 还有 {deviceList.Count - 5} 个设备未显示");
            }
            
            _menuService.ShowStatus("设备信息分析完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备信息分析失败");
            _menuService.ShowStatus($"设备分析失败: {ex.Message}", StatusType.Error);
        }
        
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 设备变化事件处理
    /// </summary>
    private void OnDeviceChanged(object? sender, DeviceChangedEventArgs e)
    {
        Console.WriteLine($"\n[事件] 设备变化: {e.ChangeType} - {e.DeviceInfo.PortName}");
        _performanceMonitor.RecordConnectionEvent(ConnectionEventType.DeviceHotplug, e.DeviceInfo.PortName);
    }
}
