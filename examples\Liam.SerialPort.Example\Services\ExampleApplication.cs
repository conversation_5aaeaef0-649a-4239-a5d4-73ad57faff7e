using Microsoft.Extensions.Logging;
using Liam.SerialPort.Example.Demos;

namespace Liam.SerialPort.Example.Services;

/// <summary>
/// 示例应用程序主服务
/// 负责协调各个演示模块的运行
/// </summary>
public class ExampleApplication
{
    private readonly ILogger<ExampleApplication> _logger;
    private readonly IMenuService _menuService;
    private readonly IPerformanceMonitor _performanceMonitor;
    private readonly BasicOperationsDemo _basicDemo;
    private readonly AdvancedFeaturesDemo _advancedDemo;
    private readonly PerformanceTestDemo _performanceDemo;
    private readonly ConnectionPoolDemo _poolDemo;
    private readonly DataFormatDemo _dataFormatDemo;
    private readonly ErrorHandlingDemo _errorHandlingDemo;
    private readonly ConfigurationDemo _configurationDemo;
    private readonly ExtensionMethodsDemo _extensionMethodsDemo;
    private readonly DataHandlerDemo _dataHandlerDemo;
    private readonly ConnectionManagementDemo _connectionManagementDemo;
    private readonly DeviceDiscoveryDemo _deviceDiscoveryDemo;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="menuService">菜单服务</param>
    /// <param name="performanceMonitor">性能监控器</param>
    /// <param name="basicDemo">基础操作演示</param>
    /// <param name="advancedDemo">高级功能演示</param>
    /// <param name="performanceDemo">性能测试演示</param>
    /// <param name="poolDemo">连接池演示</param>
    /// <param name="dataFormatDemo">数据格式演示</param>
    /// <param name="errorHandlingDemo">错误处理演示</param>
    /// <param name="configurationDemo">配置管理演示</param>
    /// <param name="extensionMethodsDemo">扩展方法演示</param>
    /// <param name="dataHandlerDemo">数据处理器演示</param>
    /// <param name="connectionManagementDemo">连接管理演示</param>
    /// <param name="deviceDiscoveryDemo">设备发现演示</param>
    public ExampleApplication(
        ILogger<ExampleApplication> logger,
        IMenuService menuService,
        IPerformanceMonitor performanceMonitor,
        BasicOperationsDemo basicDemo,
        AdvancedFeaturesDemo advancedDemo,
        PerformanceTestDemo performanceDemo,
        ConnectionPoolDemo poolDemo,
        DataFormatDemo dataFormatDemo,
        ErrorHandlingDemo errorHandlingDemo,
        ConfigurationDemo configurationDemo,
        ExtensionMethodsDemo extensionMethodsDemo,
        DataHandlerDemo dataHandlerDemo,
        ConnectionManagementDemo connectionManagementDemo,
        DeviceDiscoveryDemo deviceDiscoveryDemo)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
        _basicDemo = basicDemo ?? throw new ArgumentNullException(nameof(basicDemo));
        _advancedDemo = advancedDemo ?? throw new ArgumentNullException(nameof(advancedDemo));
        _performanceDemo = performanceDemo ?? throw new ArgumentNullException(nameof(performanceDemo));
        _poolDemo = poolDemo ?? throw new ArgumentNullException(nameof(poolDemo));
        _dataFormatDemo = dataFormatDemo ?? throw new ArgumentNullException(nameof(dataFormatDemo));
        _errorHandlingDemo = errorHandlingDemo ?? throw new ArgumentNullException(nameof(errorHandlingDemo));
        _configurationDemo = configurationDemo ?? throw new ArgumentNullException(nameof(configurationDemo));
        _extensionMethodsDemo = extensionMethodsDemo ?? throw new ArgumentNullException(nameof(extensionMethodsDemo));
        _dataHandlerDemo = dataHandlerDemo ?? throw new ArgumentNullException(nameof(dataHandlerDemo));
        _connectionManagementDemo = connectionManagementDemo ?? throw new ArgumentNullException(nameof(connectionManagementDemo));
        _deviceDiscoveryDemo = deviceDiscoveryDemo ?? throw new ArgumentNullException(nameof(deviceDiscoveryDemo));
    }

    /// <summary>
    /// 运行示例应用程序
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task RunAsync()
    {
        _logger.LogInformation("Liam.SerialPort 示例程序启动");

        try
        {
            // 启动性能监控
            _performanceMonitor.Start();

            // 主循环
            while (true)
            {
                try
                {
                    // 显示主菜单并获取用户选择
                    var choice = await _menuService.ShowMainMenuAsync();

                    // 根据用户选择执行相应的演示
                    var result = await ExecuteDemoAsync(choice);

                    if (!result)
                    {
                        // 用户选择退出
                        break;
                    }

                    // 显示性能统计
                    DisplayPerformanceStats();

                    // 等待用户确认继续
                    Console.WriteLine("\n按任意键继续...");
                    Console.ReadKey();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行演示时发生错误");
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"演示执行失败: {ex.Message}");
                    Console.ResetColor();
                    
                    Console.WriteLine("按任意键继续...");
                    Console.ReadKey();
                }
            }
        }
        finally
        {
            // 停止性能监控
            _performanceMonitor.Stop();
            _logger.LogInformation("Liam.SerialPort 示例程序结束");
        }
    }

    /// <summary>
    /// 执行指定的演示
    /// </summary>
    /// <param name="choice">用户选择</param>
    /// <returns>是否继续运行程序</returns>
    private async Task<bool> ExecuteDemoAsync(MenuChoice choice)
    {
        _logger.LogInformation("执行演示: {DemoName}", choice);

        return choice switch
        {
            MenuChoice.BasicOperations => await _basicDemo.RunAsync(),
            MenuChoice.AdvancedFeatures => await _advancedDemo.RunAsync(),
            MenuChoice.PerformanceTest => await _performanceDemo.RunAsync(),
            MenuChoice.ConnectionPool => await _poolDemo.RunAsync(),
            MenuChoice.DataFormats => await _dataFormatDemo.RunAsync(),
            MenuChoice.ErrorHandling => await _errorHandlingDemo.RunAsync(),
            MenuChoice.Configuration => await _configurationDemo.RunAsync(),
            MenuChoice.ExtensionMethods => await _extensionMethodsDemo.RunAsync(),
            MenuChoice.DataHandler => await _dataHandlerDemo.RunAsync(),
            MenuChoice.ConnectionManagement => await _connectionManagementDemo.RunAsync(),
            MenuChoice.DeviceDiscovery => await _deviceDiscoveryDemo.RunAsync(),
            MenuChoice.Exit => false,
            _ => throw new ArgumentOutOfRangeException(nameof(choice), choice, "未知的菜单选择")
        };
    }

    /// <summary>
    /// 显示性能统计信息
    /// </summary>
    private void DisplayPerformanceStats()
    {
        var stats = _performanceMonitor.GetStatistics();
        
        Console.ForegroundColor = ConsoleColor.Yellow;
        Console.WriteLine("\n=== 性能统计 ===");
        Console.WriteLine($"运行时间: {stats.TotalRunTime:hh\\:mm\\:ss}");
        Console.WriteLine($"总操作数: {stats.TotalOperations}");
        Console.WriteLine($"成功操作: {stats.SuccessfulOperations}");
        Console.WriteLine($"失败操作: {stats.FailedOperations}");
        Console.WriteLine($"成功率: {stats.SuccessRate:P2}");
        Console.WriteLine($"平均响应时间: {stats.AverageResponseTime:F2}ms");
        Console.WriteLine($"内存使用: {stats.MemoryUsage / 1024 / 1024:F2}MB");
        Console.ResetColor();
    }
}

/// <summary>
/// 菜单选择枚举
/// </summary>
public enum MenuChoice
{
    /// <summary>
    /// 基础操作演示
    /// </summary>
    BasicOperations = 1,

    /// <summary>
    /// 高级功能演示
    /// </summary>
    AdvancedFeatures = 2,

    /// <summary>
    /// 性能测试演示
    /// </summary>
    PerformanceTest = 3,

    /// <summary>
    /// 连接池演示
    /// </summary>
    ConnectionPool = 4,

    /// <summary>
    /// 数据格式演示
    /// </summary>
    DataFormats = 5,

    /// <summary>
    /// 错误处理演示
    /// </summary>
    ErrorHandling = 6,

    /// <summary>
    /// 配置管理演示
    /// </summary>
    Configuration = 7,

    /// <summary>
    /// 扩展方法演示
    /// </summary>
    ExtensionMethods = 8,

    /// <summary>
    /// 数据处理器演示
    /// </summary>
    DataHandler = 9,

    /// <summary>
    /// 连接管理演示
    /// </summary>
    ConnectionManagement = 10,

    /// <summary>
    /// 设备发现演示
    /// </summary>
    DeviceDiscovery = 11,

    /// <summary>
    /// 退出程序
    /// </summary>
    Exit = 0
}
