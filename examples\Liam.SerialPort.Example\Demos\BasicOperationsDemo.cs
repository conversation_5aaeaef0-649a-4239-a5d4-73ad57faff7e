using Microsoft.Extensions.Logging;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;
using Liam.SerialPort.Example.Services;
using System.Text;

namespace Liam.SerialPort.Example.Demos;

/// <summary>
/// 基础操作演示
/// 展示串口设备发现、连接建立、基本数据传输等核心功能
/// </summary>
public class BasicOperationsDemo
{
    private readonly ILogger<BasicOperationsDemo> _logger;
    private readonly ISerialPortService _serialPortService;
    private readonly IMenuService _menuService;
    private readonly IPerformanceMonitor _performanceMonitor;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="serialPortService">串口服务</param>
    /// <param name="menuService">菜单服务</param>
    /// <param name="performanceMonitor">性能监控器</param>
    public BasicOperationsDemo(
        ILogger<BasicOperationsDemo> logger,
        ISerialPortService serialPortService,
        IMenuService menuService,
        IPerformanceMonitor performanceMonitor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serialPortService = serialPortService ?? throw new ArgumentNullException(nameof(serialPortService));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
    }

    /// <summary>
    /// 运行基础操作演示
    /// </summary>
    /// <returns>是否继续运行程序</returns>
    public async Task<bool> RunAsync()
    {
        _menuService.ClearScreen();
        
        Console.ForegroundColor = ConsoleColor.Green;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    基础操作演示                              ║");
        Console.WriteLine("║  演示内容：设备发现、连接建立、参数配置、基本数据传输        ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();

        try
        {
            // 1. 演示设备发现功能
            await DemonstrateDeviceDiscoveryAsync();

            // 2. 演示连接建立和参数配置
            var connected = await DemonstrateConnectionAsync();
            if (!connected)
            {
                _menuService.ShowStatus("未能建立连接，演示结束", StatusType.Warning);
                return true;
            }

            // 3. 演示基本数据传输
            await DemonstrateBasicDataTransferAsync();

            // 4. 演示连接状态监控
            await DemonstrateConnectionMonitoringAsync();

            // 5. 演示缓冲区管理
            await DemonstrateBufferManagementAsync();

            _menuService.ShowStatus("基础操作演示完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "基础操作演示过程中发生错误");
            _menuService.ShowStatus($"演示失败: {ex.Message}", StatusType.Error);
        }
        finally
        {
            // 确保断开连接
            if (_serialPortService.IsConnected)
            {
                await _serialPortService.DisconnectAsync();
                _menuService.ShowStatus("已断开串口连接", StatusType.Info);
            }
        }

        return true;
    }

    /// <summary>
    /// 演示设备发现功能
    /// </summary>
    private async Task DemonstrateDeviceDiscoveryAsync()
    {
        _menuService.ShowStatus("正在演示设备发现功能...", StatusType.Info);
        
        var operationId = _performanceMonitor.StartOperation("设备发现");
        
        try
        {
            // 获取可用串口列表
            var ports = await _serialPortService.GetAvailablePortsAsync();
            var portList = ports.ToList();

            Console.WriteLine($"\n发现 {portList.Count} 个串口设备:");
            
            if (portList.Any())
            {
                foreach (var port in portList)
                {
                    Console.WriteLine($"  • {port.PortName} - {port.Description}");
                    if (!string.IsNullOrEmpty(port.Manufacturer))
                    {
                        Console.WriteLine($"    制造商: {port.Manufacturer}");
                    }
                    if (!string.IsNullOrEmpty(port.DeviceInstanceId))
                    {
                        Console.WriteLine($"    设备ID: {port.DeviceInstanceId}");
                    }
                }
            }
            else
            {
                Console.WriteLine("  未发现任何串口设备");
                Console.WriteLine("  提示: 请确保有串口设备连接到计算机");
            }

            _performanceMonitor.EndOperation(operationId, true);
            _menuService.ShowStatus("设备发现完成", StatusType.Success);
        }
        catch (Exception ex)
        {
            _performanceMonitor.EndOperation(operationId, false);
            _logger.LogError(ex, "设备发现失败");
            throw;
        }

        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示连接建立和参数配置
    /// </summary>
    private async Task<bool> DemonstrateConnectionAsync()
    {
        _menuService.ShowStatus("正在演示连接建立和参数配置...", StatusType.Info);

        try
        {
            // 获取可用串口
            var ports = await _serialPortService.GetAvailablePortsAsync();
            var portList = ports.ToList();

            if (!portList.Any())
            {
                _menuService.ShowStatus("未发现可用的串口设备", StatusType.Warning);
                return false;
            }

            // 让用户选择串口
            var portNames = portList.Select(p => p.PortName).ToList();
            var selectedIndex = await _menuService.ShowPortSelectionMenuAsync(portNames);
            
            if (selectedIndex == -1)
            {
                return false; // 用户取消
            }

            var selectedPort = portList[selectedIndex];
            
            // 配置串口参数
            var settings = await ConfigureSerialPortSettingsAsync();
            
            // 建立连接
            var operationId = _performanceMonitor.StartOperation("建立连接");
            
            Console.WriteLine($"\n正在连接到 {selectedPort.PortName}...");
            var connected = await _serialPortService.ConnectAsync(selectedPort, settings);
            
            if (connected)
            {
                _performanceMonitor.EndOperation(operationId, true);
                _performanceMonitor.RecordConnectionEvent(ConnectionEventType.Connected, selectedPort.PortName);
                
                _menuService.ShowStatus($"成功连接到 {selectedPort.PortName}", StatusType.Success);
                
                // 显示连接信息
                DisplayConnectionInfo(selectedPort, settings);
                
                return true;
            }
            else
            {
                _performanceMonitor.EndOperation(operationId, false);
                _performanceMonitor.RecordConnectionEvent(ConnectionEventType.ConnectionFailed, selectedPort.PortName);
                
                _menuService.ShowStatus($"连接到 {selectedPort.PortName} 失败", StatusType.Error);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "建立连接时发生错误");
            _menuService.ShowStatus($"连接失败: {ex.Message}", StatusType.Error);
            return false;
        }
    }

    /// <summary>
    /// 配置串口参数
    /// </summary>
    private async Task<SerialPortSettings> ConfigureSerialPortSettingsAsync()
    {
        Console.WriteLine("\n配置串口参数:");
        
        var settings = SerialPortSettings.Default;
        
        // 配置波特率
        var baudRate = await _menuService.ShowNumberInputDialogAsync(
            "波特率", 9600, 300, 921600);
        settings.BaudRate = baudRate;
        
        // 配置数据位
        var dataBits = await _menuService.ShowNumberInputDialogAsync(
            "数据位", 8, 5, 8);
        settings.DataBits = dataBits;
        
        // 其他参数使用默认值
        Console.WriteLine($"使用默认配置: 停止位={settings.StopBits}, 校验位={settings.Parity}, 流控制={settings.Handshake}");
        
        return settings;
    }

    /// <summary>
    /// 显示连接信息
    /// </summary>
    private void DisplayConnectionInfo(SerialPortInfo port, SerialPortSettings settings)
    {
        Console.WriteLine("\n连接信息:");
        Console.WriteLine($"  串口: {port.PortName}");
        Console.WriteLine($"  描述: {port.Description}");
        Console.WriteLine($"  波特率: {settings.BaudRate}");
        Console.WriteLine($"  数据位: {settings.DataBits}");
        Console.WriteLine($"  停止位: {settings.StopBits}");
        Console.WriteLine($"  校验位: {settings.Parity}");
        Console.WriteLine($"  流控制: {settings.Handshake}");
        Console.WriteLine($"  连接状态: {_serialPortService.Status}");
    }

    /// <summary>
    /// 演示基本数据传输
    /// </summary>
    private async Task DemonstrateBasicDataTransferAsync()
    {
        _menuService.ShowStatus("正在演示基本数据传输...", StatusType.Info);

        // 注册数据接收事件
        _serialPortService.DataReceived += OnDataReceived;

        try
        {
            // 演示字符串发送
            await DemonstrateStringSendAsync();
            
            // 演示字节数组发送
            await DemonstrateByteSendAsync();
            
            // 演示发送并等待响应
            await DemonstrateSendAndReceiveAsync();
        }
        finally
        {
            // 取消注册事件
            _serialPortService.DataReceived -= OnDataReceived;
        }

        _menuService.ShowStatus("基本数据传输演示完成", StatusType.Success);
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示字符串发送
    /// </summary>
    private async Task DemonstrateStringSendAsync()
    {
        Console.WriteLine("\n--- 字符串发送演示 ---");
        
        var message = "Hello, Serial Port!";
        Console.WriteLine($"发送字符串: {message}");
        
        var operationId = _performanceMonitor.StartOperation("发送字符串");
        
        try
        {
            await _serialPortService.SendAsync(message);
            _performanceMonitor.RecordDataTransfer(Encoding.UTF8.GetByteCount(message), TransferDirection.Send);
            _performanceMonitor.EndOperation(operationId, true);
            
            _menuService.ShowStatus("字符串发送成功", StatusType.Success);
        }
        catch (Exception ex)
        {
            _performanceMonitor.EndOperation(operationId, false);
            _logger.LogError(ex, "发送字符串失败");
            _menuService.ShowStatus($"发送失败: {ex.Message}", StatusType.Error);
        }
        
        await Task.Delay(1000); // 等待可能的响应
    }

    /// <summary>
    /// 演示字节数组发送
    /// </summary>
    private async Task DemonstrateByteSendAsync()
    {
        Console.WriteLine("\n--- 字节数组发送演示 ---");
        
        var data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05 };
        Console.WriteLine($"发送字节数组: {Convert.ToHexString(data)}");
        
        var operationId = _performanceMonitor.StartOperation("发送字节数组");
        
        try
        {
            await _serialPortService.SendAsync(data);
            _performanceMonitor.RecordDataTransfer(data.Length, TransferDirection.Send);
            _performanceMonitor.EndOperation(operationId, true);
            
            _menuService.ShowStatus("字节数组发送成功", StatusType.Success);
        }
        catch (Exception ex)
        {
            _performanceMonitor.EndOperation(operationId, false);
            _logger.LogError(ex, "发送字节数组失败");
            _menuService.ShowStatus($"发送失败: {ex.Message}", StatusType.Error);
        }
        
        await Task.Delay(1000); // 等待可能的响应
    }

    /// <summary>
    /// 演示发送并等待响应
    /// </summary>
    private async Task DemonstrateSendAndReceiveAsync()
    {
        Console.WriteLine("\n--- 发送并等待响应演示 ---");
        
        var query = "AT";
        Console.WriteLine($"发送查询: {query}");
        Console.WriteLine("等待响应 (超时: 5秒)...");
        
        var operationId = _performanceMonitor.StartOperation("发送并等待响应");
        
        try
        {
            var response = await _serialPortService.SendAndReceiveAsync(query, TimeSpan.FromSeconds(5));
            _performanceMonitor.RecordDataTransfer(Encoding.UTF8.GetByteCount(query), TransferDirection.Send);
            _performanceMonitor.RecordDataTransfer(Encoding.UTF8.GetByteCount(response), TransferDirection.Receive);
            _performanceMonitor.EndOperation(operationId, true);
            
            Console.WriteLine($"收到响应: {response}");
            _menuService.ShowStatus("发送并接收成功", StatusType.Success);
        }
        catch (TimeoutException)
        {
            _performanceMonitor.EndOperation(operationId, false);
            _menuService.ShowStatus("等待响应超时", StatusType.Warning);
        }
        catch (Exception ex)
        {
            _performanceMonitor.EndOperation(operationId, false);
            _logger.LogError(ex, "发送并等待响应失败");
            _menuService.ShowStatus($"操作失败: {ex.Message}", StatusType.Error);
        }
    }

    /// <summary>
    /// 演示连接状态监控
    /// </summary>
    private async Task DemonstrateConnectionMonitoringAsync()
    {
        _menuService.ShowStatus("正在演示连接状态监控...", StatusType.Info);
        
        Console.WriteLine("\n--- 连接状态监控演示 ---");
        Console.WriteLine($"当前连接状态: {_serialPortService.Status}");
        Console.WriteLine($"是否已连接: {_serialPortService.IsConnected}");
        Console.WriteLine($"自动重连: {(_serialPortService.AutoReconnectEnabled ? "启用" : "禁用")}");
        
        if (_serialPortService.CurrentPort != null)
        {
            Console.WriteLine($"当前端口: {_serialPortService.CurrentPort.PortName}");
        }
        
        if (_serialPortService.Settings != null)
        {
            Console.WriteLine($"当前设置: {_serialPortService.Settings}");
        }
        
        _menuService.ShowStatus("连接状态监控演示完成", StatusType.Success);
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 演示缓冲区管理
    /// </summary>
    private async Task DemonstrateBufferManagementAsync()
    {
        _menuService.ShowStatus("正在演示缓冲区管理...", StatusType.Info);
        
        Console.WriteLine("\n--- 缓冲区管理演示 ---");
        Console.WriteLine($"接收缓冲区字节数: {_serialPortService.BytesToRead}");
        Console.WriteLine($"发送缓冲区字节数: {_serialPortService.BytesToWrite}");
        
        // 清空缓冲区
        Console.WriteLine("清空接收缓冲区...");
        _serialPortService.ClearReceiveBuffer();
        
        Console.WriteLine("清空发送缓冲区...");
        _serialPortService.ClearSendBuffer();
        
        Console.WriteLine($"清空后 - 接收缓冲区字节数: {_serialPortService.BytesToRead}");
        Console.WriteLine($"清空后 - 发送缓冲区字节数: {_serialPortService.BytesToWrite}");
        
        _menuService.ShowStatus("缓冲区管理演示完成", StatusType.Success);
        await _menuService.WaitForKeyAsync();
    }

    /// <summary>
    /// 数据接收事件处理
    /// </summary>
    private void OnDataReceived(object? sender, Liam.SerialPort.Events.DataReceivedEventArgs e)
    {
        Console.WriteLine($"\n[数据接收] 收到 {e.Data.Length} 字节数据:");
        Console.WriteLine($"  字符串: {Encoding.UTF8.GetString(e.Data)}");
        Console.WriteLine($"  十六进制: {Convert.ToHexString(e.Data)}");
        
        _performanceMonitor.RecordDataTransfer(e.Data.Length, TransferDirection.Receive);
    }
}
