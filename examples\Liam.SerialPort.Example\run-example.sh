#!/bin/bash

echo "========================================"
echo "Liam.SerialPort 示例程序启动脚本"
echo "========================================"
echo

echo "正在检查 .NET 8.0 环境..."
if ! command -v dotnet &> /dev/null; then
    echo "错误: 未找到 .NET SDK，请先安装 .NET 8.0 SDK"
    echo "下载地址: https://dotnet.microsoft.com/download/dotnet/8.0"
    exit 1
fi

echo ".NET 环境检查通过"
echo

echo "正在还原依赖包..."
if ! dotnet restore; then
    echo "错误: 依赖包还原失败"
    exit 1
fi

echo
echo "正在构建项目..."
if ! dotnet build; then
    echo "错误: 项目构建失败"
    exit 1
fi

echo
echo "构建成功，正在启动示例程序..."
echo
echo "========================================"
echo "提示: "
echo "- 程序支持多种演示功能"
echo "- 按照菜单提示进行操作"
echo "- 按 Ctrl+C 可随时退出程序"
echo "========================================"
echo

dotnet run

echo
echo "程序已退出"
