using Liam.Logging.Interfaces;
using Microsoft.Extensions.Configuration;

namespace Liam.Logging.Example.Services;

/// <summary>
/// 示例服务实现
/// 提供交互式菜单和功能演示的主要入口
/// </summary>
public class ExampleService : IExampleService
{
    private readonly ILiamLogger _logger;
    private readonly IConfiguration _configuration;
    private readonly ILoggingDemoService _loggingDemo;
    private readonly IPerformanceTestService _performanceTest;
    private readonly IConfigurationDemoService _configurationDemo;
    private readonly IAsyncLoggingDemoService _asyncLoggingDemo;
    private readonly IStructuredLoggingDemoService _structuredLoggingDemo;
    private readonly IScopeLoggingDemoService _scopeLoggingDemo;
    private readonly IFilteringDemoService _filteringDemo;
    private readonly IExceptionHandlingDemoService _exceptionHandlingDemo;

    /// <summary>
    /// 初始化示例服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">配置服务</param>
    /// <param name="loggingDemo">日志演示服务</param>
    /// <param name="performanceTest">性能测试服务</param>
    /// <param name="configurationDemo">配置演示服务</param>
    /// <param name="asyncLoggingDemo">异步日志演示服务</param>
    /// <param name="structuredLoggingDemo">结构化日志演示服务</param>
    /// <param name="scopeLoggingDemo">作用域日志演示服务</param>
    /// <param name="filteringDemo">过滤演示服务</param>
    /// <param name="exceptionHandlingDemo">异常处理演示服务</param>
    public ExampleService(
        ILiamLogger logger,
        IConfiguration configuration,
        ILoggingDemoService loggingDemo,
        IPerformanceTestService performanceTest,
        IConfigurationDemoService configurationDemo,
        IAsyncLoggingDemoService asyncLoggingDemo,
        IStructuredLoggingDemoService structuredLoggingDemo,
        IScopeLoggingDemoService scopeLoggingDemo,
        IFilteringDemoService filteringDemo,
        IExceptionHandlingDemoService exceptionHandlingDemo)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _loggingDemo = loggingDemo ?? throw new ArgumentNullException(nameof(loggingDemo));
        _performanceTest = performanceTest ?? throw new ArgumentNullException(nameof(performanceTest));
        _configurationDemo = configurationDemo ?? throw new ArgumentNullException(nameof(configurationDemo));
        _asyncLoggingDemo = asyncLoggingDemo ?? throw new ArgumentNullException(nameof(asyncLoggingDemo));
        _structuredLoggingDemo = structuredLoggingDemo ?? throw new ArgumentNullException(nameof(structuredLoggingDemo));
        _scopeLoggingDemo = scopeLoggingDemo ?? throw new ArgumentNullException(nameof(scopeLoggingDemo));
        _filteringDemo = filteringDemo ?? throw new ArgumentNullException(nameof(filteringDemo));
        _exceptionHandlingDemo = exceptionHandlingDemo ?? throw new ArgumentNullException(nameof(exceptionHandlingDemo));
    }

    /// <summary>
    /// 运行示例程序
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task RunAsync()
    {
        _logger.LogInformation("=== Liam.Logging 示例程序启动 ===");
        
        // 显示欢迎信息
        ShowWelcomeMessage();
        
        // 显示当前环境信息
        ShowEnvironmentInfo();

        // 主菜单循环
        bool continueRunning = true;
        while (continueRunning)
        {
            try
            {
                ShowMainMenu();
                var choice = Console.ReadLine()?.Trim();

                switch (choice)
                {
                    case "1":
                        await _loggingDemo.DemonstrateLogLevelsAsync();
                        break;
                    case "2":
                        await _structuredLoggingDemo.DemonstrateStructuredLoggingAsync();
                        break;
                    case "3":
                        await _asyncLoggingDemo.DemonstrateAsyncLoggingAsync();
                        break;
                    case "4":
                        await _scopeLoggingDemo.DemonstrateScopeLoggingAsync();
                        break;
                    case "5":
                        await _filteringDemo.DemonstrateFilteringAsync();
                        break;
                    case "6":
                        await _configurationDemo.DemonstrateConfigurationAsync();
                        break;
                    case "7":
                        await _exceptionHandlingDemo.DemonstrateExceptionHandlingAsync();
                        break;
                    case "8":
                        await _performanceTest.RunPerformanceTestAsync();
                        break;
                    case "9":
                        await RunAllDemosAsync();
                        break;
                    case "0":
                    case "q":
                    case "quit":
                    case "exit":
                        continueRunning = false;
                        break;
                    default:
                        Console.WriteLine("无效的选择，请重新输入。");
                        break;
                }

                if (continueRunning)
                {
                    Console.WriteLine("\n按任意键继续...");
                    Console.ReadKey();
                    Console.Clear();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("菜单操作出现异常", ex);
                Console.WriteLine($"操作出现异常: {ex.Message}");
                Console.WriteLine("按任意键继续...");
                Console.ReadKey();
            }
        }

        _logger.LogInformation("=== Liam.Logging 示例程序结束 ===");
    }

    /// <summary>
    /// 显示欢迎信息
    /// </summary>
    private void ShowWelcomeMessage()
    {
        Console.Clear();
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    Liam.Logging 示例程序                      ║");
        Console.WriteLine("║                                                              ║");
        Console.WriteLine("║  这个示例程序演示了 Liam.Logging 库的各种功能特性：           ║");
        Console.WriteLine("║  • 多级别日志记录 (Trace, Debug, Info, Warning, Error, Critical) ║");
        Console.WriteLine("║  • 多种输出目标 (控制台、文件、数据库等)                      ║");
        Console.WriteLine("║  • 结构化日志记录 (包含上下文信息和自定义属性)                ║");
        Console.WriteLine("║  • 异步日志记录 (高性能异步处理)                             ║");
        Console.WriteLine("║  • 日志格式化和过滤功能                                      ║");
        Console.WriteLine("║  • 日志轮转和缓冲机制                                        ║");
        Console.WriteLine("║  • 依赖注入集成                                              ║");
        Console.WriteLine("║  • 配置管理 (从appsettings.json读取配置)                     ║");
        Console.WriteLine("║  • 性能监控和线程安全                                        ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.WriteLine();
    }

    /// <summary>
    /// 显示环境信息
    /// </summary>
    private void ShowEnvironmentInfo()
    {
        var environment = _configuration["ASPNETCORE_ENVIRONMENT"] ?? "Development";
        var appName = _configuration["Logging:ApplicationName"] ?? "Unknown";
        
        Console.WriteLine($"当前环境: {environment}");
        Console.WriteLine($"应用程序: {appName}");
        Console.WriteLine($"配置文件: appsettings.{environment}.json");
        Console.WriteLine($"日志目录: logs/");
        Console.WriteLine();
        
        _logger.LogStructured(Liam.Logging.Constants.LogLevel.Information,
            "环境信息显示完成: Environment={Environment}, Application={Application}",
            environment, appName);
    }

    /// <summary>
    /// 显示主菜单
    /// </summary>
    private void ShowMainMenu()
    {
        Console.WriteLine("┌─────────────────────────────────────────────────────────────┐");
        Console.WriteLine("│                        主菜单                                │");
        Console.WriteLine("├─────────────────────────────────────────────────────────────┤");
        Console.WriteLine("│  1. 多级别日志记录演示                                       │");
        Console.WriteLine("│  2. 结构化日志记录演示                                       │");
        Console.WriteLine("│  3. 异步日志记录演示                                         │");
        Console.WriteLine("│  4. 日志作用域演示                                           │");
        Console.WriteLine("│  5. 日志过滤和格式化演示                                     │");
        Console.WriteLine("│  6. 配置管理演示                                             │");
        Console.WriteLine("│  7. 异常处理演示                                             │");
        Console.WriteLine("│  8. 性能测试                                                 │");
        Console.WriteLine("│  9. 运行所有演示                                             │");
        Console.WriteLine("│  0. 退出程序                                                 │");
        Console.WriteLine("└─────────────────────────────────────────────────────────────┘");
        Console.Write("请选择功能 (1-9, 0退出): ");
    }

    /// <summary>
    /// 运行所有演示
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task RunAllDemosAsync()
    {
        _logger.LogInformation("开始运行所有演示功能");
        
        var demos = new (string Name, Func<Task> Demo)[]
        {
            ("多级别日志记录", () => _loggingDemo.DemonstrateLogLevelsAsync()),
            ("结构化日志记录", () => _structuredLoggingDemo.DemonstrateStructuredLoggingAsync()),
            ("异步日志记录", () => _asyncLoggingDemo.DemonstrateAsyncLoggingAsync()),
            ("日志作用域", () => _scopeLoggingDemo.DemonstrateScopeLoggingAsync()),
            ("日志过滤和格式化", () => _filteringDemo.DemonstrateFilteringAsync()),
            ("配置管理", () => _configurationDemo.DemonstrateConfigurationAsync()),
            ("异常处理", () => _exceptionHandlingDemo.DemonstrateExceptionHandlingAsync()),
            ("性能测试", () => _performanceTest.ComparePerformanceAsync(1000))
        };

        for (int i = 0; i < demos.Length; i++)
        {
            var (name, demo) = demos[i];
            Console.WriteLine($"\n=== 演示 {i + 1}/{demos.Length}: {name} ===");
            
            try
            {
                await demo();
                _logger.LogInformation($"演示完成: {name}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"演示失败: {name}", ex);
                Console.WriteLine($"演示 '{name}' 出现异常: {ex.Message}");
            }
            
            Console.WriteLine($"=== 演示 {i + 1} 完成 ===\n");
            
            // 在演示之间添加短暂延迟
            await Task.Delay(1000);
        }
        
        _logger.LogInformation("所有演示功能运行完成");
        Console.WriteLine("所有演示功能已完成！");
    }
}
