using Microsoft.Extensions.Logging;

namespace Liam.SerialPort.Example.Services;

/// <summary>
/// 菜单服务实现
/// 提供控制台用户界面功能
/// </summary>
public class MenuService : IMenuService
{
    private readonly ILogger<MenuService> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public MenuService(ILogger<MenuService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 显示主菜单并获取用户选择
    /// </summary>
    /// <returns>用户选择的菜单项</returns>
    public async Task<MenuChoice> ShowMainMenuAsync()
    {
        while (true)
        {
            ClearScreen();
            
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                        主菜单                                ║");
            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
            Console.WriteLine("║  1. 基础操作演示 - 设备发现、连接、基本数据传输              ║");
            Console.WriteLine("║  2. 高级功能演示 - 热插拔、自动重连、事件处理                ║");
            Console.WriteLine("║  3. 性能测试演示 - 吞吐量测试、延迟测试、压力测试            ║");
            Console.WriteLine("║  4. 连接池演示   - 多连接管理、资源池化                      ║");
            Console.WriteLine("║  5. 数据格式演示 - 字符串、字节数组、十六进制                ║");
            Console.WriteLine("║  6. 错误处理演示 - 异常处理、错误恢复、故障转移              ║");
            Console.WriteLine("║  7. 配置管理演示 - 参数配置、依赖注入、日志集成              ║");
            Console.WriteLine("║  8. 扩展方法演示 - 十六进制发送、批量发送、校验和            ║");
            Console.WriteLine("║  9. 数据处理器演示 - 直接读取、字符串读取、监听控制          ║");
            Console.WriteLine("║ 10. 连接管理演示 - 手动重连、连接测试、统计信息              ║");
            Console.WriteLine("║ 11. 设备发现演示 - 设备监控、刷新列表、端口检查              ║");
            Console.WriteLine("║  0. 退出程序                                                  ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.ResetColor();

            Console.Write("\n请选择要演示的功能 (0-11): ");
            
            var input = Console.ReadLine();
            if (int.TryParse(input, out var choice) && Enum.IsDefined(typeof(MenuChoice), choice))
            {
                var menuChoice = (MenuChoice)choice;
                _logger.LogDebug("用户选择菜单项: {MenuChoice}", menuChoice);
                return menuChoice;
            }

            ShowStatus("无效的选择，请输入 0-11 之间的数字", StatusType.Error);
            await Task.Delay(1500);
        }
    }

    /// <summary>
    /// 显示串口选择菜单
    /// </summary>
    /// <param name="ports">可用的串口列表</param>
    /// <returns>用户选择的串口索引，-1表示取消</returns>
    public async Task<int> ShowPortSelectionMenuAsync(IEnumerable<string> ports)
    {
        var portList = ports.ToList();
        
        if (!portList.Any())
        {
            ShowStatus("未发现可用的串口设备", StatusType.Warning);
            await WaitForKeyAsync();
            return -1;
        }

        while (true)
        {
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("\n╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                      选择串口设备                            ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.ResetColor();

            for (int i = 0; i < portList.Count; i++)
            {
                Console.WriteLine($"  {i + 1}. {portList[i]}");
            }
            Console.WriteLine($"  0. 取消");

            Console.Write($"\n请选择串口 (0-{portList.Count}): ");
            
            var input = Console.ReadLine();
            if (int.TryParse(input, out var choice))
            {
                if (choice == 0)
                {
                    return -1; // 取消
                }
                
                if (choice >= 1 && choice <= portList.Count)
                {
                    return choice - 1; // 返回索引
                }
            }

            ShowStatus($"无效的选择，请输入 0-{portList.Count} 之间的数字", StatusType.Error);
            await Task.Delay(1500);
        }
    }

    /// <summary>
    /// 显示确认对话框
    /// </summary>
    /// <param name="message">确认消息</param>
    /// <returns>用户是否确认</returns>
    public async Task<bool> ShowConfirmationAsync(string message)
    {
        while (true)
        {
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.Write($"{message} (y/n): ");
            Console.ResetColor();

            var key = Console.ReadKey();
            Console.WriteLine();

            switch (key.Key)
            {
                case ConsoleKey.Y:
                    return true;
                case ConsoleKey.N:
                    return false;
                default:
                    ShowStatus("请输入 'y' 或 'n'", StatusType.Warning);
                    await Task.Delay(1000);
                    break;
            }
        }
    }

    /// <summary>
    /// 显示输入对话框
    /// </summary>
    /// <param name="prompt">输入提示</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>用户输入的值</returns>
    public async Task<string> ShowInputDialogAsync(string prompt, string? defaultValue = null)
    {
        while (true)
        {
            Console.Write($"{prompt}");
            if (!string.IsNullOrEmpty(defaultValue))
            {
                Console.Write($" (默认: {defaultValue})");
            }
            Console.Write(": ");

            var input = Console.ReadLine();
            
            if (string.IsNullOrWhiteSpace(input))
            {
                if (!string.IsNullOrEmpty(defaultValue))
                {
                    return defaultValue;
                }
                
                ShowStatus("输入不能为空", StatusType.Warning);
                await Task.Delay(1000);
                continue;
            }

            return input.Trim();
        }
    }

    /// <summary>
    /// 显示数字输入对话框
    /// </summary>
    /// <param name="prompt">输入提示</param>
    /// <param name="defaultValue">默认值</param>
    /// <param name="minValue">最小值</param>
    /// <param name="maxValue">最大值</param>
    /// <returns>用户输入的数字</returns>
    public async Task<int> ShowNumberInputDialogAsync(string prompt, int defaultValue, int minValue = int.MinValue, int maxValue = int.MaxValue)
    {
        while (true)
        {
            Console.Write($"{prompt} (默认: {defaultValue}, 范围: {minValue}-{maxValue}): ");

            var input = Console.ReadLine();
            
            if (string.IsNullOrWhiteSpace(input))
            {
                return defaultValue;
            }

            if (int.TryParse(input, out var value))
            {
                if (value >= minValue && value <= maxValue)
                {
                    return value;
                }
                
                ShowStatus($"数值必须在 {minValue} 到 {maxValue} 之间", StatusType.Warning);
            }
            else
            {
                ShowStatus("请输入有效的数字", StatusType.Warning);
            }

            await Task.Delay(1000);
        }
    }

    /// <summary>
    /// 显示进度条
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="progress">进度百分比 (0-100)</param>
    public void ShowProgress(string title, int progress)
    {
        progress = Math.Clamp(progress, 0, 100);
        
        Console.Write($"\r{title}: [");
        
        var barWidth = 40;
        var filledWidth = (int)(barWidth * progress / 100.0);
        
        Console.ForegroundColor = ConsoleColor.Green;
        Console.Write(new string('█', filledWidth));
        Console.ForegroundColor = ConsoleColor.DarkGray;
        Console.Write(new string('░', barWidth - filledWidth));
        Console.ResetColor();
        
        Console.Write($"] {progress}%");
    }

    /// <summary>
    /// 显示状态信息
    /// </summary>
    /// <param name="message">状态消息</param>
    /// <param name="type">消息类型</param>
    public void ShowStatus(string message, StatusType type = StatusType.Info)
    {
        var color = type switch
        {
            StatusType.Success => ConsoleColor.Green,
            StatusType.Warning => ConsoleColor.Yellow,
            StatusType.Error => ConsoleColor.Red,
            _ => ConsoleColor.White
        };

        var prefix = type switch
        {
            StatusType.Success => "[成功]",
            StatusType.Warning => "[警告]",
            StatusType.Error => "[错误]",
            _ => "[信息]"
        };

        Console.ForegroundColor = color;
        Console.WriteLine($"{prefix} {message}");
        Console.ResetColor();
    }

    /// <summary>
    /// 清屏
    /// </summary>
    public void ClearScreen()
    {
        Console.Clear();
    }

    /// <summary>
    /// 等待用户按键
    /// </summary>
    /// <param name="message">等待消息</param>
    /// <returns>用户按下的键</returns>
    public async Task<ConsoleKeyInfo> WaitForKeyAsync(string message = "按任意键继续...")
    {
        Console.WriteLine(message);
        return await Task.Run(() => Console.ReadKey(true));
    }
}
