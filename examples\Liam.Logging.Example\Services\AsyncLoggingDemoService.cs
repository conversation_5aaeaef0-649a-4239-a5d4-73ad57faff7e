using Liam.Logging.Constants;
using Liam.Logging.Interfaces;
using System.Diagnostics;

namespace Liam.Logging.Example.Services;

/// <summary>
/// 异步日志记录演示服务实现
/// 演示异步日志记录和缓冲机制
/// </summary>
public class AsyncLoggingDemoService : IAsyncLoggingDemoService
{
    private readonly ILiamLogger _logger;

    /// <summary>
    /// 初始化异步日志记录演示服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public AsyncLoggingDemoService(ILiamLogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 演示异步日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateAsyncLoggingAsync()
    {
        Console.WriteLine("=== 异步日志记录演示 ===\n");

        Console.WriteLine("1. 基本异步日志记录:");
        
        // 基本异步日志记录
        var tasks = new List<Task>();
        
        for (int i = 0; i < 10; i++)
        {
            var task = _logger.LogAsync(LogLevel.Information, 
                $"异步日志消息 {i}: 时间戳 {DateTime.Now:HH:mm:ss.fff}");
            tasks.Add(task);
        }
        
        await Task.WhenAll(tasks);
        Console.WriteLine("✓ 基本异步日志记录完成");

        Console.WriteLine("\n2. 高并发异步日志记录:");
        
        await DemonstrateHighConcurrencyLoggingAsync();

        Console.WriteLine("\n3. 异步结构化日志记录:");
        
        await DemonstrateAsyncStructuredLoggingAsync();

        Console.WriteLine("\n4. 异步日志与同步操作混合:");
        
        await DemonstrateMixedLoggingAsync();

        Console.WriteLine("\n5. 异步日志错误处理:");
        
        await DemonstrateAsyncErrorHandlingAsync();

        Console.WriteLine("\n=== 异步日志记录演示完成 ===");
    }

    /// <summary>
    /// 演示批处理日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DemonstrateBatchLoggingAsync()
    {
        Console.WriteLine("=== 批处理日志记录演示 ===\n");

        Console.WriteLine("1. 小批量日志记录:");
        
        await ProcessSmallBatchesAsync();

        Console.WriteLine("\n2. 大批量日志记录:");
        
        await ProcessLargeBatchesAsync();

        Console.WriteLine("\n3. 动态批量大小:");
        
        await ProcessDynamicBatchesAsync();

        Console.WriteLine("\n4. 批量日志性能测试:");
        
        await BatchPerformanceTestAsync();

        Console.WriteLine("\n=== 批处理日志记录演示完成 ===");
    }

    /// <summary>
    /// 演示高并发异步日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateHighConcurrencyLoggingAsync()
    {
        const int threadCount = 10;
        const int messagesPerThread = 50;
        
        var stopwatch = Stopwatch.StartNew();
        
        var tasks = Enumerable.Range(0, threadCount).Select(async threadId =>
        {
            for (int i = 0; i < messagesPerThread; i++)
            {
                await _logger.LogAsync(LogLevel.Information,
                    $"高并发日志 - 线程 {threadId}, 消息 {i}, 时间 {DateTime.Now:HH:mm:ss.fff}");
                
                // 模拟一些处理时间
                await Task.Delay(Random.Shared.Next(1, 10));
            }
        });
        
        await Task.WhenAll(tasks);
        stopwatch.Stop();
        
        var totalMessages = threadCount * messagesPerThread;
        var throughput = totalMessages * 1000.0 / stopwatch.ElapsedMilliseconds;
        
        Console.WriteLine($"   线程数: {threadCount}");
        Console.WriteLine($"   每线程消息数: {messagesPerThread}");
        Console.WriteLine($"   总消息数: {totalMessages}");
        Console.WriteLine($"   耗时: {stopwatch.ElapsedMilliseconds} ms");
        Console.WriteLine($"   吞吐量: {throughput:F0} 条/秒");
        Console.WriteLine("✓ 高并发异步日志记录完成");
    }

    /// <summary>
    /// 演示异步结构化日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateAsyncStructuredLoggingAsync()
    {
        var users = new[]
        {
            new { Id = 1001, Name = "张三", Email = "<EMAIL>", Role = "Admin" },
            new { Id = 1002, Name = "李四", Email = "<EMAIL>", Role = "User" },
            new { Id = 1003, Name = "王五", Email = "<EMAIL>", Role = "Manager" },
            new { Id = 1004, Name = "赵六", Email = "<EMAIL>", Role = "User" },
            new { Id = 1005, Name = "钱七", Email = "<EMAIL>", Role = "Admin" }
        };

        var tasks = users.Select(async user =>
        {
            await _logger.LogStructuredAsync(LogLevel.Information,
                "用户操作: {UserId} {UserName} ({UserRole}) 执行了登录操作，邮箱: {Email}",
                new object[] { user.Id, user.Name, user.Role, user.Email });

            // 模拟用户操作时间
            await Task.Delay(Random.Shared.Next(50, 200));

            await _logger.LogStructuredAsync(LogLevel.Debug,
                "用户 {UserId} 会话信息更新完成",
                new object[] { user.Id });
        });
        
        await Task.WhenAll(tasks);
        Console.WriteLine("✓ 异步结构化日志记录完成");
    }

    /// <summary>
    /// 演示混合日志记录
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateMixedLoggingAsync()
    {
        // 同步日志记录
        _logger.LogInformation("开始混合日志记录演示");
        
        var tasks = new List<Task>();
        
        // 异步日志记录任务
        for (int i = 0; i < 5; i++)
        {
            var taskIndex = i;
            var task = Task.Run(async () =>
            {
                await _logger.LogAsync(LogLevel.Information, 
                    $"异步任务 {taskIndex} 开始执行");
                
                // 模拟一些工作
                await Task.Delay(Random.Shared.Next(100, 500));
                
                // 在任务中记录同步日志
                _logger.LogInformation($"异步任务 {taskIndex} 中的同步日志");
                
                await _logger.LogAsync(LogLevel.Information, 
                    $"异步任务 {taskIndex} 执行完成");
            });
            
            tasks.Add(task);
        }
        
        // 在等待异步任务的同时记录同步日志
        for (int i = 0; i < 3; i++)
        {
            _logger.LogInformation($"主线程同步日志 {i}");
            await Task.Delay(150);
        }
        
        await Task.WhenAll(tasks);
        
        // 最终同步日志
        _logger.LogInformation("混合日志记录演示完成");
        Console.WriteLine("✓ 混合日志记录完成");
    }

    /// <summary>
    /// 演示异步日志错误处理
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task DemonstrateAsyncErrorHandlingAsync()
    {
        var tasks = new List<Task>();
        
        // 正常的异步日志记录
        for (int i = 0; i < 3; i++)
        {
            var task = _logger.LogAsync(LogLevel.Information, $"正常异步日志 {i}");
            tasks.Add(task);
        }
        
        // 模拟可能出错的异步日志记录
        for (int i = 0; i < 2; i++)
        {
            var taskIndex = i;
            var task = Task.Run(async () =>
            {
                try
                {
                    await _logger.LogAsync(LogLevel.Warning, 
                        $"可能出错的异步日志 {taskIndex}");
                    
                    // 模拟一些可能失败的操作
                    if (Random.Shared.NextDouble() < 0.3) // 30% 概率失败
                    {
                        throw new InvalidOperationException($"模拟异步操作失败 {taskIndex}");
                    }
                    
                    await _logger.LogAsync(LogLevel.Information, 
                        $"异步操作 {taskIndex} 成功完成");
                }
                catch (Exception ex)
                {
                    // 在异常处理中记录异步日志
                    await _logger.LogAsync(LogLevel.Error, 
                        $"异步操作 {taskIndex} 失败: {ex.Message}", ex);
                }
            });
            
            tasks.Add(task);
        }
        
        try
        {
            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            await _logger.LogAsync(LogLevel.Error, "异步日志批处理出现异常", ex);
        }
        
        Console.WriteLine("✓ 异步日志错误处理完成");
    }

    /// <summary>
    /// 处理小批量日志
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task ProcessSmallBatchesAsync()
    {
        const int batchSize = 10;
        const int batchCount = 5;
        
        for (int batch = 0; batch < batchCount; batch++)
        {
            var batchTasks = new List<Task>();
            
            for (int i = 0; i < batchSize; i++)
            {
                var messageIndex = batch * batchSize + i;
                var task = _logger.LogAsync(LogLevel.Information,
                    $"小批量日志 - 批次 {batch}, 消息 {i}, 全局索引 {messageIndex}");
                batchTasks.Add(task);
            }
            
            await Task.WhenAll(batchTasks);
            Console.WriteLine($"   批次 {batch + 1}/{batchCount} 完成 ({batchSize} 条消息)");
            
            // 批次间短暂延迟
            await Task.Delay(100);
        }
        
        Console.WriteLine($"✓ 小批量处理完成 (总计 {batchCount * batchSize} 条消息)");
    }

    /// <summary>
    /// 处理大批量日志
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task ProcessLargeBatchesAsync()
    {
        const int batchSize = 100;
        const int batchCount = 3;
        
        var stopwatch = Stopwatch.StartNew();
        
        for (int batch = 0; batch < batchCount; batch++)
        {
            var batchStopwatch = Stopwatch.StartNew();
            var batchTasks = new List<Task>();
            
            for (int i = 0; i < batchSize; i++)
            {
                var messageIndex = batch * batchSize + i;
                var task = _logger.LogAsync(LogLevel.Information,
                    $"大批量日志 - 批次 {batch}, 消息 {i}, 全局索引 {messageIndex}, 时间戳 {DateTime.Now:HH:mm:ss.fff}");
                batchTasks.Add(task);
            }
            
            await Task.WhenAll(batchTasks);
            batchStopwatch.Stop();
            
            var batchThroughput = batchSize * 1000.0 / batchStopwatch.ElapsedMilliseconds;
            Console.WriteLine($"   批次 {batch + 1}/{batchCount} 完成 ({batchSize} 条消息, {batchStopwatch.ElapsedMilliseconds}ms, {batchThroughput:F0} 条/秒)");
        }
        
        stopwatch.Stop();
        var totalMessages = batchCount * batchSize;
        var overallThroughput = totalMessages * 1000.0 / stopwatch.ElapsedMilliseconds;
        
        Console.WriteLine($"✓ 大批量处理完成 (总计 {totalMessages} 条消息, {stopwatch.ElapsedMilliseconds}ms, {overallThroughput:F0} 条/秒)");
    }

    /// <summary>
    /// 处理动态批量日志
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task ProcessDynamicBatchesAsync()
    {
        var batchSizes = new[] { 5, 15, 25, 50, 30, 10 };
        var totalMessages = 0;
        var stopwatch = Stopwatch.StartNew();
        
        for (int batchIndex = 0; batchIndex < batchSizes.Length; batchIndex++)
        {
            var batchSize = batchSizes[batchIndex];
            var batchTasks = new List<Task>();
            
            for (int i = 0; i < batchSize; i++)
            {
                var task = _logger.LogAsync(LogLevel.Information,
                    $"动态批量日志 - 批次 {batchIndex} (大小: {batchSize}), 消息 {i}");
                batchTasks.Add(task);
            }
            
            await Task.WhenAll(batchTasks);
            totalMessages += batchSize;
            
            Console.WriteLine($"   动态批次 {batchIndex + 1}/{batchSizes.Length} 完成 (大小: {batchSize})");
            
            // 根据批次大小调整延迟
            await Task.Delay(batchSize * 2);
        }
        
        stopwatch.Stop();
        var throughput = totalMessages * 1000.0 / stopwatch.ElapsedMilliseconds;
        
        Console.WriteLine($"✓ 动态批量处理完成 (总计 {totalMessages} 条消息, {throughput:F0} 条/秒)");
    }

    /// <summary>
    /// 批量日志性能测试
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task BatchPerformanceTestAsync()
    {
        var testCases = new[]
        {
            new { Name = "小批量", BatchSize = 10, BatchCount = 20 },
            new { Name = "中批量", BatchSize = 50, BatchCount = 10 },
            new { Name = "大批量", BatchSize = 100, BatchCount = 5 }
        };

        foreach (var testCase in testCases)
        {
            var stopwatch = Stopwatch.StartNew();
            var totalMessages = 0;
            
            for (int batch = 0; batch < testCase.BatchCount; batch++)
            {
                var batchTasks = new List<Task>();
                
                for (int i = 0; i < testCase.BatchSize; i++)
                {
                    var task = _logger.LogAsync(LogLevel.Information,
                        $"{testCase.Name}性能测试 - 批次 {batch}, 消息 {i}");
                    batchTasks.Add(task);
                }
                
                await Task.WhenAll(batchTasks);
                totalMessages += testCase.BatchSize;
            }
            
            stopwatch.Stop();
            var throughput = totalMessages * 1000.0 / stopwatch.ElapsedMilliseconds;
            
            Console.WriteLine($"   {testCase.Name}: {totalMessages} 条消息, {stopwatch.ElapsedMilliseconds}ms, {throughput:F0} 条/秒");
        }
        
        Console.WriteLine("✓ 批量日志性能测试完成");
    }
}
