{"Logging": {"MinimumLevel": "Trace", "IncludeSourceInfo": true, "Environment": "Development", "GlobalProperties": {"Environment": "Development", "Debug": true}, "Providers": [{"TypeName": "ConsoleLogProvider", "Enabled": true, "MinimumLevel": "Trace", "Settings": {"EnableColors": true, "UseStandardError": false, "TimestampFormat": "HH:mm:ss.fff", "FormatterType": "Text"}}, {"TypeName": "FileLogProvider", "Enabled": true, "MinimumLevel": "Debug", "Settings": {"FilePath": "logs/dev-app.log", "EnableRotation": false, "AutoFlush": true, "FormatterType": "Json"}}]}, "ExampleSettings": {"PerformanceTestIterations": 1000, "ConcurrentThreads": 5, "EnableDetailedOutput": true, "SimulateErrors": true, "ErrorRate": 0.2}}