﻿using Liam.Cryptography.Services;
using Liam.Cryptography.Extensions;
using Liam.Cryptography.Exceptions;
using System.Diagnostics;
using System.Security.Cryptography;

namespace Liam.Cryptography.Example;

/// <summary>
/// Liam.Cryptography功能库完整示例程序
/// 全面演示所有核心功能、现代加密算法、最佳实践和性能测试
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Liam.Cryptography 功能库完整示例程序 ===");
        Console.WriteLine("本程序全面演示Liam.Cryptography库的所有核心功能");
        Console.WriteLine("包括传统算法、现代加密、流式处理、性能测试和最佳实践");
        Console.WriteLine();

        try
        {
            // 1. 对称加密演示（AES + ChaCha20-Poly1305）
            Console.WriteLine("🔐 1. 对称加密演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await SymmetricEncryptionDemo();
            Console.WriteLine();

            // 2. 非对称加密演示（RSA + ECDSA）
            Console.WriteLine("🔑 2. 非对称加密演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await AsymmetricEncryptionDemo();
            Console.WriteLine();

            // 3. 哈希算法演示（SHA-256 + Argon2）
            Console.WriteLine("🔍 3. 哈希算法演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await HashingDemo();
            Console.WriteLine();

            // 4. 数字签名演示（RSA + ECDSA）
            Console.WriteLine("✍️ 4. 数字签名演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await DigitalSignatureDemo();
            Console.WriteLine();

            // 5. 现代加密算法演示
            Console.WriteLine("🚀 5. 现代加密算法演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await ModernCryptographyDemo();
            Console.WriteLine();

            // 6. 密钥管理演示
            Console.WriteLine("🗝️ 6. 密钥管理演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await KeyManagementDemo();
            Console.WriteLine();

            // 7. 流式处理演示
            Console.WriteLine("📁 7. 流式处理演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await StreamProcessingDemo();
            Console.WriteLine();

            // 8. 扩展方法演示
            Console.WriteLine("🛠️ 8. 扩展方法演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await ExtensionMethodsDemo();
            Console.WriteLine();

            // 9. 性能测试演示
            Console.WriteLine("⚡ 9. 性能测试演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await PerformanceTestDemo();
            Console.WriteLine();

            // 10. 最佳实践演示
            Console.WriteLine("⭐ 10. 最佳实践演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await BestPracticesDemo();
            Console.WriteLine();

            Console.WriteLine("✅ 所有示例演示完成！");
            Console.WriteLine("📖 更多详细信息请参阅：https://www.nuget.org/packages/Liam.Cryptography/");
            Console.WriteLine("📚 完整API文档：https://gitee.com/liam-gitee/liam");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 程序执行出错: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
        }

        Console.WriteLine();
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }

    /// <summary>
    /// 对称加密演示（AES-256 + ChaCha20-Poly1305）
    /// </summary>
    static async Task SymmetricEncryptionDemo()
    {
        Console.WriteLine("📋 对称加密功能演示包括：");
        Console.WriteLine("   • AES-256基础加密/解密");
        Console.WriteLine("   • ChaCha20-Poly1305 AEAD加密");
        Console.WriteLine("   • 异步加密/解密操作");
        Console.WriteLine("   • 异常处理最佳实践");
        Console.WriteLine();

        // 1. AES-256基础演示
        Console.WriteLine("🔐 基础AES-256加密演示");
        Console.WriteLine("─────────────────────────");
        try
        {
            var aes = new AesSymmetricCrypto();
            var key = aes.GenerateKey(256);
            var iv = aes.GenerateIV();

            Console.WriteLine("✓ 创建AES加密服务实例");
            Console.WriteLine($"✓ 生成AES-256密钥 (长度: {key.Length * 8}位)");
            Console.WriteLine($"✓ 生成初始化向量 (长度: {iv.Length * 8}位)");

            var originalText = "这是一段需要加密保护的重要数据！🔒";
            Console.WriteLine($"📝 原始数据: {originalText}");

            // 同步加密
            var encrypted = aes.Encrypt(originalText, key, iv);
            Console.WriteLine($"🔐 加密完成，密文长度: {encrypted.Length} 字节");

            // 同步解密
            var decrypted = aes.Decrypt(encrypted, key, iv);
            Console.WriteLine($"🔓 解密完成: {decrypted}");

            // 验证数据完整性
            var isValid = originalText == decrypted;
            Console.WriteLine($"✅ 数据完整性验证: {(isValid ? "通过" : "失败")}");

            // 异步操作演示
            Console.WriteLine("\n🔄 异步加密/解密演示");
            var encryptedAsync = await aes.EncryptAsync(originalText, key, iv);
            var decryptedAsync = await aes.DecryptAsync(encryptedAsync, key, iv);
            var asyncValid = originalText == decryptedAsync;
            Console.WriteLine($"✅ 异步操作验证: {(asyncValid ? "通过" : "失败")}");

            // 安全清理敏感数据
            if (key != null) Array.Clear(key, 0, key.Length);
            if (iv != null) Array.Clear(iv, 0, iv.Length);
            Console.WriteLine("🧹 敏感数据已安全清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ AES加密演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 2. ChaCha20-Poly1305 AEAD演示
        Console.WriteLine("🚀 ChaCha20-Poly1305 AEAD加密演示");
        Console.WriteLine("─────────────────────────────────");
        try
        {
            var chacha = new ChaCha20Poly1305Crypto();
            var chachaKey = chacha.GenerateKey();

            Console.WriteLine("✓ 创建ChaCha20-Poly1305加密服务");
            Console.WriteLine($"✓ 生成ChaCha20密钥 (长度: {chachaKey.Length * 8}位)");

            var testData = "ChaCha20-Poly1305认证加密测试数据 🔐";
            Console.WriteLine($"📝 测试数据: {testData}");

            // AEAD加密（包含认证）
            var chachaEncrypted = chacha.Encrypt(testData, chachaKey);
            Console.WriteLine($"🔐 AEAD加密完成，密文长度: {chachaEncrypted.Length} 字节");

            // AEAD解密（验证认证）
            var chachaDecrypted = chacha.Decrypt(chachaEncrypted, chachaKey);
            Console.WriteLine($"🔓 AEAD解密完成: {chachaDecrypted}");

            // 验证数据完整性和认证
            var chachaValid = testData == chachaDecrypted;
            Console.WriteLine($"✅ 数据完整性和认证验证: {(chachaValid ? "通过" : "失败")}");

            // 异步AEAD操作
            Console.WriteLine("\n🔄 异步AEAD操作演示");
            var chachaAsyncEncrypted = await chacha.EncryptAsync(testData, chachaKey);
            var chachaAsyncDecrypted = await chacha.DecryptAsync(chachaAsyncEncrypted, chachaKey);
            var chachaAsyncValid = testData == chachaAsyncDecrypted;
            Console.WriteLine($"✅ 异步AEAD验证: {(chachaAsyncValid ? "通过" : "失败")}");

            // 清理
            if (chachaKey != null) Array.Clear(chachaKey, 0, chachaKey.Length);
            Console.WriteLine("🧹 ChaCha20密钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ ChaCha20-Poly1305演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 非对称加密演示（RSA + ECDSA密钥生成）
    /// </summary>
    static async Task AsymmetricEncryptionDemo()
    {
        Console.WriteLine("📋 非对称加密功能演示包括：");
        Console.WriteLine("   • RSA-2048/4096密钥生成和加密");
        Console.WriteLine("   • ECDSA P-256密钥生成");
        Console.WriteLine("   • 公钥加密/私钥解密流程");
        Console.WriteLine("   • 异步非对称加密操作");
        Console.WriteLine();

        // 1. RSA-2048演示
        Console.WriteLine("🔐 RSA-2048非对称加密演示");
        Console.WriteLine("─────────────────────────────");
        try
        {
            var rsa = new RsaAsymmetricCrypto();
            var keyPair2048 = rsa.GenerateKeyPair(2048);

            Console.WriteLine("✓ 创建RSA加密服务实例");
            Console.WriteLine($"✓ 生成RSA-2048密钥对");
            Console.WriteLine($"  • 公钥长度: {keyPair2048.PublicKey?.Length} 字节");
            Console.WriteLine($"  • 私钥长度: {keyPair2048.PrivateKey?.Length} 字节");

            var originalText = "RSA非对称加密保护的机密数据 🔐";
            Console.WriteLine($"📝 原始数据: {originalText}");

            // 公钥加密
            var encrypted = rsa.EncryptWithPublicKey(originalText, keyPair2048.PublicKey!);
            Console.WriteLine($"🔐 公钥加密完成，密文长度: {encrypted.Length} 字节");

            // 私钥解密
            var decrypted = rsa.DecryptWithPrivateKey(encrypted, keyPair2048.PrivateKey!);
            Console.WriteLine($"🔓 私钥解密完成: {decrypted}");

            // 验证数据完整性
            var isValid = originalText == decrypted;
            Console.WriteLine($"✅ 数据完整性验证: {(isValid ? "通过" : "失败")}");

            // 异步操作演示
            Console.WriteLine("\n🔄 异步RSA操作演示");
            var encryptedAsync = await rsa.EncryptWithPublicKeyAsync(originalText, keyPair2048.PublicKey!);
            var decryptedAsync = await rsa.DecryptWithPrivateKeyAsync(encryptedAsync, keyPair2048.PrivateKey!);
            var asyncValid = originalText == decryptedAsync;
            Console.WriteLine($"✅ 异步操作验证: {(asyncValid ? "通过" : "失败")}");

            // 清理RSA密钥
            keyPair2048.ClearPrivateKey();
            Console.WriteLine("🧹 RSA-2048私钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ RSA-2048演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 2. RSA-4096高强度演示
        Console.WriteLine("🛡️ RSA-4096高强度加密演示");
        Console.WriteLine("─────────────────────────────");
        try
        {
            var rsa = new RsaAsymmetricCrypto();
            var keyPair4096 = rsa.GenerateKeyPair(4096);

            Console.WriteLine("✓ 生成RSA-4096高强度密钥对");
            Console.WriteLine($"  • 公钥长度: {keyPair4096.PublicKey?.Length} 字节");
            Console.WriteLine($"  • 私钥长度: {keyPair4096.PrivateKey?.Length} 字节");

            var sensitiveData = "高度机密的企业数据 🏢🔒";
            Console.WriteLine($"📝 敏感数据: {sensitiveData}");

            // 高强度加密
            var highSecurityEncrypted = rsa.EncryptWithPublicKey(sensitiveData, keyPair4096.PublicKey!);
            Console.WriteLine($"🔐 高强度加密完成，密文长度: {highSecurityEncrypted.Length} 字节");

            // 高强度解密
            var highSecurityDecrypted = rsa.DecryptWithPrivateKey(highSecurityEncrypted, keyPair4096.PrivateKey!);
            Console.WriteLine($"🔓 高强度解密完成: {highSecurityDecrypted}");

            var highSecurityValid = sensitiveData == highSecurityDecrypted;
            Console.WriteLine($"✅ 高强度验证: {(highSecurityValid ? "通过" : "失败")}");

            // 清理高强度密钥
            keyPair4096.ClearPrivateKey();
            Console.WriteLine("🧹 RSA-4096私钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ RSA-4096演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 3. ECDSA密钥生成演示
        Console.WriteLine("🌐 ECDSA P-256密钥生成演示");
        Console.WriteLine("─────────────────────────────");
        try
        {
            var ecdsa = new Ed25519DigitalSignature();
            var ecdsaKeyPair = ecdsa.GenerateKeyPair();

            Console.WriteLine("✓ 创建ECDSA数字签名服务");
            Console.WriteLine("✓ 生成ECDSA P-256密钥对");
            Console.WriteLine($"  • 公钥长度: {ecdsaKeyPair.PublicKey?.Length} 字节");
            Console.WriteLine($"  • 私钥长度: {ecdsaKeyPair.PrivateKey?.Length} 字节");
            Console.WriteLine("  • 椭圆曲线: NIST P-256");
            Console.WriteLine("  • 优势: 更小的密钥尺寸，更快的运算速度");

            // 清理ECDSA密钥
            ecdsaKeyPair.ClearPrivateKey();
            Console.WriteLine("🧹 ECDSA私钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ ECDSA演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 哈希算法演示（SHA-256 + Argon2 + 文件哈希）
    /// </summary>
    static async Task HashingDemo()
    {
        Console.WriteLine("📋 哈希算法功能演示包括：");
        Console.WriteLine("   • SHA-256数据完整性验证");
        Console.WriteLine("   • Argon2密码安全哈希");
        Console.WriteLine("   • 文件哈希计算和验证");
        Console.WriteLine("   • 异步哈希计算操作");
        Console.WriteLine();

        // 1. SHA-256基础演示
        Console.WriteLine("🔍 SHA-256哈希算法演示");
        Console.WriteLine("─────────────────────────");
        try
        {
            var hashProvider = new Sha256HashProvider();
            Console.WriteLine("✓ 创建SHA-256哈希提供者");

            var testData = "这是需要验证完整性的重要数据 📄";
            Console.WriteLine($"📝 测试数据: {testData}");

            // 计算哈希
            var hash = hashProvider.ComputeHash(testData);
            Console.WriteLine($"🔍 SHA-256哈希: {hash}");

            // 验证哈希
            var isValid = hashProvider.VerifyHash(testData, hash);
            Console.WriteLine($"✅ 哈希验证: {(isValid ? "通过" : "失败")}");

            // 篡改检测演示
            var tamperedData = testData + " [已篡改]";
            var tamperedValid = hashProvider.VerifyHash(tamperedData, hash);
            Console.WriteLine($"🚨 篡改检测: {(!tamperedValid ? "✓ 检测到篡改" : "✗ 未检测到篡改")}");

            // 异步哈希计算
            Console.WriteLine("\n🔄 异步SHA-256计算演示");
            var asyncHash = await hashProvider.ComputeHashAsync(testData);
            var asyncValid = hash == asyncHash;
            Console.WriteLine($"✅ 异步哈希验证: {(asyncValid ? "通过" : "失败")}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ SHA-256演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 2. Argon2密码哈希演示
        Console.WriteLine("🔐 Argon2密码哈希演示");
        Console.WriteLine("─────────────────────────");
        try
        {
            var passwordHasher = new Argon2PasswordHasher();
            var password = "MySecurePassword123!@#";
            Console.WriteLine($"📝 测试密码: {password}");

            // 基础Argon2哈希
            var passwordHashResult = passwordHasher.HashPassword(password);
            Console.WriteLine($"🔐 Argon2哈希: {passwordHashResult.FormattedHash[..50]}...");
            Console.WriteLine($"🧂 盐值长度: {passwordHashResult.Salt.Length} 字节");
            Console.WriteLine($"⚙️ 迭代次数: {passwordHashResult.Options.Iterations}");
            Console.WriteLine($"💾 内存使用: {passwordHashResult.Options.MemorySize} KB");

            // 密码验证
            var passwordValid = passwordHasher.VerifyPassword(password, passwordHashResult);
            Console.WriteLine($"✅ 密码验证: {(passwordValid ? "通过" : "失败")}");

            // 错误密码验证
            var wrongPassword = "WrongPassword123!";
            var wrongPasswordValid = passwordHasher.VerifyPassword(wrongPassword, passwordHashResult);
            Console.WriteLine($"🚨 错误密码检测: {(!wrongPasswordValid ? "✓ 正确拒绝" : "✗ 错误接受")}");

            // 自定义Argon2配置
            Console.WriteLine("\n⚙️ 自定义Argon2配置演示");
            var customOptions = new Argon2PasswordHasher.Argon2Options
            {
                Iterations = 6,
                MemorySize = 131072, // 128 MB
                DegreeOfParallelism = 2,
                HashSize = 64 // 512 bits
            };

            var customHasher = new Argon2PasswordHasher(customOptions);
            var customHashResult = customHasher.HashPassword(password, customOptions);
            Console.WriteLine($"🔐 高强度Argon2哈希: {customHashResult.FormattedHash[..50]}...");
            Console.WriteLine($"⚙️ 自定义配置 - 迭代: {customHashResult.Options.Iterations}, 内存: {customHashResult.Options.MemorySize} KB");

            // 异步Argon2哈希
            Console.WriteLine("\n🔄 异步Argon2计算演示");
            var asyncPasswordHash = await passwordHasher.HashPasswordAsync(password);
            var asyncPasswordValid = passwordHasher.VerifyPassword(password, asyncPasswordHash);
            Console.WriteLine($"✅ 异步密码哈希验证: {(asyncPasswordValid ? "通过" : "失败")}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Argon2演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 3. 文件哈希演示
        Console.WriteLine("📁 文件哈希计算演示");
        Console.WriteLine("─────────────────────────");
        try
        {
            // 创建临时测试文件
            var tempFile = Path.GetTempFileName();
            var fileContent = "这是一个用于测试文件哈希计算的临时文件内容。\n包含多行数据用于验证文件完整性。\n文件哈希可以确保文件未被篡改。";
            await File.WriteAllTextAsync(tempFile, fileContent);

            Console.WriteLine($"✓ 创建临时测试文件: {Path.GetFileName(tempFile)}");
            Console.WriteLine($"📝 文件内容长度: {fileContent.Length} 字符");

            var hashProvider = new Sha256HashProvider();

            // 计算文件哈希
            var fileHash = hashProvider.ComputeFileHash(tempFile);
            Console.WriteLine($"🔍 文件SHA-256哈希: {fileHash}");

            // 验证文件哈希（重新计算对比）
            var verifyFileHash = hashProvider.ComputeFileHash(tempFile);
            var fileHashValid = fileHash == verifyFileHash;
            Console.WriteLine($"✅ 文件哈希验证: {(fileHashValid ? "通过" : "失败")}");

            // 异步文件哈希
            Console.WriteLine("\n🔄 异步文件哈希计算");
            var asyncFileHash = await hashProvider.ComputeFileHashAsync(tempFile);
            var asyncFileValid = fileHash == asyncFileHash;
            Console.WriteLine($"✅ 异步文件哈希验证: {(asyncFileValid ? "通过" : "失败")}");

            // 清理临时文件
            File.Delete(tempFile);
            Console.WriteLine("🧹 临时文件已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 文件哈希演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 数字签名演示（RSA-PSS + ECDSA）
    /// </summary>
    static async Task DigitalSignatureDemo()
    {
        Console.WriteLine("📋 数字签名功能演示包括：");
        Console.WriteLine("   • RSA-PSS数字签名和验证");
        Console.WriteLine("   • ECDSA数字签名和验证");
        Console.WriteLine("   • 批量签名验证");
        Console.WriteLine("   • 篡改检测和防伪验证");
        Console.WriteLine();

        // 1. RSA数字签名演示
        Console.WriteLine("✍️ RSA-PSS数字签名演示");
        Console.WriteLine("─────────────────────────");
        try
        {
            var rsaSignature = new RsaDigitalSignature();
            var rsaCrypto = new RsaAsymmetricCrypto();
            var rsaKeyPair = rsaCrypto.GenerateKeyPair(2048);

            Console.WriteLine("✓ 创建RSA数字签名服务");
            Console.WriteLine("✓ 生成RSA-2048密钥对用于签名");

            var document = "重要合同文档内容，需要数字签名保护其完整性和真实性 📄";
            Console.WriteLine($"📄 文档内容: {document}");

            // 创建RSA签名
            var rsaSignatureBytes = rsaSignature.Sign(document, rsaKeyPair.PrivateKey!);
            Console.WriteLine($"✍️ RSA数字签名创建完成，签名长度: {rsaSignatureBytes.Length} 字节");

            // 验证RSA签名
            var rsaValid = rsaSignature.Verify(document, rsaSignatureBytes, rsaKeyPair.PublicKey!);
            Console.WriteLine($"✅ RSA签名验证: {(rsaValid ? "通过" : "失败")}");

            // RSA篡改检测
            var tamperedDocument = document + " [已被恶意篡改]";
            var rsaTamperedValid = rsaSignature.Verify(tamperedDocument, rsaSignatureBytes, rsaKeyPair.PublicKey!);
            Console.WriteLine($"🚨 RSA篡改检测: {(!rsaTamperedValid ? "✓ 检测到篡改" : "✗ 未检测到篡改")}");

            // 异步RSA签名
            Console.WriteLine("\n🔄 异步RSA签名演示");
            var asyncRsaSignature = await rsaSignature.SignAsync(document, rsaKeyPair.PrivateKey!);
            var asyncRsaValid = await rsaSignature.VerifyAsync(document, asyncRsaSignature, rsaKeyPair.PublicKey!);
            Console.WriteLine($"✅ 异步RSA签名验证: {(asyncRsaValid ? "通过" : "失败")}");

            // 清理RSA密钥
            rsaKeyPair.ClearPrivateKey();
            Console.WriteLine("🧹 RSA私钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ RSA签名演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 2. ECDSA数字签名演示
        Console.WriteLine("🌐 ECDSA数字签名演示");
        Console.WriteLine("─────────────────────────");
        try
        {
            var ecdsaSignature = new Ed25519DigitalSignature();
            var ecdsaKeyPair = ecdsaSignature.GenerateKeyPair();

            Console.WriteLine("✓ 创建ECDSA数字签名服务");
            Console.WriteLine("✓ 生成ECDSA P-256密钥对");
            Console.WriteLine("  • 优势: 更小的签名尺寸，更快的验证速度");

            var message = "区块链交易数据，使用ECDSA签名确保不可篡改 ⛓️";
            Console.WriteLine($"📝 消息内容: {message}");

            // 创建ECDSA签名
            var ecdsaSignatureBytes = ecdsaSignature.Sign(message, ecdsaKeyPair.PrivateKey!);
            Console.WriteLine($"✍️ ECDSA数字签名创建完成，签名长度: {ecdsaSignatureBytes.Length} 字节");

            // 验证ECDSA签名
            var ecdsaValid = ecdsaSignature.Verify(message, ecdsaSignatureBytes, ecdsaKeyPair.PublicKey!);
            Console.WriteLine($"✅ ECDSA签名验证: {(ecdsaValid ? "通过" : "失败")}");

            // ECDSA篡改检测
            var tamperedMessage = message + " [恶意修改]";
            var ecdsaTamperedValid = ecdsaSignature.Verify(tamperedMessage, ecdsaSignatureBytes, ecdsaKeyPair.PublicKey!);
            Console.WriteLine($"🚨 ECDSA篡改检测: {(!ecdsaTamperedValid ? "✓ 检测到篡改" : "✗ 未检测到篡改")}");

            // 异步ECDSA签名
            Console.WriteLine("\n🔄 异步ECDSA签名演示");
            var asyncEcdsaSignature = await ecdsaSignature.SignAsync(message, ecdsaKeyPair.PrivateKey!);
            var asyncEcdsaValid = await ecdsaSignature.VerifyAsync(message, asyncEcdsaSignature, ecdsaKeyPair.PublicKey!);
            Console.WriteLine($"✅ 异步ECDSA签名验证: {(asyncEcdsaValid ? "通过" : "失败")}");

            // 清理ECDSA密钥
            ecdsaKeyPair.ClearPrivateKey();
            Console.WriteLine("🧹 ECDSA私钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ ECDSA签名演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 3. 批量签名验证演示
        Console.WriteLine("📦 批量签名验证演示");
        Console.WriteLine("─────────────────────────");
        try
        {
            var rsaSignature = new RsaDigitalSignature();
            var rsaCrypto = new RsaAsymmetricCrypto();
            var batchKeyPair = rsaCrypto.GenerateKeyPair(2048);

            var documents = new[]
            {
                "文档1: 财务报表数据",
                "文档2: 用户协议条款",
                "文档3: 系统配置信息",
                "文档4: 安全策略文档",
                "文档5: 审计日志记录"
            };

            Console.WriteLine($"✓ 准备批量签名 {documents.Length} 个文档");

            // 批量创建签名
            var signatures = new List<byte[]>();
            foreach (var doc in documents)
            {
                var sig = rsaSignature.Sign(doc, batchKeyPair.PrivateKey!);
                signatures.Add(sig);
            }
            Console.WriteLine($"✍️ 批量签名创建完成，共 {signatures.Count} 个签名");

            // 批量验证签名
            var validCount = 0;
            for (int i = 0; i < documents.Length; i++)
            {
                var isValid = rsaSignature.Verify(documents[i], signatures[i], batchKeyPair.PublicKey!);
                if (isValid) validCount++;
            }
            Console.WriteLine($"✅ 批量验证完成: {validCount}/{documents.Length} 个签名有效");

            // 清理批量密钥
            batchKeyPair.ClearPrivateKey();
            Console.WriteLine("🧹 批量签名密钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 批量签名演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 现代加密算法演示（ChaCha20-Poly1305 + Ed25519 + Argon2）
    /// </summary>
    static async Task ModernCryptographyDemo()
    {
        Console.WriteLine("📋 现代加密算法功能演示包括：");
        Console.WriteLine("   • ChaCha20-Poly1305 AEAD认证加密");
        Console.WriteLine("   • Ed25519高性能数字签名");
        Console.WriteLine("   • Argon2抗GPU攻击密码哈希");
        Console.WriteLine("   • 现代算法性能对比");
        Console.WriteLine();

        // 1. ChaCha20-Poly1305详细演示
        Console.WriteLine("🚀 ChaCha20-Poly1305 AEAD详细演示");
        Console.WriteLine("─────────────────────────────────");
        try
        {
            var chacha = new ChaCha20Poly1305Crypto();
            var key = chacha.GenerateKey();
            var nonce = chacha.GenerateNonce();

            Console.WriteLine("✓ 创建ChaCha20-Poly1305加密服务");
            Console.WriteLine($"✓ 生成密钥 (长度: {key.Length * 8}位)");
            Console.WriteLine($"✓ 生成随机数 (长度: {nonce.Length * 8}位)");
            Console.WriteLine("  • 特点: 认证加密(AEAD)，防止篡改和伪造");

            var plaintext = "ChaCha20-Poly1305提供高性能的认证加密，比AES更快且更安全 🚀";
            Console.WriteLine($"📝 明文数据: {plaintext}");

            // 使用指定nonce加密
            var encrypted = chacha.Encrypt(plaintext, key, nonce);
            Console.WriteLine($"🔐 AEAD加密完成，密文长度: {encrypted.Length} 字节");

            // 解密并验证认证
            var decrypted = chacha.Decrypt(encrypted, key, nonce);
            Console.WriteLine($"🔓 AEAD解密完成: {decrypted}");

            var isValid = plaintext == decrypted;
            Console.WriteLine($"✅ 数据完整性和认证验证: {(isValid ? "通过" : "失败")}");

            // 自动生成nonce的加密
            Console.WriteLine("\n🔄 自动nonce生成演示");
            var autoEncrypted = chacha.Encrypt(plaintext, key); // 自动生成nonce
            var autoDecrypted = chacha.Decrypt(autoEncrypted, key);
            var autoValid = plaintext == autoDecrypted;
            Console.WriteLine($"✅ 自动nonce验证: {(autoValid ? "通过" : "失败")}");

            // 异步AEAD操作
            Console.WriteLine("\n🔄 异步AEAD操作演示");
            var asyncEncrypted = await chacha.EncryptAsync(plaintext, key, nonce);
            var asyncDecrypted = await chacha.DecryptAsync(asyncEncrypted, key, nonce);
            var asyncValid = plaintext == asyncDecrypted;
            Console.WriteLine($"✅ 异步AEAD验证: {(asyncValid ? "通过" : "失败")}");

            // 清理
            Array.Clear(key, 0, key.Length);
            Array.Clear(nonce, 0, nonce.Length);
            Console.WriteLine("🧹 ChaCha20密钥和nonce已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ ChaCha20-Poly1305演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 2. Ed25519数字签名详细演示
        Console.WriteLine("🌐 Ed25519数字签名详细演示");
        Console.WriteLine("─────────────────────────────");
        try
        {
            var ed25519 = new Ed25519DigitalSignature();
            var keyPair = ed25519.GenerateKeyPair();

            Console.WriteLine("✓ 创建Ed25519数字签名服务");
            Console.WriteLine("✓ 生成Ed25519密钥对");
            Console.WriteLine("  • 特点: 高性能，小签名尺寸，抗侧信道攻击");
            Console.WriteLine($"  • 公钥长度: {keyPair.PublicKey?.Length} 字节");
            Console.WriteLine($"  • 私钥长度: {keyPair.PrivateKey?.Length} 字节");

            var message = "Ed25519提供快速且安全的数字签名，适用于高频交易和区块链应用 ⛓️";
            Console.WriteLine($"📝 签名消息: {message}");

            // 创建Ed25519签名
            var signature = ed25519.Sign(message, keyPair.PrivateKey!);
            Console.WriteLine($"✍️ Ed25519签名创建完成，签名长度: {signature.Length} 字节");

            // 验证Ed25519签名
            var signatureValid = ed25519.Verify(message, signature, keyPair.PublicKey!);
            Console.WriteLine($"✅ Ed25519签名验证: {(signatureValid ? "通过" : "失败")}");

            // 性能测试 - 批量签名
            Console.WriteLine("\n⚡ Ed25519性能测试");
            var testMessages = Enumerable.Range(1, 100).Select(i => $"测试消息 {i}").ToArray();
            var stopwatch = Stopwatch.StartNew();

            foreach (var msg in testMessages)
            {
                var sig = ed25519.Sign(msg, keyPair.PrivateKey!);
                ed25519.Verify(msg, sig, keyPair.PublicKey!);
            }

            stopwatch.Stop();
            Console.WriteLine($"⚡ 100次签名+验证耗时: {stopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"⚡ 平均每次操作: {stopwatch.ElapsedMilliseconds / 100.0:F2} ms");

            // 清理
            keyPair.ClearPrivateKey();
            Console.WriteLine("🧹 Ed25519密钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Ed25519演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 3. Argon2高级配置演示
        Console.WriteLine("🔐 Argon2高级配置演示");
        Console.WriteLine("─────────────────────────");
        try
        {
            var password = "MyVerySecurePassword2024!@#$";
            Console.WriteLine($"📝 测试密码: {password}");

            // 不同安全级别的Argon2配置
            var configs = new[]
            {
                new { Name = "快速模式", Options = new Argon2PasswordHasher.Argon2Options { Iterations = 2, MemorySize = 32768, DegreeOfParallelism = 1 } },
                new { Name = "标准模式", Options = new Argon2PasswordHasher.Argon2Options { Iterations = 4, MemorySize = 65536, DegreeOfParallelism = 1 } },
                new { Name = "高安全模式", Options = new Argon2PasswordHasher.Argon2Options { Iterations = 8, MemorySize = 131072, DegreeOfParallelism = 2 } }
            };

            foreach (var config in configs)
            {
                Console.WriteLine($"\n🔧 {config.Name}配置测试");
                var hasher = new Argon2PasswordHasher(config.Options);

                var stopwatch = Stopwatch.StartNew();
                var hashResult = hasher.HashPassword(password, config.Options);
                stopwatch.Stop();

                Console.WriteLine($"  • 哈希时间: {stopwatch.ElapsedMilliseconds} ms");
                Console.WriteLine($"  • 迭代次数: {hashResult.Options.Iterations}");
                Console.WriteLine($"  • 内存使用: {hashResult.Options.MemorySize} KB");
                Console.WriteLine($"  • 并行度: {hashResult.Options.DegreeOfParallelism}");
                Console.WriteLine($"  • 哈希长度: {hashResult.Hash.Length} 字节");

                // 验证密码
                var verifyValid = hasher.VerifyPassword(password, hashResult);
                Console.WriteLine($"  • 验证结果: {(verifyValid ? "通过" : "失败")}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Argon2演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 密钥管理演示
    /// </summary>
    static async Task KeyManagementDemo()
    {
        Console.WriteLine("📋 密钥管理功能演示包括：");
        Console.WriteLine("   • 对称和非对称密钥生成");
        Console.WriteLine("   • 密钥导入导出功能");
        Console.WriteLine("   • PBKDF2密钥派生");
        Console.WriteLine("   • 安全随机数生成");
        Console.WriteLine();

        try
        {
            var keyManager = new CryptoKeyManager();
            Console.WriteLine("✓ 创建密钥管理服务");

            // 1. 对称密钥管理
            Console.WriteLine("\n🔑 对称密钥管理演示");
            Console.WriteLine("─────────────────────────");

            var symmetricKey128 = keyManager.GenerateSymmetricKey(128);
            var symmetricKey256 = keyManager.GenerateSymmetricKey(256);
            Console.WriteLine($"✓ 生成AES-128密钥 (长度: {symmetricKey128.Length * 8}位)");
            Console.WriteLine($"✓ 生成AES-256密钥 (长度: {symmetricKey256.Length * 8}位)");

            // 2. 非对称密钥管理
            Console.WriteLine("\n🔐 非对称密钥管理演示");
            Console.WriteLine("─────────────────────────");

            var rsaKeyPair2048 = keyManager.GenerateAsymmetricKeyPair(2048);
            var rsaKeyPair4096 = keyManager.GenerateAsymmetricKeyPair(4096);
            Console.WriteLine($"✓ 生成RSA-2048密钥对");
            Console.WriteLine($"  • 公钥长度: {rsaKeyPair2048.PublicKey?.Length} 字节");
            Console.WriteLine($"  • 私钥长度: {rsaKeyPair2048.PrivateKey?.Length} 字节");
            Console.WriteLine($"✓ 生成RSA-4096密钥对");
            Console.WriteLine($"  • 公钥长度: {rsaKeyPair4096.PublicKey?.Length} 字节");
            Console.WriteLine($"  • 私钥长度: {rsaKeyPair4096.PrivateKey?.Length} 字节");

            // 3. 密钥导出演示
            Console.WriteLine("\n💾 密钥导出演示");
            Console.WriteLine("─────────────────────────");

            var tempDir = Path.GetTempPath();
            var symmetricKeyFile = Path.Combine(tempDir, "symmetric.key");
            var privateKeyFile = Path.Combine(tempDir, "private.pem");
            var publicKeyFile = Path.Combine(tempDir, "public.pub");
            var password = "ExportPassword123!";

            // 导出对称密钥
            keyManager.ExportKey(symmetricKey256, symmetricKeyFile, password);
            Console.WriteLine($"✓ 对称密钥已导出到: {Path.GetFileName(symmetricKeyFile)}");

            // 导出密钥对
            keyManager.ExportKeyPair(rsaKeyPair2048, privateKeyFile, publicKeyFile, password);
            Console.WriteLine($"✓ 私钥已导出到: {Path.GetFileName(privateKeyFile)}");
            Console.WriteLine($"✓ 公钥已导出到: {Path.GetFileName(publicKeyFile)}");

            // 4. 密钥导入演示
            Console.WriteLine("\n📥 密钥导入演示");
            Console.WriteLine("─────────────────────────");

            var importedSymmetricKey = keyManager.ImportKey(symmetricKeyFile, password);
            var importedPrivateKey = keyManager.ImportPrivateKey(privateKeyFile, password);
            var importedPublicKey = keyManager.ImportPublicKey(publicKeyFile);

            Console.WriteLine($"✓ 对称密钥导入成功 (长度: {importedSymmetricKey.Length * 8}位)");
            Console.WriteLine($"✓ 私钥导入成功 (长度: {importedPrivateKey.Length} 字节)");
            Console.WriteLine($"✓ 公钥导入成功 (长度: {importedPublicKey.Length} 字节)");

            // 验证导入的密钥
            var keyMatches = symmetricKey256.SequenceEqual(importedSymmetricKey);
            Console.WriteLine($"✅ 对称密钥完整性验证: {(keyMatches ? "通过" : "失败")}");

            // 清理临时文件
            File.Delete(symmetricKeyFile);
            File.Delete(privateKeyFile);
            File.Delete(publicKeyFile);
            Console.WriteLine("🧹 临时密钥文件已清理");

            // 清理密钥
            Array.Clear(symmetricKey128, 0, symmetricKey128.Length);
            Array.Clear(symmetricKey256, 0, symmetricKey256.Length);
            Array.Clear(importedSymmetricKey, 0, importedSymmetricKey.Length);
            rsaKeyPair2048.ClearPrivateKey();
            rsaKeyPair4096.ClearPrivateKey();
            Console.WriteLine("🧹 所有密钥已安全清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 密钥管理演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 流式处理演示
    /// </summary>
    static async Task StreamProcessingDemo()
    {
        Console.WriteLine("📋 流式处理功能演示包括：");
        Console.WriteLine("   • 大文件AES流式加密/解密");
        Console.WriteLine("   • 流式SHA-256哈希计算");
        Console.WriteLine("   • 内存高效的文件处理");
        Console.WriteLine("   • 异步流式操作");
        Console.WriteLine();

        try
        {
            // 创建测试文件
            var tempDir = Path.GetTempPath();
            var originalFile = Path.Combine(tempDir, "large_test_file.txt");
            var encryptedFile = Path.Combine(tempDir, "large_test_file.encrypted");
            var decryptedFile = Path.Combine(tempDir, "large_test_file_decrypted.txt");

            // 生成大文件内容（模拟大文件）
            var fileContent = string.Join("\n", Enumerable.Range(1, 10000)
                .Select(i => $"这是第 {i} 行测试数据，用于演示流式加密处理大文件的能力。"));

            await File.WriteAllTextAsync(originalFile, fileContent);
            var fileSize = new FileInfo(originalFile).Length;
            Console.WriteLine($"✓ 创建测试文件: {Path.GetFileName(originalFile)}");
            Console.WriteLine($"📊 文件大小: {fileSize / 1024.0:F2} KB");

            // 1. 流式AES加密演示
            Console.WriteLine("\n🔐 流式AES加密演示");
            Console.WriteLine("─────────────────────────");

            var aes = new AesSymmetricCrypto();
            var key = aes.GenerateKey(256);
            var iv = aes.GenerateIV();

            var stopwatch = Stopwatch.StartNew();

            using (var inputStream = File.OpenRead(originalFile))
            using (var outputStream = File.Create(encryptedFile))
            {
                await inputStream.EncryptAesStreamAsync(outputStream, key, iv);
            }

            stopwatch.Stop();
            var encryptedSize = new FileInfo(encryptedFile).Length;
            Console.WriteLine($"🔐 流式加密完成");
            Console.WriteLine($"⏱️ 加密耗时: {stopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"📊 加密后大小: {encryptedSize / 1024.0:F2} KB");
            Console.WriteLine($"⚡ 加密速度: {fileSize / 1024.0 / (stopwatch.ElapsedMilliseconds / 1000.0):F2} KB/s");

            // 2. 流式AES解密演示
            Console.WriteLine("\n🔓 流式AES解密演示");
            Console.WriteLine("─────────────────────────");

            stopwatch.Restart();

            using (var inputStream = File.OpenRead(encryptedFile))
            using (var outputStream = File.Create(decryptedFile))
            {
                await inputStream.DecryptAesStreamAsync(outputStream, key, iv);
            }

            stopwatch.Stop();
            var decryptedSize = new FileInfo(decryptedFile).Length;
            Console.WriteLine($"🔓 流式解密完成");
            Console.WriteLine($"⏱️ 解密耗时: {stopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"📊 解密后大小: {decryptedSize / 1024.0:F2} KB");
            Console.WriteLine($"⚡ 解密速度: {encryptedSize / 1024.0 / (stopwatch.ElapsedMilliseconds / 1000.0):F2} KB/s");

            // 3. 文件完整性验证
            Console.WriteLine("\n✅ 文件完整性验证");
            Console.WriteLine("─────────────────────────");

            var originalContent = await File.ReadAllTextAsync(originalFile);
            var decryptedContent = await File.ReadAllTextAsync(decryptedFile);
            var contentMatches = originalContent == decryptedContent;
            Console.WriteLine($"✅ 文件内容完整性: {(contentMatches ? "通过" : "失败")}");
            Console.WriteLine($"📊 原始文件行数: {originalContent.Split('\n').Length}");
            Console.WriteLine($"📊 解密文件行数: {decryptedContent.Split('\n').Length}");

            // 4. 流式哈希计算演示
            Console.WriteLine("\n🔍 流式哈希计算演示");
            Console.WriteLine("─────────────────────────");

            stopwatch.Restart();
            string fileHash;
            using (var fileStream = File.OpenRead(originalFile))
            {
                fileHash = fileStream.ComputeSha256Hash();
            }
            stopwatch.Stop();

            Console.WriteLine($"🔍 流式SHA-256哈希: {fileHash}");
            Console.WriteLine($"⏱️ 哈希计算耗时: {stopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"⚡ 哈希计算速度: {fileSize / 1024.0 / (stopwatch.ElapsedMilliseconds / 1000.0):F2} KB/s");

            // 清理临时文件
            File.Delete(originalFile);
            File.Delete(encryptedFile);
            File.Delete(decryptedFile);
            Console.WriteLine("\n🧹 临时文件已清理");

            // 清理密钥
            Array.Clear(key, 0, key.Length);
            Array.Clear(iv, 0, iv.Length);
            Console.WriteLine("🧹 加密密钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 流式处理演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 扩展方法演示（完整API覆盖）
    /// </summary>
    static async Task ExtensionMethodsDemo()
    {
        Console.WriteLine("📋 扩展方法功能演示包括：");
        Console.WriteLine("   • 字符串加密扩展方法");
        Console.WriteLine("   • 现代加密算法扩展方法");
        Console.WriteLine("   • 字节数组扩展方法");
        Console.WriteLine("   • 流式处理扩展方法");
        Console.WriteLine();

        try
        {
            // 1. 基础字符串扩展方法
            Console.WriteLine("🛠️ 基础字符串扩展方法演示");
            Console.WriteLine("─────────────────────────────");

            var plainText = "使用扩展方法简化加密操作的测试数据 🔧";
            Console.WriteLine($"📝 原始文本: {plainText}");

            // AES扩展方法
            var aes = new AesSymmetricCrypto();
            var aesKey = aes.GenerateKey(256);

            var aesEncrypted = plainText.EncryptAes(aesKey);
            Console.WriteLine($"🔐 AES扩展方法加密完成");

            var aesDecrypted = aesEncrypted.DecryptAes(aesKey);
            Console.WriteLine($"🔓 AES扩展方法解密完成: {aesDecrypted}");

            var aesValid = plainText == aesDecrypted;
            Console.WriteLine($"✅ AES扩展方法验证: {(aesValid ? "通过" : "失败")}");

            // RSA扩展方法
            var rsa = new RsaAsymmetricCrypto();
            var rsaKeyPair = rsa.GenerateKeyPair(2048);

            var rsaEncrypted = plainText.EncryptRsa(rsaKeyPair.PublicKey!);
            Console.WriteLine($"🔐 RSA扩展方法加密完成");

            var rsaDecrypted = rsaEncrypted.DecryptRsa(rsaKeyPair.PrivateKey!);
            Console.WriteLine($"🔓 RSA扩展方法解密完成: {rsaDecrypted}");

            var rsaValid = plainText == rsaDecrypted;
            Console.WriteLine($"✅ RSA扩展方法验证: {(rsaValid ? "通过" : "失败")}");

            Console.WriteLine();

            // 2. 现代加密算法扩展方法
            Console.WriteLine("🚀 现代加密算法扩展方法演示");
            Console.WriteLine("─────────────────────────────────");

            var modernText = "现代加密算法扩展方法测试数据 🚀";
            Console.WriteLine($"📝 测试文本: {modernText}");

            // ChaCha20-Poly1305扩展方法
            var chacha = new ChaCha20Poly1305Crypto();
            var chachaKey = chacha.GenerateKey();

            var chachaEncrypted = modernText.EncryptChaCha20Poly1305(chachaKey);
            Console.WriteLine($"🔐 ChaCha20-Poly1305扩展方法加密完成");

            var chachaDecrypted = chachaEncrypted.DecryptChaCha20Poly1305(chachaKey);
            Console.WriteLine($"🔓 ChaCha20-Poly1305扩展方法解密完成: {chachaDecrypted}");

            var chachaValid = modernText == chachaDecrypted;
            Console.WriteLine($"✅ ChaCha20-Poly1305扩展方法验证: {(chachaValid ? "通过" : "失败")}");

            // Ed25519数字签名扩展方法
            var ed25519 = new Ed25519DigitalSignature();
            var ed25519KeyPair = ed25519.GenerateKeyPair();

            var ed25519Signature = modernText.SignEcdsa(ed25519KeyPair.PrivateKey!);
            Console.WriteLine($"✍️ Ed25519扩展方法签名完成");

            var ed25519Valid = modernText.VerifyEcdsaSignature(ed25519Signature, ed25519KeyPair.PublicKey!);
            Console.WriteLine($"✅ Ed25519扩展方法验证: {(ed25519Valid ? "通过" : "失败")}");

            Console.WriteLine();

            // 3. 哈希扩展方法
            Console.WriteLine("🔍 哈希扩展方法演示");
            Console.WriteLine("─────────────────────────");

            var hashText = "哈希扩展方法测试数据";
            Console.WriteLine($"📝 哈希文本: {hashText}");

            // SHA-256扩展方法
            var sha256Hash = hashText.ToSha256Hash();
            Console.WriteLine($"🔍 SHA-256扩展方法: {sha256Hash[..32]}...");

            // Argon2扩展方法
            var password = "ExtensionPassword2024!@#";
            var argon2Hash = password.ToArgon2Hash();
            Console.WriteLine($"🔐 Argon2扩展方法: {argon2Hash.FormattedHash[..50]}...");

            var passwordValid = password.VerifyArgon2Hash(argon2Hash);
            Console.WriteLine($"✅ Argon2密码验证: {(passwordValid ? "通过" : "失败")}");

            Console.WriteLine();

            // 4. 数字签名扩展方法
            Console.WriteLine("✍️ 数字签名扩展方法演示");
            Console.WriteLine("─────────────────────────────");

            var signatureText = "数字签名扩展方法测试数据";
            Console.WriteLine($"📝 签名文本: {signatureText}");

            // RSA签名扩展方法
            var rsaSignature = signatureText.SignRsa(rsaKeyPair.PrivateKey!);
            Console.WriteLine($"✍️ RSA签名扩展方法完成");

            var rsaSignatureValid = signatureText.VerifyRsaSignature(rsaSignature, rsaKeyPair.PublicKey!);
            Console.WriteLine($"✅ RSA签名扩展方法验证: {(rsaSignatureValid ? "通过" : "失败")}");

            Console.WriteLine();

            // 5. 异步扩展方法演示
            Console.WriteLine("🔄 异步扩展方法演示");
            Console.WriteLine("─────────────────────────");

            var asyncText = "异步扩展方法测试数据";
            Console.WriteLine($"📝 异步文本: {asyncText}");

            // 异步ChaCha20-Poly1305
            var asyncChachaEncrypted = await asyncText.EncryptChaCha20Poly1305Async(chachaKey);
            var asyncChachaDecrypted = await asyncChachaEncrypted.DecryptChaCha20Poly1305Async(chachaKey);
            var asyncChachaValid = asyncText == asyncChachaDecrypted;
            Console.WriteLine($"✅ 异步ChaCha20-Poly1305验证: {(asyncChachaValid ? "通过" : "失败")}");

            // 异步Ed25519签名
            var asyncEd25519Signature = await asyncText.SignEcdsaAsync(ed25519KeyPair.PrivateKey!);
            var asyncEd25519Valid = await asyncText.VerifyEcdsaSignatureAsync(asyncEd25519Signature, ed25519KeyPair.PublicKey!);
            Console.WriteLine($"✅ 异步Ed25519签名验证: {(asyncEd25519Valid ? "通过" : "失败")}");

            // 异步Argon2哈希
            var asyncArgon2Hash = await password.ToArgon2HashAsync();
            var asyncPasswordValid = password.VerifyArgon2Hash(asyncArgon2Hash);
            Console.WriteLine($"✅ 异步Argon2验证: {(asyncPasswordValid ? "通过" : "失败")}");

            // 清理所有密钥
            Array.Clear(aesKey, 0, aesKey.Length);
            Array.Clear(chachaKey, 0, chachaKey.Length);
            rsaKeyPair.ClearPrivateKey();
            ed25519KeyPair.ClearPrivateKey();
            Console.WriteLine("\n🧹 所有密钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 扩展方法演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 性能测试演示
    /// </summary>
    static async Task PerformanceTestDemo()
    {
        Console.WriteLine("📋 性能测试功能演示包括：");
        Console.WriteLine("   • 对称加密算法性能对比");
        Console.WriteLine("   • 非对称加密算法性能对比");
        Console.WriteLine("   • 哈希算法性能对比");
        Console.WriteLine("   • 数字签名算法性能对比");
        Console.WriteLine();

        try
        {
            var testData = "这是用于性能测试的标准数据，长度适中，适合各种算法的性能对比测试。";
            var testIterations = 1000;
            Console.WriteLine($"📊 测试数据长度: {testData.Length} 字符");
            Console.WriteLine($"🔄 测试迭代次数: {testIterations}");
            Console.WriteLine();

            // 1. 对称加密性能对比
            Console.WriteLine("⚡ 对称加密算法性能对比");
            Console.WriteLine("─────────────────────────────");

            // AES-256性能测试
            var aes = new AesSymmetricCrypto();
            var aesKey = aes.GenerateKey(256);
            var aesIv = aes.GenerateIV();

            var aesStopwatch = Stopwatch.StartNew();
            for (int i = 0; i < testIterations; i++)
            {
                var encrypted = aes.Encrypt(testData, aesKey, aesIv);
                var decrypted = aes.Decrypt(encrypted, aesKey, aesIv);
            }
            aesStopwatch.Stop();

            Console.WriteLine($"🔐 AES-256性能:");
            Console.WriteLine($"  • 总耗时: {aesStopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"  • 平均每次: {aesStopwatch.ElapsedMilliseconds / (double)testIterations:F3} ms");
            Console.WriteLine($"  • 吞吐量: {testIterations * 1000.0 / aesStopwatch.ElapsedMilliseconds:F0} ops/s");

            // ChaCha20-Poly1305性能测试
            var chacha = new ChaCha20Poly1305Crypto();
            var chachaKey = chacha.GenerateKey();

            var chachaStopwatch = Stopwatch.StartNew();
            for (int i = 0; i < testIterations; i++)
            {
                var encrypted = chacha.Encrypt(testData, chachaKey);
                var decrypted = chacha.Decrypt(encrypted, chachaKey);
            }
            chachaStopwatch.Stop();

            Console.WriteLine($"🚀 ChaCha20-Poly1305性能:");
            Console.WriteLine($"  • 总耗时: {chachaStopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"  • 平均每次: {chachaStopwatch.ElapsedMilliseconds / (double)testIterations:F3} ms");
            Console.WriteLine($"  • 吞吐量: {testIterations * 1000.0 / chachaStopwatch.ElapsedMilliseconds:F0} ops/s");

            var chachaSpeedup = (double)aesStopwatch.ElapsedMilliseconds / chachaStopwatch.ElapsedMilliseconds;
            Console.WriteLine($"📈 ChaCha20相对AES加速比: {chachaSpeedup:F2}x");

            Console.WriteLine();

            // 2. 哈希算法性能对比
            Console.WriteLine("⚡ 哈希算法性能对比");
            Console.WriteLine("─────────────────────────");

            // SHA-256性能测试
            var sha256 = new Sha256HashProvider();
            var sha256Stopwatch = Stopwatch.StartNew();
            for (int i = 0; i < testIterations; i++)
            {
                var hash = sha256.ComputeHash(testData);
            }
            sha256Stopwatch.Stop();

            Console.WriteLine($"🔍 SHA-256性能:");
            Console.WriteLine($"  • 总耗时: {sha256Stopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"  • 平均每次: {sha256Stopwatch.ElapsedMilliseconds / (double)testIterations:F3} ms");
            Console.WriteLine($"  • 吞吐量: {testIterations * 1000.0 / sha256Stopwatch.ElapsedMilliseconds:F0} ops/s");

            // Argon2性能测试（较少迭代次数）
            var argon2 = new Argon2PasswordHasher();
            var argon2Iterations = 10; // Argon2较慢，减少测试次数
            var argon2Stopwatch = Stopwatch.StartNew();
            for (int i = 0; i < argon2Iterations; i++)
            {
                var hash = argon2.HashPassword(testData);
            }
            argon2Stopwatch.Stop();

            Console.WriteLine($"🔐 Argon2性能 ({argon2Iterations}次):");
            Console.WriteLine($"  • 总耗时: {argon2Stopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"  • 平均每次: {argon2Stopwatch.ElapsedMilliseconds / (double)argon2Iterations:F1} ms");
            Console.WriteLine($"  • 吞吐量: {argon2Iterations * 1000.0 / argon2Stopwatch.ElapsedMilliseconds:F1} ops/s");

            Console.WriteLine();

            // 3. 数字签名性能对比
            Console.WriteLine("⚡ 数字签名算法性能对比");
            Console.WriteLine("─────────────────────────────");

            var signatureIterations = 100; // 数字签名较慢，减少测试次数

            // RSA签名性能测试
            var rsaSignature = new RsaDigitalSignature();
            var rsaCrypto = new RsaAsymmetricCrypto();
            var rsaKeyPair = rsaCrypto.GenerateKeyPair(2048);

            var rsaStopwatch = Stopwatch.StartNew();
            for (int i = 0; i < signatureIterations; i++)
            {
                var signature = rsaSignature.Sign(testData, rsaKeyPair.PrivateKey!);
                var valid = rsaSignature.Verify(testData, signature, rsaKeyPair.PublicKey!);
            }
            rsaStopwatch.Stop();

            Console.WriteLine($"✍️ RSA-2048签名性能 ({signatureIterations}次):");
            Console.WriteLine($"  • 总耗时: {rsaStopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"  • 平均每次: {rsaStopwatch.ElapsedMilliseconds / (double)signatureIterations:F1} ms");
            Console.WriteLine($"  • 吞吐量: {signatureIterations * 1000.0 / rsaStopwatch.ElapsedMilliseconds:F1} ops/s");

            // ECDSA签名性能测试
            var ecdsaSignature = new Ed25519DigitalSignature();
            var ecdsaKeyPair = ecdsaSignature.GenerateKeyPair();

            var ecdsaStopwatch = Stopwatch.StartNew();
            for (int i = 0; i < signatureIterations; i++)
            {
                var signature = ecdsaSignature.Sign(testData, ecdsaKeyPair.PrivateKey!);
                var valid = ecdsaSignature.Verify(testData, signature, ecdsaKeyPair.PublicKey!);
            }
            ecdsaStopwatch.Stop();

            Console.WriteLine($"🌐 ECDSA签名性能 ({signatureIterations}次):");
            Console.WriteLine($"  • 总耗时: {ecdsaStopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"  • 平均每次: {ecdsaStopwatch.ElapsedMilliseconds / (double)signatureIterations:F1} ms");
            Console.WriteLine($"  • 吞吐量: {signatureIterations * 1000.0 / ecdsaStopwatch.ElapsedMilliseconds:F1} ops/s");

            var ecdsaSpeedup = (double)rsaStopwatch.ElapsedMilliseconds / ecdsaStopwatch.ElapsedMilliseconds;
            Console.WriteLine($"📈 ECDSA相对RSA加速比: {ecdsaSpeedup:F2}x");

            // 清理密钥
            Array.Clear(aesKey, 0, aesKey.Length);
            Array.Clear(chachaKey, 0, chachaKey.Length);
            rsaKeyPair.ClearPrivateKey();
            ecdsaKeyPair.ClearPrivateKey();
            Console.WriteLine("\n🧹 性能测试密钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 性能测试演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 最佳实践演示
    /// </summary>
    static async Task BestPracticesDemo()
    {
        Console.WriteLine("📋 最佳实践功能演示包括：");
        Console.WriteLine("   • 密钥安全管理最佳实践");
        Console.WriteLine("   • 异常处理和错误恢复");
        Console.WriteLine("   • 安全编程模式");
        Console.WriteLine("   • 常见安全陷阱避免");
        Console.WriteLine();

        // 1. 密钥安全管理最佳实践
        Console.WriteLine("🔐 密钥安全管理最佳实践");
        Console.WriteLine("─────────────────────────────");
        try
        {
            Console.WriteLine("✅ 最佳实践1: 使用强密钥长度");
            var aes = new AesSymmetricCrypto();
            var strongKey = aes.GenerateKey(256); // 使用AES-256而不是AES-128
            Console.WriteLine($"  • 推荐: AES-256 ({strongKey.Length * 8}位)");
            Console.WriteLine($"  • 避免: AES-128 (128位) - 安全强度不足");

            Console.WriteLine("\n✅ 最佳实践2: 每次加密使用新的IV");
            var data = "重要数据";
            var iv1 = aes.GenerateIV();
            var iv2 = aes.GenerateIV();
            var encrypted1 = aes.Encrypt(data, strongKey, iv1);
            var encrypted2 = aes.Encrypt(data, strongKey, iv2);
            Console.WriteLine($"  • 相同数据，不同IV产生不同密文: {!encrypted1.SequenceEqual(encrypted2)}");

            Console.WriteLine("\n✅ 最佳实践3: 及时清理敏感数据");
            var tempKey = aes.GenerateKey(256);
            Console.WriteLine($"  • 使用前密钥长度: {tempKey.Length} 字节");
            Array.Clear(tempKey, 0, tempKey.Length);
            Console.WriteLine($"  • 清理后密钥内容: 全部为0");

            Console.WriteLine("\n✅ 最佳实践4: 使用安全的随机数生成器");
            var secureRandom = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(secureRandom);
            }
            Console.WriteLine($"  • 使用密码学安全的随机数生成器");
            Console.WriteLine($"  • 避免使用Random类生成密钥或IV");

            // 清理
            Array.Clear(strongKey, 0, strongKey.Length);
            Array.Clear(iv1, 0, iv1.Length);
            Array.Clear(iv2, 0, iv2.Length);
            Array.Clear(secureRandom, 0, secureRandom.Length);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 密钥管理最佳实践演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 2. 异常处理和错误恢复
        Console.WriteLine("🛡️ 异常处理和错误恢复最佳实践");
        Console.WriteLine("─────────────────────────────────────");
        try
        {
            Console.WriteLine("✅ 最佳实践5: 正确的异常处理");

            var aes = new AesSymmetricCrypto();
            var key = aes.GenerateKey(256);

            // 演示正确的异常处理
            try
            {
                // 故意使用错误的密钥长度触发异常
                var wrongKey = new byte[16]; // AES需要32字节密钥
                var result = aes.Encrypt("test", wrongKey);
            }
            catch (ArgumentException ex) when (ex.Message.Contains("密钥"))
            {
                Console.WriteLine($"  • 捕获到密钥相关异常: {ex.Message}");
                Console.WriteLine($"  • 正确处理: 使用具体的异常类型和条件");
            }
            catch (CryptographyException ex)
            {
                Console.WriteLine($"  • 捕获到加密异常: {ex.Message}");
                Console.WriteLine($"  • 错误代码: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  • 捕获到通用异常: {ex.Message}");
                Console.WriteLine($"  • 避免: 空的catch块或忽略异常");
            }

            Console.WriteLine("\n✅ 最佳实践6: 安全的错误信息");
            try
            {
                // 模拟解密失败
                var invalidCiphertext = new byte[16];
                var result = aes.Decrypt(invalidCiphertext, key);
            }
            catch (CryptographyException)
            {
                Console.WriteLine($"  • 正确: 返回通用错误信息，不泄露具体细节");
                Console.WriteLine($"  • 避免: 在错误信息中包含密钥或敏感数据");
            }

            Array.Clear(key, 0, key.Length);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 异常处理演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 3. 安全编程模式
        Console.WriteLine("🔒 安全编程模式最佳实践");
        Console.WriteLine("─────────────────────────────");
        try
        {
            Console.WriteLine("✅ 最佳实践7: 使用AEAD加密模式");
            var chacha = new ChaCha20Poly1305Crypto();
            var key = chacha.GenerateKey();
            var data = "重要数据";

            var encrypted = chacha.Encrypt(data, key);
            Console.WriteLine($"  • 推荐: ChaCha20-Poly1305 (AEAD) - 提供认证和加密");
            Console.WriteLine($"  • 避免: 单独的加密模式 - 容易受到篡改攻击");

            Console.WriteLine("\n✅ 最佳实践8: 密码安全存储");
            var password = "UserPassword123!";
            var argon2 = new Argon2PasswordHasher();
            var hashResult = argon2.HashPassword(password);
            Console.WriteLine($"  • 推荐: Argon2密码哈希 - 抗GPU攻击");
            Console.WriteLine($"  • 避免: MD5, SHA1直接哈希 - 容易被彩虹表攻击");
            Console.WriteLine($"  • 盐值长度: {hashResult.Salt.Length} 字节");

            Console.WriteLine("\n✅ 最佳实践9: 数字签名验证");
            var rsaSignature = new RsaDigitalSignature();
            var rsaCrypto = new RsaAsymmetricCrypto();
            var keyPair = rsaCrypto.GenerateKeyPair(2048);

            var document = "重要文档";
            var signature = rsaSignature.Sign(document, keyPair.PrivateKey!);
            var isValid = rsaSignature.Verify(document, signature, keyPair.PublicKey!);
            Console.WriteLine($"  • 正确: 始终验证数字签名 - {(isValid ? "验证通过" : "验证失败")}");
            Console.WriteLine($"  • 避免: 跳过签名验证 - 无法检测篡改");

            // 清理
            Array.Clear(key, 0, key.Length);
            keyPair.ClearPrivateKey();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 安全编程模式演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 4. 常见安全陷阱避免
        Console.WriteLine("⚠️ 常见安全陷阱避免");
        Console.WriteLine("─────────────────────────");
        try
        {
            Console.WriteLine("❌ 陷阱1: 硬编码密钥");
            Console.WriteLine("  • 错误示例: const string key = \"hardcoded_key\";");
            Console.WriteLine("  • 正确做法: 从安全配置或密钥管理服务获取密钥");

            Console.WriteLine("\n❌ 陷阱2: 重用IV或Nonce");
            Console.WriteLine("  • 错误示例: 使用固定的IV进行多次加密");
            Console.WriteLine("  • 正确做法: 每次加密生成新的随机IV");

            Console.WriteLine("\n❌ 陷阱3: 不验证输入");
            var aes = new AesSymmetricCrypto();
            try
            {
                // 演示输入验证的重要性
                string? nullInput = null;
                // 这会抛出ArgumentNullException
                var result = aes.Encrypt(nullInput!, new byte[32]);
            }
            catch (ArgumentNullException)
            {
                Console.WriteLine("  • 正确: 库已实现输入验证，防止null输入");
                Console.WriteLine("  • 避免: 不验证输入参数的有效性");
            }

            Console.WriteLine("\n❌ 陷阱4: 忽略异步操作的ConfigureAwait");
            Console.WriteLine("  • 错误示例: await SomeAsyncMethod();");
            Console.WriteLine("  • 正确做法: await SomeAsyncMethod().ConfigureAwait(false);");
            Console.WriteLine("  • 说明: 避免死锁，提高性能");

            Console.WriteLine("\n✅ 最佳实践10: 定期密钥轮换");
            Console.WriteLine("  • 建议: 定期更换加密密钥");
            Console.WriteLine("  • 频率: 根据数据敏感性确定（如每月、每季度）");
            Console.WriteLine("  • 实现: 使用版本化密钥管理");

            Console.WriteLine("\n✅ 最佳实践11: 安全审计和日志");
            Console.WriteLine("  • 记录: 密钥生成、加密操作、访问尝试");
            Console.WriteLine("  • 避免: 在日志中记录密钥或明文数据");
            Console.WriteLine("  • 监控: 异常的加密操作模式");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 安全陷阱演示失败: {ex.Message}");
        }

        Console.WriteLine();

        // 5. 性能和安全平衡
        Console.WriteLine("⚖️ 性能和安全平衡最佳实践");
        Console.WriteLine("─────────────────────────────────");
        try
        {
            Console.WriteLine("✅ 最佳实践12: 算法选择指南");
            Console.WriteLine("  • 对称加密: ChaCha20-Poly1305 (性能) vs AES-256-GCM (兼容性)");
            Console.WriteLine("  • 非对称加密: Ed25519 (性能) vs RSA-2048+ (兼容性)");
            Console.WriteLine("  • 哈希算法: SHA-256 (通用) vs Argon2 (密码)");

            Console.WriteLine("\n✅ 最佳实践13: 批量操作优化");
            var aes = new AesSymmetricCrypto();
            var key = aes.GenerateKey(256);
            var testData = Enumerable.Range(1, 100).Select(i => $"数据{i}").ToArray();

            var stopwatch = Stopwatch.StartNew();
            var results = new List<byte[]>();
            foreach (var data in testData)
            {
                results.Add(aes.Encrypt(data, key));
            }
            stopwatch.Stop();

            Console.WriteLine($"  • 批量加密100项耗时: {stopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"  • 建议: 对于大量数据，考虑并行处理或流式处理");

            Console.WriteLine("\n✅ 最佳实践14: 内存管理");
            Console.WriteLine("  • 使用: using语句自动释放资源");
            Console.WriteLine("  • 清理: 及时清理敏感数据的内存");
            Console.WriteLine("  • 避免: 在托管堆上长时间保留敏感数据");

            Array.Clear(key, 0, key.Length);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 性能安全平衡演示失败: {ex.Message}");
        }

        Console.WriteLine();
        Console.WriteLine("🎯 最佳实践总结:");
        Console.WriteLine("   1. 使用强密钥长度和安全算法");
        Console.WriteLine("   2. 每次加密使用新的IV/Nonce");
        Console.WriteLine("   3. 及时清理敏感数据");
        Console.WriteLine("   4. 实现完整的异常处理");
        Console.WriteLine("   5. 选择AEAD加密模式");
        Console.WriteLine("   6. 使用Argon2进行密码哈希");
        Console.WriteLine("   7. 始终验证数字签名");
        Console.WriteLine("   8. 避免硬编码密钥");
        Console.WriteLine("   9. 定期进行密钥轮换");
        Console.WriteLine("   10. 平衡性能和安全需求");
    }
}
