# Liam.Cryptography 示例应用程序

## 项目概述

这是一个完整的 **Liam.Cryptography** 功能库示例应用程序，展示了该加密库的所有核心功能和最佳实践。通过运行这个控制台应用程序，您可以深入了解如何在实际项目中使用 Liam.Cryptography 库。

## 功能演示

### 🔐 1. 对称加密演示 (SymmetricEncryptionDemo)
- **AES-256基础加密/解密**：展示标准的AES加密流程，包括密钥和IV生成
- **ChaCha20-Poly1305 AEAD加密**：演示现代认证加密算法的使用
- **异步操作**：展示异步加密/解密的性能优势和正确用法
- **数据完整性验证**：演示加密前后数据的完整性检查
- **安全清理**：展示敏感数据的安全清理最佳实践

### 🔑 2. 非对称加密演示 (AsymmetricEncryptionDemo)
- **RSA-2048/4096密钥生成**：展示不同强度RSA密钥的生成和使用
- **公钥加密/私钥解密**：演示完整的RSA非对称加密流程
- **ECDSA P-256密钥生成**：展示椭圆曲线密钥对的生成
- **异步非对称操作**：演示异步RSA加密/解密操作
- **高强度加密**：对比RSA-2048和RSA-4096的安全性差异

### 🔍 3. 哈希算法演示 (HashingDemo)
- **SHA-256数据哈希**：展示基础哈希功能和完整性验证
- **Argon2密码哈希**：演示现代密码哈希算法的多种配置
- **文件哈希计算**：演示大文件的哈希计算和验证
- **篡改检测**：展示哈希算法的篡改检测能力
- **异步哈希操作**：演示异步哈希计算的性能优势

### ✍️ 4. 数字签名演示 (DigitalSignatureDemo)
- **RSA-PSS数字签名**：展示RSA签名的创建和验证流程
- **ECDSA数字签名**：展示Ed25519签名算法的高性能特性
- **批量签名验证**：演示高效的批量文档签名处理
- **篡改检测**：展示数字签名的防篡改和防伪造能力
- **异步签名操作**：演示异步数字签名的创建和验证

### 🚀 5. 现代加密算法演示 (ModernCryptographyDemo)
- **ChaCha20-Poly1305详细演示**：深入展示AEAD认证加密的特性
- **Ed25519高性能签名**：演示现代椭圆曲线签名的性能优势
- **Argon2高级配置**：展示不同安全级别的Argon2配置选项
- **性能基准测试**：对比现代算法与传统算法的性能表现
- **自动nonce生成**：演示安全的随机数自动生成机制

### 🗝️ 6. 密钥管理演示 (KeyManagementDemo)
- **对称密钥生成**：展示AES-128/256密钥的安全生成
- **非对称密钥生成**：展示RSA-2048/4096密钥对的生成
- **密钥导入导出**：演示密钥的安全存储和恢复功能
- **密钥完整性验证**：验证导入导出过程中密钥的完整性
- **安全密钥清理**：展示密钥使用后的安全清理流程

### 📁 7. 流式处理演示 (StreamProcessingDemo)
- **大文件AES加密**：展示内存高效的大文件加密处理
- **流式哈希计算**：演示大文件的流式SHA-256哈希计算
- **性能监控**：实时监控加密/解密的速度和效率
- **内存优化**：演示如何在处理大文件时保持低内存使用
- **文件完整性验证**：验证流式处理前后文件的完整性

### 🛠️ 8. 扩展方法演示 (ExtensionMethodsDemo)
- **基础字符串扩展**：演示AES和RSA的字符串扩展方法
- **现代算法扩展**：展示ChaCha20-Poly1305和Ed25519扩展方法
- **哈希扩展方法**：演示SHA-256和Argon2的便捷扩展方法
- **数字签名扩展**：展示RSA和ECDSA签名的扩展方法
- **异步扩展方法**：演示所有算法的异步扩展方法使用

### ⚡ 9. 性能测试演示 (PerformanceTestDemo)
- **对称加密性能对比**：AES-256 vs ChaCha20-Poly1305性能测试
- **哈希算法性能对比**：SHA-256 vs Argon2性能基准测试
- **数字签名性能对比**：RSA vs ECDSA签名性能对比
- **吞吐量测试**：测量各算法的操作吞吐量和延迟
- **加速比分析**：分析现代算法相对传统算法的性能提升

### ⭐ 10. 最佳实践演示 (BestPracticesDemo)
- **密钥安全管理**：展示密钥的安全生成、存储和销毁
- **异常处理模式**：演示正确的异常处理和错误恢复
- **安全编程模式**：展示AEAD加密、安全哈希等最佳实践
- **常见陷阱避免**：展示硬编码密钥、IV重用等安全陷阱
- **性能安全平衡**：展示如何在性能和安全之间找到平衡

## 快速开始

### 环境要求
- **.NET 8.0** 或更高版本
- **Liam.Cryptography 1.1.0+** NuGet包
- **Windows/Linux/macOS** 跨平台支持

### 运行示例

1. **克隆或下载项目**
   ```bash
   git clone https://gitee.com/liam-gitee/liam.git
   cd liam/examples/Liam.Cryptography.Example
   ```

2. **还原依赖包**
   ```bash
   dotnet restore
   ```

3. **运行完整示例程序**
   ```bash
   dotnet run
   ```

### 运行特定演示

如果您只想查看特定功能的演示，可以修改 `Program.cs` 文件中的 `Main` 方法，注释掉不需要的演示部分：

```csharp
// 只运行对称加密演示
await SymmetricEncryptionDemo();

// 只运行现代加密算法演示
await ModernCryptographyDemo();

// 只运行性能测试演示
await PerformanceTestDemo();

// 只运行最佳实践演示
await BestPracticesDemo();
```

### 自定义演示参数

您可以通过修改代码中的参数来自定义演示：

```csharp
// 修改性能测试的迭代次数
var testIterations = 5000; // 默认1000

// 修改Argon2的安全配置
var customOptions = new Argon2PasswordHasher.Argon2Options
{
    Iterations = 8,        // 默认4
    MemorySize = 131072,   // 默认65536 (128MB vs 64MB)
    DegreeOfParallelism = 4 // 默认1
};

// 修改流式处理的文件大小
var fileLines = 50000; // 默认10000行
```

## 项目结构

```
Liam.Cryptography.Example/
├── Program.cs                          # 主程序入口，包含所有演示方法
├── Liam.Cryptography.Example.csproj    # 项目文件
└── README.md                           # 本文档

演示方法结构：
├── SymmetricEncryptionDemo()           # 对称加密演示
├── AsymmetricEncryptionDemo()          # 非对称加密演示
├── HashingDemo()                       # 哈希算法演示
├── DigitalSignatureDemo()              # 数字签名演示
├── ModernCryptographyDemo()            # 现代加密算法演示
├── KeyManagementDemo()                 # 密钥管理演示
├── StreamProcessingDemo()              # 流式处理演示
├── ExtensionMethodsDemo()              # 扩展方法演示
├── PerformanceTestDemo()               # 性能测试演示
└── BestPracticesDemo()                 # 最佳实践演示
```

## 示例输出

运行程序后，您将看到类似以下的输出：

```
=== Liam.Cryptography 功能库完整示例程序 ===
本程序全面演示Liam.Cryptography库的所有核心功能
包括传统算法、现代加密、流式处理、性能测试和最佳实践

🔐 1. 对称加密演示
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📋 对称加密功能演示包括：
   • AES-256基础加密/解密
   • ChaCha20-Poly1305 AEAD加密
   • 异步加密/解密操作
   • 异常处理最佳实践

🔐 基础AES-256加密演示
─────────────────────────
✓ 创建AES加密服务实例
✓ 生成AES-256密钥 (长度: 256位)
✓ 生成初始化向量 (长度: 128位)
📝 原始数据: 这是一段需要加密保护的重要数据！🔒
🔐 加密完成，密文长度: 64 字节
🔓 解密完成: 这是一段需要加密保护的重要数据！🔒
✅ 数据完整性验证: 通过
🔄 异步操作验证: 通过
🧹 敏感数据已安全清理

🚀 ChaCha20-Poly1305 AEAD加密演示
─────────────────────────────────
✓ 创建ChaCha20-Poly1305加密服务
✓ 生成ChaCha20密钥 (长度: 256位)
✓ 生成随机数 (长度: 96位)
  • 特点: 认证加密(AEAD)，防止篡改和伪造
📝 测试数据: ChaCha20-Poly1305提供高性能的认证加密，比AES更快且更安全 🚀
🔐 AEAD加密完成，密文长度: 108 字节
🔓 AEAD解密完成: ChaCha20-Poly1305提供高性能的认证加密，比AES更快且更安全 🚀
✅ 数据完整性和认证验证: 通过
🔄 异步AEAD验证: 通过
🧹 ChaCha20密钥和nonce已清理

🔑 2. 非对称加密演示
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📋 非对称加密功能演示包括：
   • RSA-2048/4096密钥生成和加密
   • ECDSA P-256密钥生成
   • 公钥加密/私钥解密流程
   • 异步非对称加密操作
...

⚡ 9. 性能测试演示
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 测试数据长度: 64 字符
🔄 测试迭代次数: 1000

⚡ 对称加密算法性能对比
─────────────────────────────
🔐 AES-256性能:
  • 总耗时: 45 ms
  • 平均每次: 0.045 ms
  • 吞吐量: 22222 ops/s
🚀 ChaCha20-Poly1305性能:
  • 总耗时: 28 ms
  • 平均每次: 0.028 ms
  • 吞吐量: 35714 ops/s
📈 ChaCha20相对AES加速比: 1.61x
...

⭐ 10. 最佳实践演示
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 最佳实践总结:
   1. 使用强密钥长度和安全算法
   2. 每次加密使用新的IV/Nonce
   3. 及时清理敏感数据
   4. 实现完整的异常处理
   5. 选择AEAD加密模式
   6. 使用Argon2进行密码哈希
   7. 始终验证数字签名
   8. 避免硬编码密钥
   9. 定期进行密钥轮换
   10. 平衡性能和安全需求

✅ 所有示例演示完成！
📖 更多详细信息请参阅：https://www.nuget.org/packages/Liam.Cryptography/
📚 完整API文档：https://gitee.com/liam-gitee/liam
```

## 学习建议

### 初学者路径
1. **从对称加密开始**：理解基础的加密/解密概念
2. **学习密钥管理**：了解密钥的安全生成和管理
3. **掌握哈希算法**：学习数据完整性验证
4. **探索数字签名**：理解身份验证和不可否认性

### 进阶用户路径
1. **现代加密算法**：学习ChaCha20-Poly1305和Ed25519
2. **流式处理**：掌握大文件的高效处理方法
3. **性能优化**：学习提高加密操作性能的技巧
4. **最佳实践**：掌握安全编程和常见陷阱避免

## 实际应用场景

### 🔐 数据保护
- **用户敏感信息加密**：使用AES-256保护用户数据
- **文件加密存储**：使用流式加密保护大文件
- **数据库字段加密**：保护数据库中的敏感字段

### 🔑 身份认证
- **密码安全存储**：使用Argon2哈希存储用户密码
- **API密钥管理**：安全生成和管理API密钥
- **数字证书**：使用RSA或ECDSA进行身份验证

### 📁 文件完整性
- **文件哈希验证**：使用SHA-256验证文件完整性
- **数字签名**：为文件添加数字签名防止篡改
- **版本控制**：验证软件包的完整性和来源

### 🌐 网络通信
- **HTTPS增强**：使用现代加密算法增强安全性
- **消息加密**：保护网络传输中的敏感消息
- **端到端加密**：实现客户端到服务器的端到端加密

## 性能参考

在现代硬件上的典型性能表现（基于示例程序实际测试）：

| 操作类型 | 算法 | 测试规模 | 性能表现 | 相对优势 |
|---------|------|----------|----------|----------|
| 对称加密 | AES-256 | 1000次/64字节 | ~22,000 ops/s | 传统标准 |
| 对称加密 | ChaCha20-Poly1305 | 1000次/64字节 | ~35,000 ops/s | 比AES快1.6x |
| 非对称加密 | RSA-2048 | 100次/64字节 | ~500 ops/s | 高兼容性 |
| 数字签名 | RSA-2048 | 100次/64字节 | ~200 ops/s | 广泛支持 |
| 数字签名 | ECDSA P-256 | 100次/64字节 | ~800 ops/s | 比RSA快4x |
| 哈希计算 | SHA-256 | 1000次/64字节 | ~50,000 ops/s | 通用标准 |
| 密码哈希 | Argon2 | 10次/64字节 | ~10 ops/s | 抗GPU攻击 |
| 流式加密 | AES-256 | 大文件(~500KB) | ~2MB/s | 内存高效 |
| 流式哈希 | SHA-256 | 大文件(~500KB) | ~5MB/s | 完整性验证 |

**性能测试环境说明：**
- 测试数据：64字符标准字符串
- 迭代次数：对称加密1000次，非对称加密100次，密码哈希10次
- 包含完整的加密+解密或签名+验证周期
- 实际性能取决于硬件配置、数据大小和具体使用场景

**算法选择建议：**
- **高性能场景**：ChaCha20-Poly1305 + Ed25519 + SHA-256
- **高兼容性场景**：AES-256 + RSA-2048 + SHA-256
- **密码存储**：Argon2（安全性优先，性能次要）

## 安全建议

### 🔒 密钥管理
- 使用256位密钥长度（AES-256）
- 每次加密使用新的随机IV
- 及时清理内存中的敏感数据
- 定期轮换密钥

### 🛡️ 算法选择
- **对称加密**：优先选择ChaCha20-Poly1305（AEAD）
- **非对称加密**：新项目使用Ed25519，兼容性要求使用RSA-2048+
- **密码哈希**：使用Argon2id替代PBKDF2
- **数据哈希**：使用SHA-256或更高强度算法

### ⚠️ 常见陷阱
- 不要硬编码密钥在源代码中
- 不要重用IV或Nonce
- 不要使用弱随机数生成器
- 不要忽略异常处理和错误恢复

## 技术支持

- **NuGet包**：https://www.nuget.org/packages/Liam.Cryptography/
- **源代码**：https://gitee.com/liam-gitee/liam
- **问题反馈**：请在Gitee上提交Issue
- **文档**：查看库的README.md获取详细API文档

## 许可证

本示例项目采用MIT许可证，与Liam.Cryptography库保持一致。

---

**开始探索Liam.Cryptography的强大功能，构建更安全的应用程序！** 🚀
