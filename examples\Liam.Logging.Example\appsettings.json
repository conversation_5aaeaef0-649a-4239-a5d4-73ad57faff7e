{"Logging": {"MinimumLevel": "Information", "EnableAsync": true, "AsyncQueueSize": 10000, "BatchSize": 100, "BatchTimeoutMs": 1000, "IncludeScopes": true, "IncludeExceptionDetails": true, "IncludeSourceInfo": false, "ApplicationName": "<PERSON>.Logging.Example", "Environment": "Development", "GlobalProperties": {"Application": "<PERSON>.Logging.Example", "Version": "1.0.0", "MachineName": "Development-PC"}, "Providers": [{"TypeName": "ConsoleLogProvider", "Enabled": true, "MinimumLevel": "Debug", "Settings": {"EnableColors": true, "UseStandardError": false, "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff", "FormatterType": "Text"}}, {"TypeName": "FileLogProvider", "Enabled": true, "MinimumLevel": "Information", "Settings": {"FilePath": "logs/app.log", "EnableRotation": true, "MaxFileSize": 10485760, "RetainedFileCount": 10, "RotationInterval": "1.00:00:00", "Encoding": "UTF-8", "AutoFlush": true, "BufferSize": 4096, "FormatterType": "Text"}}], "Filters": [{"Type": "Level", "Condition": "Debug", "Action": "Include", "Settings": {}}, {"Type": "Category", "Condition": "Microsoft.*", "Action": "Exclude", "Settings": {}}, {"Type": "Message", "Condition": "*performance*", "Action": "Include", "Settings": {}}]}, "ExampleSettings": {"PerformanceTestIterations": 10000, "ConcurrentThreads": 10, "TestDataSize": 1000, "EnableDetailedOutput": true, "SimulateErrors": true, "ErrorRate": 0.1}}